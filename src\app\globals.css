@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-color: #29ABE2;
  --secondary-color: #FFA500;
  --background-color: #F0F0F0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
}

/* YouTube Live style chat interface */
.chat-container {
  height: 100vh;
  width: 100vw;
  position: relative;
  overflow: hidden;
  background: #000;
}

.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
}

.video-background video {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: auto;
  height: 100%;
  max-width: 100%;
  object-fit: cover;
  object-position: center;
}

/* Responsive video sizing for 9:16 content */

/* Mobile portrait (most common for 9:16 videos) */
@media (orientation: portrait) and (max-width: 768px) {
  .video-background video {
    width: 100%;
    height: auto;
    min-height: 100vh;
  }
}

/* Mobile landscape */
@media (orientation: landscape) and (max-width: 768px) {
  .video-background video {
    width: auto;
    height: 100vh;
    max-width: calc(100vh * 9 / 16);
  }
}

/* Tablet and desktop */
@media (min-width: 769px) {
  .video-background video {
    width: auto;
    height: 100vh;
    max-width: calc(100vh * 9 / 16);
  }
}

/* Ultra-wide screens */
@media (min-width: 1920px) {
  .video-background video {
    width: auto;
    height: 100vh;
    max-width: calc(100vh * 9 / 16);
  }
}

/* For very narrow screens (narrower than 9:16) */
@media (max-aspect-ratio: 9/16) {
  .video-background video {
    width: 100vw;
    height: auto;
    min-height: 100vh;
  }
}

.chat-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  pointer-events: none;
  display: flex;
  flex-direction: column;
}

/* Adjust chat overlay for centered 9:16 video on wide screens */
@media (min-width: 769px) and (min-aspect-ratio: 16/9) {
  .chat-overlay {
    left: 50%;
    transform: translateX(-50%);
    width: calc(100vh * 9 / 16);
    max-width: 100vw;
  }
}

.chat-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.4), transparent);
  z-index: 15;
  pointer-events: auto;
}

.chat-messages-area {
  position: absolute;
  bottom: 80px;
  left: 0;
  right: 0;
  height: 50vh;
  max-height: 50vh;
  z-index: 12;
  pointer-events: none;
  overflow: hidden;
}

.chat-input-area {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.6), transparent);
  z-index: 15;
  pointer-events: auto;
}

/* YouTube Live style messages */
.live-chat-messages {
  height: 100%;
  overflow-y: auto;
  padding: 8px 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.live-chat-messages::-webkit-scrollbar {
  width: 4px;
}

.live-chat-messages::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.live-chat-messages::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.message-bubble {
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 8px 12px;
  margin: 2px 0;
  max-width: 90%;
  font-size: 14px;
  line-height: 1.4;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  animation: slideInFromBottom 0.3s ease-out;
  word-wrap: break-word;
}

.message-bubble.user {
  background: rgba(41, 171, 226, 0.5);
  color: white;
  align-self: flex-end;
  border: 1px solid rgba(41, 171, 226, 0.2);
}

.message-bubble.ai {
  background: rgba(0, 0, 0, 0.4);
  color: #ffffff;
  align-self: flex-start;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.message-bubble .sender-name {
  font-weight: 600;
  font-size: 12px;
  opacity: 0.9;
  margin-bottom: 2px;
}

.message-bubble .message-content {
  font-size: 14px;
  line-height: 1.3;
}

/* Animations */
@keyframes slideInFromBottom {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes heartbeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.heartbeat {
  animation: heartbeat 2s ease-in-out infinite;
}

/* Typing indicator styles */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 0;
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.6);
  animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingDot {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.4;
  }
  30% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

/* Animation delay utilities */
.animation-delay-100 {
  animation-delay: 0.1s;
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

/* Loading animation */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #999;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
  40% { transform: scale(1); opacity: 1; }
}
