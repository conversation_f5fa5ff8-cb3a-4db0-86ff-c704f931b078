import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// Admin authentication check
function isAdmin(request: NextRequest): boolean {
  const adminToken = request.headers.get('x-admin-token');
  const expectedToken = process.env.NEXT_PUBLIC_ADMIN_TOKEN || 'your-secure-admin-token-here-change-this';
  return adminToken === expectedToken;
}

// POST - Upload media file(s)
export async function POST(request: NextRequest) {
  try {
    if (!isAdmin(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const file = formData.get('file') as File;
    const characterId = formData.get('characterId') as string;
    const type = formData.get('type') as string;
    const setIndex = formData.get('setIndex') as string;

    // Handle single file upload (backward compatibility)
    if (file && characterId && type) {
      return await handleSingleFileUpload(file, characterId, type);
    }

    // Handle multiple files upload (video sets)
    if (files.length > 0 && characterId) {
      return await handleMultipleFilesUpload(files, characterId, setIndex);
    }

    return NextResponse.json(
      { success: false, error: 'File(s), character ID, and type are required' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error uploading file:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle single file upload
async function handleSingleFileUpload(file: File, characterId: string, type: string) {
  try {
    // Validate file type
    const allowedTypes = {
      profileImage: ['image/jpeg', 'image/png', 'image/jpg'],
      thumbnailImage: ['image/jpeg', 'image/png', 'image/jpg'],
      idleVideo: ['video/mp4', 'video/webm', 'video/mov'],
      speakingVideo: ['video/mp4', 'video/webm', 'video/mov'],
      listeningVideo: ['video/mp4', 'video/webm', 'video/mov'],
      normalVideo: ['video/mp4', 'video/webm', 'video/mov'],
      lipSyncVideo: ['video/mp4', 'video/webm', 'video/mov'],
      voiceSample: ['audio/mpeg', 'audio/mp3', 'audio/wav']
    };

    const typeKey = type as keyof typeof allowedTypes;
    if (!allowedTypes[typeKey] || !allowedTypes[typeKey].includes(file.type)) {
      return NextResponse.json(
        { success: false, error: `Invalid file type for ${type}` },
        { status: 400 }
      );
    }

    // Validate file size (in bytes)
    const maxSizes = {
      profileImage: 3 * 1024 * 1024, // 3MB
      thumbnailImage: 3 * 1024 * 1024, // 3MB
      idleVideo: 10 * 1024 * 1024, // 10MB
      speakingVideo: 10 * 1024 * 1024, // 10MB
      listeningVideo: 10 * 1024 * 1024, // 10MB
      normalVideo: 10 * 1024 * 1024, // 10MB
      lipSyncVideo: 10 * 1024 * 1024, // 10MB
      voiceSample: 2 * 1024 * 1024 // 2MB
    };

    if (file.size > maxSizes[typeKey]) {
      return NextResponse.json(
        { success: false, error: `File too large for ${type}. Max size: ${Math.round(maxSizes[typeKey] / 1024 / 1024)}MB` },
        { status: 400 }
      );
    }

    // Determine file extension and directory
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    let directory = '';
    let fileName = '';

    if (type.includes('Image')) {
      directory = 'images';
      fileName = `${characterId}-${type.replace('Image', '').toLowerCase()}.${fileExtension}`;
    } else if (type.includes('Video')) {
      directory = 'videos';
      fileName = `${characterId}-${type.replace('Video', '').toLowerCase()}.${fileExtension}`;
    } else if (type.includes('Sample')) {
      directory = 'audio';
      fileName = `${characterId}-voice.${fileExtension}`;
    }

    // Create directory if it doesn't exist
    const uploadDir = path.join(process.cwd(), 'public', 'characters', directory);
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // Save file
    const filePath = path.join(uploadDir, fileName);
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    fs.writeFileSync(filePath, buffer);

    // Return the public path
    const publicPath = `/characters/${directory}/${fileName}`;

    return NextResponse.json({
      success: true,
      filePath: publicPath,
      fileName,
      fileSize: file.size,
      message: 'File uploaded successfully'
    });
  } catch (error) {
    console.error('Error uploading single file:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle multiple files upload (video sets)
async function handleMultipleFilesUpload(files: File[], characterId: string, setIndex: string) {
  try {
    const uploadedFiles = [];
    const videoSetDir = path.join(process.cwd(), 'public', 'characters', 'video-sets', characterId);

    // Create directory if it doesn't exist
    if (!fs.existsSync(videoSetDir)) {
      fs.mkdirSync(videoSetDir, { recursive: true });
    }

    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      // Validate file type (videos only)
      if (!file.type.startsWith('video/')) {
        return NextResponse.json(
          { success: false, error: `File ${file.name} is not a video file` },
          { status: 400 }
        );
      }

      // Validate file size (10MB max)
      if (file.size > 10 * 1024 * 1024) {
        return NextResponse.json(
          { success: false, error: `File ${file.name} is too large. Max size: 10MB` },
          { status: 400 }
        );
      }

      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      const fileName = `${characterId}-set${setIndex || '1'}-${i === 0 ? 'normal' : 'lipsync'}.${fileExtension}`;
      const filePath = path.join(videoSetDir, fileName);

      const bytes = await file.arrayBuffer();
      const buffer = Buffer.from(bytes);
      fs.writeFileSync(filePath, buffer);

      const publicPath = `/characters/video-sets/${characterId}/${fileName}`;
      uploadedFiles.push({
        originalName: file.name,
        fileName,
        filePath: publicPath,
        fileSize: file.size,
        type: i === 0 ? 'normal' : 'lipsync'
      });
    }

    return NextResponse.json({
      success: true,
      uploadedFiles,
      setIndex: setIndex || '1',
      message: `Video set uploaded successfully (${uploadedFiles.length} files)`
    });
  } catch (error) {
    console.error('Error uploading multiple files:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
