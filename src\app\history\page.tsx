"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useI18n } from "@/contexts/I18nProvider";
import { getChatHistory, getCharacters } from "@/lib/firebaseService";
import { UIMessage } from "@/lib/firebaseService";
import { Character } from "@/types/firebase";

export default function HistoryPage() {
  const { user } = useAuth();
  const { t } = useI18n();
  const [messages, setMessages] = useState<UIMessage[]>([]);
  const [characters, setCharacters] = useState<Character[]>([]);
  const [selectedCharacter, setSelectedCharacter] = useState<string>('all');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      if (!user) return;
      
      try {
        // Fetch characters first
        const charactersData = await getCharacters();
        setCharacters(charactersData);

        // Fetch messages for all characters or selected character
        let allMessages: UIMessage[] = [];
        
        if (selectedCharacter === 'all') {
          // Fetch messages for all characters
          for (const character of charactersData) {
            try {
              const characterMessages = await getChatHistory(user.uid, character.id);
              allMessages = [...allMessages, ...characterMessages];
            } catch (error) {
              console.error(`Error fetching messages for character ${character.id}:`, error);
            }
          }
        } else {
          // Fetch messages for selected character
          allMessages = await getChatHistory(user.uid, selectedCharacter);
        }

        // Sort messages by timestamp (newest first)
        allMessages.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
        setMessages(allMessages);
      } catch (error) {
        console.error('Error fetching chat history:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user, selectedCharacter]);

  const getCharacterName = (characterId: string) => {
    const character = characters.find(c => c.id === characterId);
    return character?.name?.en || 'Unknown Character';
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="bg-white/10 backdrop-blur-md rounded-xl p-8 text-center">
          <h2 className="text-2xl font-semibold text-white mb-4">
            {t('history.notLoggedIn', 'Please log in to view your chat history')}
          </h2>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-white mb-4">
              {t('history.title', 'Chat History')}
            </h1>
          </div>

          <div className="mb-6">
            <label className="block text-white font-semibold mb-2">
              {t('history.filterByCharacter', 'Filter by Character')}
            </label>
            <select
              value={selectedCharacter}
              onChange={(e) => setSelectedCharacter(e.target.value)}
              className="w-full md:w-auto px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="all">{t('history.allCharacters', 'All Characters')}</option>
              {characters.map((character) => (
                <option key={character.id} value={character.id}>
                  {character.name?.en || character.id}
                </option>
              ))}
            </select>
          </div>

          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6">
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto"></div>
                <p className="text-white mt-4">{t('history.loading', 'Loading chat history...')}</p>
              </div>
            ) : messages.length > 0 ? (
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`p-4 rounded-lg ${
                      message.sender === 'user'
                        ? 'bg-purple-600/50 ml-8'
                        : 'bg-blue-600/50 mr-8'
                    }`}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <span className="font-semibold text-white">
                        {message.sender === 'user' 
                          ? t('history.you', 'You') 
                          : getCharacterName(message.characterId || '')
                        }
                      </span>
                      <span className="text-gray-300 text-sm">
                        {message.timestamp.toLocaleString()}
                      </span>
                    </div>
                    <p className="text-gray-100">{message.content}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-300 text-lg">
                  {t('history.noMessages', 'No chat history found')}
                </p>
                <p className="text-gray-400 mt-2">
                  {t('history.startChatting', 'Start chatting with AI characters to see your history here!')}
                </p>
              </div>
            )}
          </div>

          {messages.length > 0 && (
            <div className="mt-6 text-center">
              <p className="text-gray-300">
                {t('history.totalMessages', 'Total messages')}: {messages.length}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
