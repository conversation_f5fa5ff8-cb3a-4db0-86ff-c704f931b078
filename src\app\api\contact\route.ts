import { NextRequest, NextResponse } from 'next/server';
import { getSecret } from '@/lib/secretManager';
import nodemailer from 'nodemailer';

export const dynamic = 'force-dynamic';

interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export async function POST(request: NextRequest) {
  console.log('=== Contact API Called ===');
  try {
    const body = await request.text();
    console.log('Raw request body:', body);

    const { name, email, subject, message }: ContactFormData = JSON.parse(body);

    // Validate required fields
    if (!name || !email || !subject || !message) {
      return NextResponse.json(
        {
          status: 'error',
          message: 'すべての項目を入力してください。'
        },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        {
          status: 'error',
          message: '正しいメールアドレスを入力してください。'
        },
        { status: 400 }
      );
    }

    console.log('=== Contact Form Submission ===');
    console.log('Name:', name);
    console.log('Email:', email);
    console.log('Subject:', subject);
    console.log('Message length:', message.length);

    // Send email using Brevo SMTP
    const emailSent = await sendContactEmail({
      name,
      email,
      subject,
      message
    });

    if (emailSent) {
      console.log('Contact email sent successfully');
      return NextResponse.json({
        status: 'success',
        message: 'メッセージが送信されました。'
      });
    } else {
      console.error('Failed to send contact email');
      return NextResponse.json(
        {
          status: 'error',
          message: 'メッセージの送信に失敗しました。'
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Contact form API error:', error);

    // Log detailed error information for debugging
    if (error instanceof Error) {
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    }

    return NextResponse.json(
      {
        status: 'error',
        message: 'メッセージの送信に失敗しました。'
      },
      { status: 500 }
    );
  }
}

async function sendContactEmail(data: ContactFormData): Promise<boolean> {
  try {
    console.log('=== Getting SMTP credentials ===');
    // Get Brevo SMTP credentials from Secret Manager
    const smtpHost = await getSecret('SMTP_HOST');
    const smtpPort = await getSecret('SMTP_PORT');
    const smtpUser = await getSecret('SMTP_USER');
    const smtpPassword = await getSecret('SMTP_PASSWORD');
    const senderEmail = await getSecret('FORM_SENDER_EMAIL');
    const recipientEmail = await getSecret('FORM_RECIPIENT_EMAIL');

    console.log('SMTP credentials check:', {
      smtpHost: smtpHost ? `${smtpHost.substring(0, 10)}...` : 'NOT_FOUND',
      smtpPort: smtpPort || 'NOT_FOUND',
      smtpUser: smtpUser ? `${smtpUser.substring(0, 5)}...` : 'NOT_FOUND',
      smtpPassword: smtpPassword ? 'FOUND' : 'NOT_FOUND',
      senderEmail: senderEmail || 'NOT_FOUND',
      recipientEmail: recipientEmail || 'NOT_FOUND'
    });

    if (!smtpHost || !smtpPort || !smtpUser || !smtpPassword || !senderEmail || !recipientEmail) {
      console.error('Brevo SMTP credentials not found in Secret Manager');
      console.error('Missing credentials:', {
        smtpHost: !!smtpHost,
        smtpPort: !!smtpPort,
        smtpUser: !!smtpUser,
        smtpPassword: !!smtpPassword,
        senderEmail: !!senderEmail,
        recipientEmail: !!recipientEmail
      });
      return false;
    }

    // Development mode: Skip actual email sending if using test credentials
    if (process.env.NODE_ENV === 'development' && smtpUser === '<EMAIL>') {
      console.log('=== DEVELOPMENT MODE: Simulating email send ===');
      console.log('Email would be sent with the following details:');
      console.log('From:', senderEmail);
      console.log('To:', recipientEmail);
      console.log('Reply-To:', data.email);
      console.log('Subject:', `お問い合わせがありました：${data.subject}`);
      console.log('Content:', {
        name: data.name,
        email: data.email,
        subject: data.subject,
        message: data.message.substring(0, 100) + (data.message.length > 100 ? '...' : '')
      });
      console.log('=== Email simulation completed successfully ===');
      return true;
    }

    // Use nodemailer with Brevo SMTP
    const transporter = nodemailer.createTransport({
      host: smtpHost,
      port: parseInt(smtpPort),
      secure: false, // Use STARTTLS for port 587
      requireTLS: true, // Force TLS
      auth: {
        user: smtpUser,
        pass: smtpPassword
      },
      tls: {
        rejectUnauthorized: true
      },
      connectionTimeout: 10000, // 10 seconds
      greetingTimeout: 5000, // 5 seconds
      socketTimeout: 10000 // 10 seconds
    });

    const mailOptions = {
      from: senderEmail,
      to: recipientEmail,
      replyTo: data.email,
      subject: `お問い合わせがありました：${data.subject}`,
      html: `
        <h2>新しいお問い合わせ</h2>
        <table style="border-collapse: collapse; width: 100%; max-width: 600px;">
          <tr>
            <td style="border: 1px solid #ddd; padding: 12px; background-color: #f9f9f9; font-weight: bold; width: 120px;">お名前</td>
            <td style="border: 1px solid #ddd; padding: 12px;">${data.name}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 12px; background-color: #f9f9f9; font-weight: bold;">メールアドレス</td>
            <td style="border: 1px solid #ddd; padding: 12px;">${data.email}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 12px; background-color: #f9f9f9; font-weight: bold;">件名</td>
            <td style="border: 1px solid #ddd; padding: 12px;">${data.subject}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 12px; background-color: #f9f9f9; font-weight: bold; vertical-align: top;">メッセージ</td>
            <td style="border: 1px solid #ddd; padding: 12px;">
              <div style="white-space: pre-wrap;">${data.message}</div>
            </td>
          </tr>
        </table>
        <hr style="margin: 20px 0;">
        <p style="color: #666; font-size: 12px;">
          このメッセージはAiLuvChatのお問い合わせフォームから送信されました。<br>
          送信日時: ${new Date().toLocaleString('ja-JP', { timeZone: 'Asia/Tokyo' })}
        </p>
      `
    };

    // Verify SMTP connection before sending
    await transporter.verify();
    console.log('SMTP connection verified successfully');

    await transporter.sendMail(mailOptions);
    console.log('Email sent successfully via Brevo SMTP');
    console.log('Email details:', {
      from: senderEmail,
      to: recipientEmail,
      subject: mailOptions.subject,
      replyTo: data.email
    });
    return true;

  } catch (error) {
    console.error('Error sending email via Brevo SMTP:', error);

    // Log specific error types for debugging
    if (error instanceof Error) {
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      if ('code' in error) {
        console.error('Error code:', error.code);
      }
    }

    return false;
  }
}
