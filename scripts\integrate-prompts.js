/**
 * プロンプト統合スクリプト
 * 個別プロンプトファイルをcharacters.jsonに統合
 */

const fs = require('fs');
const path = require('path');

// ファイルパス定義
const DATA_DIR = path.join(process.cwd(), 'data');
const CHARACTERS_FILE = path.join(DATA_DIR, 'characters.json');
const PROMPTS_DIR = path.join(DATA_DIR, 'prompts');

function integratePrompts() {
  try {
    console.log('🔄 プロンプト統合を開始...');
    
    // 現在のキャラクターデータを読み込み
    if (!fs.existsSync(CHARACTERS_FILE)) {
      console.error('❌ characters.jsonが見つかりません');
      return false;
    }
    
    const characters = JSON.parse(fs.readFileSync(CHARACTERS_FILE, 'utf-8'));
    console.log(`📋 ${Object.keys(characters).length}個のキャラクターを処理中...`);
    
    // 各キャラクターにプロンプトを統合
    for (const characterId in characters) {
      const character = characters[characterId];
      
      // systemPromptフィールドを初期化
      if (!character.systemPrompt) {
        character.systemPrompt = { en: '', ja: '' };
      }
      
      let promptsAdded = 0;
      
      // 英語プロンプトを読み込み
      const enPromptFile = path.join(PROMPTS_DIR, `${characterId}-en.txt`);
      if (fs.existsSync(enPromptFile)) {
        character.systemPrompt.en = fs.readFileSync(enPromptFile, 'utf-8').trim();
        console.log(`  ✅ ${characterId}: EN prompt (${character.systemPrompt.en.length} chars)`);
        promptsAdded++;
      }
      
      // 日本語プロンプトを読み込み
      const jaPromptFile = path.join(PROMPTS_DIR, `${characterId}-ja.txt`);
      if (fs.existsSync(jaPromptFile)) {
        character.systemPrompt.ja = fs.readFileSync(jaPromptFile, 'utf-8').trim();
        console.log(`  ✅ ${characterId}: JA prompt (${character.systemPrompt.ja.length} chars)`);
        promptsAdded++;
      }
      
      // レガシーファイルも確認
      const legacyPromptFile = path.join(PROMPTS_DIR, `${characterId}.txt`);
      if (fs.existsSync(legacyPromptFile) && !character.systemPrompt.en && !character.systemPrompt.ja) {
        const legacyPrompt = fs.readFileSync(legacyPromptFile, 'utf-8').trim();
        character.systemPrompt.en = legacyPrompt;
        character.systemPrompt.ja = legacyPrompt;
        console.log(`  ✅ ${characterId}: Legacy prompt (${legacyPrompt.length} chars)`);
        promptsAdded++;
      }
      
      if (promptsAdded === 0) {
        console.log(`  ⚠️  ${characterId}: プロンプトファイルが見つかりません`);
      }
    }
    
    // 統合されたデータを保存
    fs.writeFileSync(CHARACTERS_FILE, JSON.stringify(characters, null, 2));
    console.log('💾 characters.jsonを更新しました');
    
    console.log('🎯 プロンプト統合完了');
    return true;
  } catch (error) {
    console.error('❌ プロンプト統合エラー:', error);
    return false;
  }
}

// スクリプト実行
if (require.main === module) {
  const success = integratePrompts();
  process.exit(success ? 0 : 1);
}

module.exports = { integratePrompts };
