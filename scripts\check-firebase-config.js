#!/usr/bin/env node

/**
 * Firebase Configuration Checker
 * This script helps verify Firebase configuration for Google Authentication
 */

const https = require('https');

// Firebase configuration from environment
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

console.log('🔥 Firebase Configuration Check');
console.log('================================');

// Check environment variables
console.log('\n📋 Environment Variables:');
Object.entries(firebaseConfig).forEach(([key, value]) => {
  console.log(`${key}: ${value ? '✅ Set' : '❌ Missing'}`);
});

// Check if all required variables are present
const missingVars = Object.entries(firebaseConfig)
  .filter(([key, value]) => !value)
  .map(([key]) => key);

if (missingVars.length > 0) {
  console.log('\n❌ Missing environment variables:');
  missingVars.forEach(varName => console.log(`  - ${varName}`));
  console.log('\nPlease add these to your .env.local file');
  process.exit(1);
}

console.log('\n✅ All environment variables are set');

// Check Firebase project accessibility
console.log('\n🌐 Checking Firebase project accessibility...');

const checkFirebaseProject = () => {
  return new Promise((resolve, reject) => {
    const url = `https://${firebaseConfig.projectId}.firebaseapp.com`;
    
    https.get(url, (res) => {
      console.log(`Firebase project URL: ${url}`);
      console.log(`Status: ${res.statusCode}`);
      
      if (res.statusCode === 200 || res.statusCode === 404) {
        console.log('✅ Firebase project is accessible');
        resolve();
      } else {
        console.log('⚠️  Unexpected status code');
        resolve();
      }
    }).on('error', (err) => {
      console.log('❌ Error accessing Firebase project:', err.message);
      reject(err);
    });
  });
};

// Check Google OAuth configuration
const checkGoogleOAuth = () => {
  console.log('\n🔐 Google OAuth Configuration:');
  console.log('Please verify the following in Google Cloud Console:');
  console.log('1. Go to: https://console.cloud.google.com/');
  console.log(`2. Select project: ${firebaseConfig.projectId}`);
  console.log('3. Navigate to: APIs & Services > Credentials');
  console.log('4. Find your OAuth 2.0 Client ID');
  console.log('5. Verify these authorized origins:');
  console.log('   - http://localhost:3000');
  console.log('   - https://localhost:3000');
  console.log('   - https://ailuvchat.web.app');
  console.log('   - https://ailuvchat.firebaseapp.com');
  console.log('6. Verify these redirect URIs:');
  console.log('   - http://localhost:3000/__/auth/handler');
  console.log('   - https://localhost:3000/__/auth/handler');
  console.log('   - https://ailuvchat.web.app/__/auth/handler');
  console.log('   - https://ailuvchat.firebaseapp.com/__/auth/handler');
};

// Check Firebase Authentication settings
const checkFirebaseAuth = () => {
  console.log('\n🔑 Firebase Authentication Configuration:');
  console.log('Please verify the following in Firebase Console:');
  console.log('1. Go to: https://console.firebase.google.com/');
  console.log(`2. Select project: ${firebaseConfig.projectId}`);
  console.log('3. Navigate to: Authentication > Sign-in method');
  console.log('4. Ensure Google provider is enabled');
  console.log('5. Navigate to: Authentication > Settings');
  console.log('6. Verify these authorized domains:');
  console.log('   - localhost');
  console.log('   - 127.0.0.1');
  console.log('   - ailuvchat.web.app');
  console.log('   - ailuvchat.firebaseapp.com');
};

// Run checks
async function runChecks() {
  try {
    await checkFirebaseProject();
    checkGoogleOAuth();
    checkFirebaseAuth();
    
    console.log('\n🎉 Configuration check completed!');
    console.log('\nIf Google Sign-in still fails, please:');
    console.log('1. Verify the manual configurations above');
    console.log('2. Clear browser cache and cookies');
    console.log('3. Try in an incognito/private window');
    console.log('4. Check browser console for additional errors');
    
  } catch (error) {
    console.error('\n❌ Configuration check failed:', error.message);
    process.exit(1);
  }
}

runChecks();
