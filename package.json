{"name": "ailuv<PERSON>hat", "version": "0.1.0", "private": true, "engines": {"node": ">=18.0.0 <=20.x"}, "scripts": {"dev": "next dev", "dev:emulator": "npm run emulator:start & npm run dev:with-emulator", "dev:with-emulator": "cross-env USE_FIREBASE_EMULATOR=true next dev", "dev:emulator-mode": "next dev", "build": "next build", "start": "next start", "lint": "next lint --dir src", "type-check": "tsc --noEmit", "build:functions": "cd functions && npm run build", "deploy:functions": "firebase deploy --only functions", "deploy:hosting": "firebase deploy --only hosting", "emulator:start": "firebase emulators:start --only auth,firestore,storage,functions", "emulator:start:ui": "firebase emulators:start --only auth,firestore,storage,functions,ui", "emulator:export": "firebase emulators:export ./firebase-export", "emulator:import": "firebase emulators:start --import=./firebase-export", "sync-production": "node scripts/sync-production-data.js", "sync-characters": "node scripts/sync-characters.js sync", "validate-characters": "node scripts/sync-characters.js validate", "list-characters": "node scripts/sync-characters.js list", "build-production": "npm run sync-characters && npm run build", "warmup": "node scripts/warmup-service.js once", "warmup:comprehensive": "node scripts/warmup-service.js comprehensive", "warmup:service": "node scripts/warmup-service.js start"}, "dependencies": {"@genkit-ai/firebase": "^1.10.0", "@genkit-ai/googleai": "^1.10.0", "@google-cloud/firestore": "^7.11.1", "@google-cloud/functions-framework": "^4.0.0", "@google-cloud/secret-manager": "^6.0.1", "@google-cloud/storage": "^7.16.0", "@stripe/stripe-js": "^7.3.1", "@types/hls.js": "^0.13.3", "autoprefixer": "^10.4.21", "elevenlabs-node": "^2.0.3", "firebase": "^11.8.1", "firebase-admin": "^13.4.0", "genkit": "^1.10.0", "hls.js": "^1.6.2", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "next": "^15.3.3", "nodemailer": "^6.9.15", "openai": "^4.103.0", "react": "^18", "react-dom": "^18", "react-i18next": "^15.5.2", "stripe": "^18.1.1"}, "devDependencies": {"@types/node": "^20.17.57", "@types/nodemailer": "^6.4.17", "@types/react": "^18", "@types/react-dom": "^18", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "15.1.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}