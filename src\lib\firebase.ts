// Debug logging only in development
const isDev = process.env.NODE_ENV === 'development';
if (isDev) {
  console.log('🔥 Firebase module loading started...');
}

import { initializeApp, getApps } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getStorage, connectStorageEmulator } from 'firebase/storage';
import { getFunctions, connectFunctionsEmulator } from 'firebase/functions';

if (isDev) {
  console.log('🔥 Firebase imports completed...');
}

// Get Firebase config from environment variables or FIREBASE_WEBAPP_CONFIG
const getFirebaseConfig = () => {
  // Debug: Log environment variables in development only
  if (isDev) {
    console.log('Firebase config debug:', {
      hasWebappConfig: !!process.env.FIREBASE_WEBAPP_CONFIG,
      hasApiKey: !!process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
      nodeEnv: process.env.NODE_ENV,
      isClient: typeof window !== 'undefined'
    });
  }

  // Try to get from FIREBASE_WEBAPP_CONFIG first (Firebase App Hosting)
  if (process.env.FIREBASE_WEBAPP_CONFIG) {
    try {
      const config = JSON.parse(process.env.FIREBASE_WEBAPP_CONFIG);
      console.log('Using FIREBASE_WEBAPP_CONFIG:', { ...config, apiKey: config.apiKey ? '[REDACTED]' : 'missing' });
      return config;
    } catch (error) {
      console.error('Failed to parse FIREBASE_WEBAPP_CONFIG:', error);
    }
  }

  // Fallback to individual environment variables
  const config = {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  };

  console.log('Using individual env vars:', {
    ...config,
    apiKey: config.apiKey ? '[REDACTED]' : 'missing'
  });

  // Final fallback to known Firebase App Hosting config
  if (!config.apiKey && typeof window !== 'undefined') {
    console.log('Using Firebase App Hosting fallback config');
    return {
      apiKey: "AIzaSyAtlYMWef3r3yG9asba8ZkIWSEgwJ8kTSg",
      authDomain: "ailuvchat.firebaseapp.com",
      projectId: "ailuvchat",
      storageBucket: "ailuvchat.firebasestorage.app",
      messagingSenderId: "75473504940",
      appId: "1:75473504940:web:9b73c394ec3f7559176d09"
    };
  }

  return config;
};

const firebaseConfig = getFirebaseConfig();

// Initialize Firebase only if config is valid
let app = null;
try {
  if (isDev) console.log('🔥 About to initialize Firebase app...');
  if (firebaseConfig.apiKey) {
    if (getApps().length === 0) {
      if (isDev) console.log('🔥 Initializing Firebase app...');
      app = initializeApp(firebaseConfig);
      if (isDev) console.log('🔥 Firebase app initialized successfully');
    } else {
      if (isDev) console.log('🔥 Using existing Firebase app');
      app = getApps()[0];
    }
  } else {
    console.warn('Firebase API key not found, Firebase will not be initialized');
  }
} catch (error) {
  console.error('Failed to initialize Firebase:', error);
  app = null;
}

// Initialize Firebase services only if app is available
if (isDev) console.log('🔥 About to initialize Firebase services...');
export const auth = app ? getAuth(app) : null;
if (isDev) console.log('🔥 Auth service initialized');
export const db = app ? getFirestore(app) : null;
if (isDev) console.log('🔥 Firestore service initialized');
export const storage = app ? getStorage(app) : null;
if (isDev) console.log('🔥 Storage service initialized');
export const functions = app ? getFunctions(app) : null;
if (isDev) console.log('🔥 Functions service initialized');

// Debug: Log service initialization status in development only
if (isDev) {
  console.log('Firebase services initialized:', {
    app: !!app,
    auth: !!auth,
    db: !!db,
    storage: !!storage,
    functions: !!functions
  });
}

// Connect to emulators in development ONLY when explicitly enabled AND in development mode
const shouldUseEmulator = process.env.NODE_ENV === 'development' &&
  typeof window !== 'undefined' && // Only in browser environment
  (process.env.USE_FIREBASE_EMULATOR === 'true' || process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATOR === 'true') &&
  window.location.hostname === 'localhost'; // Only on localhost

if (isDev) {
  console.log('🔥 Emulator connection check:', {
    shouldUseEmulator,
    hasFirebaseServices: !!(app && auth && db && functions && storage),
    isClient: typeof window !== 'undefined'
  });
}

if (shouldUseEmulator && app && auth && db && functions && storage) {
  if (typeof window !== 'undefined') {
    // Only run emulator connections on client side
    try {
      if (isDev) console.log('🔥 Attempting to connect to Firebase emulators...');

      // Auth emulator - Simple connection without checking existing state
      try {
        connectAuthEmulator(auth, 'http://localhost:9098');
        if (isDev) console.log('✅ Connected to Auth emulator');
      } catch (authError) {
        if (isDev) console.log('✅ Auth emulator already connected or connection failed');
      }

      // Firestore emulator - Simple connection without checking existing state
      try {
        connectFirestoreEmulator(db, 'localhost', 8084);
        if (isDev) console.log('✅ Connected to Firestore emulator');
      } catch (firestoreError) {
        if (isDev) console.log('✅ Firestore emulator already connected or connection failed');
      }

      // Storage emulator - Simple connection without checking existing state
      try {
        connectStorageEmulator(storage, 'localhost', 9198);
        if (isDev) console.log('✅ Connected to Storage emulator');
      } catch (storageError) {
        if (isDev) console.log('✅ Storage emulator already connected or connection failed');
      }

      // Functions emulator - Simple connection without checking existing state
      try {
        connectFunctionsEmulator(functions, 'localhost', 5001);
        if (isDev) console.log('✅ Connected to Functions emulator');
      } catch (functionsError) {
        if (isDev) console.log('✅ Functions emulator already connected or connection failed');
      }

      if (isDev) console.log('🎉 Firebase emulators connected successfully');
    } catch (error) {
      console.error('⚠️ Firebase emulators connection error:', error);
    }
  }
} else if (isDev) {
  console.log('🚨 Firebase emulator connections disabled - using production Firebase');
  console.log('🚨 Reason:', {
    shouldUseEmulator,
    hasApp: !!app,
    hasAuth: !!auth,
    hasDb: !!db,
    hasFunctions: !!functions,
    hasStorage: !!storage
  });
}

if (isDev) {
  console.log('🔥 Firebase initialization script completed');
  if (typeof window !== 'undefined') {
    console.log('🔥 Firebase client-side initialization complete');
  }
}

export default app;
