/**
 * 記憶システム統合ヘルパー
 * 既存のAPIと記憶システムを連携させるためのユーティリティ
 */

import { db } from '@/lib/firebase';
import { collection, query, where, orderBy, limit, getDocs } from 'firebase/firestore';

export interface UserSubscriptionInfo {
  subscriptionTier: string;
  isPaidUser: boolean;
  email: string;
  displayName?: string;
}

export interface ShortTermMessage {
  content: string;
  sender: 'user' | 'ai';
  createdAt: any;
}

/**
 * ユーザーの課金状態確認
 */
export async function checkUserSubscription(userId: string): Promise<UserSubscriptionInfo> {
  try {
    const userDoc = await db.collection('users').doc(userId).get();
    
    if (!userDoc.exists) {
      return {
        subscriptionTier: 'free',
        isPaidUser: false,
        email: ''
      };
    }
    
    const userData = userDoc.data();
    const subscriptionTier = userData?.subscriptionTier || 'free';
    const isPaidUser = subscriptionTier !== 'free';
    
    return {
      subscriptionTier,
      isPaidUser,
      email: userData?.email || '',
      displayName: userData?.displayName
    };
  } catch (error) {
    console.error('Error checking user subscription:', error);
    return {
      subscriptionTier: 'free',
      isPaidUser: false,
      email: ''
    };
  }
}

/**
 * 短期記憶（最近の会話）取得
 */
export async function getShortTermMemories(
  userId: string,
  characterId: string,
  limitCount: number = 10
): Promise<ShortTermMessage[]> {
  try {
    const messagesRef = collection(db, 'messages');
    const q = query(
      messagesRef,
      where('userId', '==', userId),
      where('characterId', '==', characterId),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );
    
    const snapshot = await getDocs(q);
    
    const messages = snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        content: data.content,
        sender: data.sender,
        createdAt: data.createdAt
      };
    });
    
    // 時系列順に並び替え（古い順）
    return messages.reverse();
  } catch (error) {
    console.error('Error getting short-term memories:', error);
    return [];
  }
}

/**
 * 長期記憶検索（有料ユーザー限定）
 * Cloud Functionsを呼び出して検索
 */
export async function searchLongTermMemories(
  userId: string,
  characterId: string,
  query: string
): Promise<any[]> {
  try {
    // Cloud Functionsの記憶検索エンドポイントを呼び出し
    const response = await fetch('/api/memory/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        characterId,
        query
      })
    });
    
    if (!response.ok) {
      throw new Error(`Memory search failed: ${response.status}`);
    }
    
    const data = await response.json();
    return data.memories || [];
  } catch (error) {
    console.error('Error searching long-term memories:', error);
    return [];
  }
}

/**
 * 記憶統合チャット応答生成
 */
export async function generateMemoryEnhancedResponse(
  userId: string,
  characterId: string,
  message: string,
  language: 'ja' | 'en' = 'ja'
): Promise<{
  success: boolean;
  response?: string;
  memoryStats?: any;
  error?: string;
}> {
  try {
    // 1. ユーザーの課金状態確認
    const userInfo = await checkUserSubscription(userId);
    
    // 2. キャラクター情報取得
    const character = CharacterService.getCharacter(characterId);
    if (!character) {
      throw new Error(`Character not found: ${characterId}`);
    }
    
    // 3. 短期記憶取得（全ユーザー共通）
    const shortTermMemories = await getShortTermMemories(userId, characterId);
    
    let longTermMemories: any[] = [];
    
    // 4. 長期記憶検索（有料ユーザー限定）
    if (userInfo.isPaidUser) {
      longTermMemories = await searchLongTermMemories(userId, characterId, message);
    }
    
    // 5. プロンプト生成
    const promptContext: PromptContext = {
      character,
      currentMessage: message,
      language,
      shortTermMemories,
      longTermMemories: userInfo.isPaidUser ? longTermMemories : undefined,
      isPaidUser: userInfo.isPaidUser,
      userName: userInfo.displayName
    };
    
    const { prompt, truncated } = MemoryPromptService.optimizePrompt(promptContext);
    
    // 6. AI応答生成（既存のロジックを使用）
    const aiResponse = generateAIResponse(message, characterId, language, prompt);
    
    // 7. 記憶統計
    const memoryStats = MemoryPromptService.generateMemoryStats(promptContext);
    
    return {
      success: true,
      response: aiResponse,
      memoryStats: {
        ...memoryStats,
        promptTruncated: truncated,
        subscriptionTier: userInfo.subscriptionTier
      }
    };
    
  } catch (error) {
    console.error('Error generating memory-enhanced response:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * 既存のAI応答生成関数（記憶システム対応版）
 */
function generateAIResponse(
  message: string,
  characterId: string,
  language: string,
  customPrompt?: string
): string {
  // カスタムプロンプトがある場合はそれを使用
  if (customPrompt) {
    // TODO: 実際のAI APIを呼び出してカスタムプロンプトで応答生成
    console.log('Using memory-enhanced prompt for AI generation');
  }
  
  // 既存のロジックをフォールバック
  const responses = language === 'ja' ? {
    naomi: [
      "それは本当に興味深いですね！もっと詳しく教えてください。",
      "あなたの気持ちがわかります。私はここで聞いています。",
      "それは素晴らしいですね！もっと聞きたいです。",
      "あなたはとても思いやりがありますね。シェアしてくれてありがとうございます。",
      "それは良い質問ですね！ちょっと考えさせてください..."
    ],
    jhon: [
      "クールですね！そのゲームについてもっと教えてください。",
      "僕もそのゲーム好きです！一緒にプレイしませんか？",
      "面白そうですね！どんなストラテジーを使いますか？",
      "それは挑戦的ですね！頑張って！",
      "ゲームの世界は無限の可能性がありますね。"
    ],
    jake: [
      "それは魔法のように美しいですね！",
      "星々があなたに微笑んでいるようです。",
      "あなたの心の中に素晴らしい光を感じます。",
      "神秘的な力があなたを導いているようですね。",
      "この瞬間は特別な意味があるかもしれません。"
    ],
    ava: [
      "自然の力を感じますね！",
      "地球があなたに語りかけているようです。",
      "バランスが大切ですね。心と体の調和を保ちましょう。",
      "あなたの内なる平和が美しく輝いています。",
      "今この瞬間を大切にしましょう。"
    ]
  } : {
    naomi: [
      "That's really interesting! Tell me more about that.",
      "I understand how you feel. I'm here to listen.",
      "That sounds wonderful! I'd love to hear more.",
      "You're so thoughtful. I appreciate you sharing that with me.",
      "That's a great question! Let me think about that..."
    ],
    jhon: [
      "Cool! Tell me more about that game.",
      "I love that game too! Want to play together sometime?",
      "Sounds interesting! What strategy do you use?",
      "That's challenging! Good luck with that!",
      "Gaming worlds have infinite possibilities, don't they?"
    ],
    jake: [
      "That's magically beautiful!",
      "The stars seem to be smiling upon you.",
      "I sense a wonderful light within your heart.",
      "It seems like mystical forces are guiding you.",
      "This moment might have special meaning."
    ],
    ava: [
      "I can feel the power of nature!",
      "The earth seems to be speaking to you.",
      "Balance is important. Let's maintain harmony between mind and body.",
      "Your inner peace shines beautifully.",
      "Let's cherish this moment."
    ]
  };

  const characterResponses = responses[characterId as keyof typeof responses] || responses.naomi;
  return characterResponses[Math.floor(Math.random() * characterResponses.length)];
}
