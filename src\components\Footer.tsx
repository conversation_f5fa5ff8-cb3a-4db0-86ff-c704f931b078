"use client";

import Link from "next/link";
import { useI18n } from "@/contexts/I18nProvider";
import { usePathname } from "next/navigation";

export default function Footer() {
  const { t, language } = useI18n();
  const pathname = usePathname();

  // SSR-safe language detection
  const isJapanese = pathname.startsWith('/ja');

  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Logo & Description */}
          <div className="space-y-4">
            <h3 className="text-2xl font-bold text-primary">AiLuvChat</h3>
            <p className="text-gray-300 text-sm">
              {isJapanese
                ? 'AIキャラクターとの革新的なチャット体験'
                : 'Interactive AI character chat experience'
              }
            </p>
          </div>

          {/* Navigation Links */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">
              {isJapanese ? 'ナビゲーション' : 'Navigation'}
            </h4>
            <nav className="flex flex-col space-y-2">
              <Link
                href={isJapanese ? '/ja' : '/'}
                className="text-gray-300 hover:text-white transition-colors text-sm"
              >
                {isJapanese ? 'ホーム' : 'Home'}
              </Link>
              <Link
                href="/about"
                className="text-gray-300 hover:text-white transition-colors text-sm"
              >
                {isJapanese ? 'について' : 'About'}
              </Link>
              <Link
                href="/contact"
                className="text-gray-300 hover:text-white transition-colors text-sm"
              >
                {isJapanese ? 'お問い合わせ' : 'Contact'}
              </Link>
              <Link
                href="/privacy-policy"
                className="text-gray-300 hover:text-white transition-colors text-sm"
              >
                {isJapanese ? 'プライバシーポリシー' : 'Privacy Policy'}
              </Link>
              <Link
                href="/terms"
                className="text-gray-300 hover:text-white transition-colors text-sm"
              >
                {isJapanese ? '利用規約' : 'Terms of Service'}
              </Link>
              {isJapanese && (
                <Link
                  href="/ja/tokusho"
                  className="text-gray-300 hover:text-white transition-colors text-sm"
                >
                  特定商取引法に基づく表記
                </Link>
              )}
            </nav>
          </div>

          {/* Additional Info */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">
              {isJapanese ? 'サポート' : 'Support'}
            </h4>
            <p className="text-gray-300 text-sm">
              {isJapanese
                ? 'お問い合わせはContactページからお送りください'
                : 'Please use the Contact page for inquiries'
              }
            </p>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 mt-8 pt-6">
          <div className="text-center">
            <p className="text-gray-400 text-sm">
              © 2025 AiLuvChat. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
