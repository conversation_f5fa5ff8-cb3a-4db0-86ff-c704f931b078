// Voice optimization management API
import { NextRequest, NextResponse } from 'next/server';
import { 
  getOptimizationStatus, 
  logPerformanceMetrics, 
  warmCharacterCache,
  healthCheck,
  autoOptimize,
  emergencyCacheClear
} from '@/lib/voiceOptimization';
import { voiceCache } from '@/lib/voiceCache';

export const dynamic = 'force-dynamic';

// GET: Get optimization status and metrics
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const action = url.searchParams.get('action');

    switch (action) {
      case 'status':
        const status = getOptimizationStatus();
        return NextResponse.json({
          success: true,
          data: status
        });

      case 'health':
        const health = healthCheck();
        return NextResponse.json({
          success: true,
          data: health
        });

      case 'stats':
        const stats = voiceCache.getStats();
        return NextResponse.json({
          success: true,
          data: stats
        });

      case 'metrics':
        // Log metrics and return them
        logPerformanceMetrics();
        const metricsStatus = getOptimizationStatus();
        return NextResponse.json({
          success: true,
          data: {
            ...metricsStatus,
            health: healthCheck()
          }
        });

      default:
        return NextResponse.json({
          success: true,
          data: {
            status: getOptimizationStatus(),
            health: healthCheck(),
            stats: voiceCache.getStats()
          }
        });
    }
  } catch (error) {
    console.error('Voice optimization API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

// POST: Perform optimization actions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, characterId, language } = body;

    switch (action) {
      case 'warm-character':
        if (!characterId) {
          return NextResponse.json(
            { success: false, error: 'Character ID required' },
            { status: 400 }
          );
        }
        
        await warmCharacterCache(characterId, language || 'ja');
        return NextResponse.json({
          success: true,
          message: `Cache warmed for character ${characterId}`
        });

      case 'auto-optimize':
        autoOptimize();
        return NextResponse.json({
          success: true,
          message: 'Auto-optimization triggered'
        });

      case 'clear-cache':
        emergencyCacheClear();
        return NextResponse.json({
          success: true,
          message: 'Cache cleared successfully'
        });

      case 'clear-expired':
        voiceCache.clearExpired();
        return NextResponse.json({
          success: true,
          message: 'Expired cache entries cleared'
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Voice optimization action error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

// DELETE: Clear cache or reset optimization
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const target = url.searchParams.get('target');

    switch (target) {
      case 'cache':
        emergencyCacheClear();
        return NextResponse.json({
          success: true,
          message: 'All cache cleared'
        });

      case 'expired':
        voiceCache.clearExpired();
        return NextResponse.json({
          success: true,
          message: 'Expired cache entries cleared'
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid target' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Voice optimization delete error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
