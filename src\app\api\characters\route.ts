import { NextRequest, NextResponse } from 'next/server';
import { loadCharactersFromServer } from '@/lib/serverFileManager';

// Helper functions to get voice configuration based on provider
function getVoiceIdForProvider(char: any): string {
  const provider = char.voiceProvider || 'elevenlabs';

  switch (provider) {
    case 'minimax':
      // Priority: 1) voiceConfig.voiceId (admin UI), 2) customVoiceId, 3) minimaxVoiceId, 4) default
      return char.voiceConfig?.voiceId ||
             char.customVoiceId ||
             char.minimaxVoiceId ||
             'default-voice';
    case 'elevenlabs':
    default:
      // Priority: 1) voiceConfig.voiceId (admin UI), 2) elevenLabsVoiceId, 3) default
      return char.voiceConfig?.voiceId ||
             char.elevenLabsVoiceId ||
             'default-voice';
  }
}

function getVoiceModelForProvider(char: any): string | undefined {
  const provider = char.voiceProvider || 'elevenlabs';

  switch (provider) {
    case 'minimax':
      return char.minimaxModel || 'speech-01-turbo';
    case 'elevenlabs':
    default:
      return undefined; // ElevenLabs doesn't use model selection
  }
}

function getVoiceSettingsForProvider(char: any): Record<string, any> {
  const provider = char.voiceProvider || 'elevenlabs';

  switch (provider) {
    case 'minimax':
      return {
        speed: 1.0,
        vol: 1.0,
        pitch: 0,
        audio_sample_rate: 24000,
        bitrate: 128000
      };
    case 'elevenlabs':
    default:
      return {
        stability: 0.5,
        similarityBoost: 0.5
      };
  }
}
import { Timestamp } from 'firebase/firestore';

// Helper function to safely convert date to timestamp
function toTimestamp(dateValue: string | Timestamp | Date): { seconds: number; nanoseconds: number } {
  try {
    if (typeof dateValue === 'string') {
      const date = new Date(dateValue);
      return {
        seconds: Math.floor(date.getTime() / 1000),
        nanoseconds: 0
      };
    } else if (dateValue instanceof Date) {
      return {
        seconds: Math.floor(dateValue.getTime() / 1000),
        nanoseconds: 0
      };
    } else if (dateValue && typeof dateValue === 'object' && 'seconds' in dateValue) {
      // Already a Timestamp
      return {
        seconds: dateValue.seconds,
        nanoseconds: dateValue.nanoseconds || 0
      };
    } else {
      // Fallback to current time
      const now = new Date();
      return {
        seconds: Math.floor(now.getTime() / 1000),
        nanoseconds: 0
      };
    }
  } catch (error) {
    console.error('Error converting date:', error);
    const now = new Date();
    return {
      seconds: Math.floor(now.getTime() / 1000),
      nanoseconds: 0
    };
  }
}

// GET - List active characters for public use
export async function GET(request: NextRequest) {
  try {
    // Force reload characters to ensure latest data
    const { forceReloadCharacters } = await import('@/lib/characterManager');

    // Force reload to get latest character settings
    forceReloadCharacters();

    // Load characters from server files
    const serverCharacters = loadCharactersFromServer();
    
    // Filter only active characters and convert to array
    const activeCharacters = Object.values(serverCharacters)
      .filter(char => char.isActive)
      .sort((a, b) => (a.sortOrder || 999) - (b.sortOrder || 999));

    // Transform to match the expected Character interface
    const publicCharacters = activeCharacters.map(char => {
      console.log(`Processing character ${char.id}:`, {
        profileImage: char.media?.profileImage,
        thumbnailImage: char.media?.thumbnailImage
      });

      return {
        id: char.id,
        name: char.name,
        description: char.description,
        personality: char.personality,
        interests: char.interests,
        age: char.age,
        occupation: char.occupation,
        background: char.background,
        welcomeMessage: char.welcomeMessage, // Include welcome message
        tags: {
          en: char.personality?.en || [],
          ja: char.personality?.ja || []
        },
        images: {
          avatar: char.media?.profileImage || '/characters/images/placeholder.svg',
          preview: char.media?.thumbnailImage || char.media?.profileImage || '/characters/images/placeholder.svg'
        },
        videos: {
          idle: char.videoSets && char.videoSets.length > 0 && char.videoSets[0].normalVideoPath
            ? [char.videoSets[0].normalVideoPath]
            : char.media?.idleVideo ? [char.media.idleVideo] : [],
          speaking: char.videoSets && char.videoSets.length > 0 && char.videoSets[0].lipSyncVideoPath
            ? [char.videoSets[0].lipSyncVideoPath]
            : char.media?.speakingVideo ? [char.media.speakingVideo] : []
        },
        videoSets: char.videoSets || [],
        // Use actual AI and voice config from character data
        aiConfig: {
          provider: char.aiProvider || char.aiConfig?.provider || 'openrouter.ai',
          model: char.aiModel || char.aiConfig?.model || 'gpt-3.5-turbo',
          temperature: char.aiConfig?.temperature || 0.7,
          maxTokens: char.aiConfig?.maxTokens || 500
        },
        voiceConfig: {
          provider: char.voiceProvider || 'elevenlabs',
          voiceId: getVoiceIdForProvider(char),
          model: getVoiceModelForProvider(char),
          settings: getVoiceSettingsForProvider(char)
        },
        // Don't include system prompts for security
        systemPrompt: {
          en: '',
          ja: ''
        },
        isActive: char.isActive,
        createdAt: toTimestamp(char.createdAt),
        updatedAt: toTimestamp(char.updatedAt)
      };
    });

    return NextResponse.json({
      success: true,
      characters: publicCharacters,
      total: publicCharacters.length
    });

  } catch (error) {
    console.error('Error fetching characters:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch characters',
        characters: [],
        total: 0
      },
      { status: 500 }
    );
  }
}
