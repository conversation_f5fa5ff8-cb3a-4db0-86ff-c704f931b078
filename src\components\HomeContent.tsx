"use client";

import { useI18n } from "@/contexts/I18nProvider";
import { useAuth } from "@/contexts/AuthContext";
import CharacterGrid from "@/components/CharacterGrid";
import EmailVerificationModal from "@/components/EmailVerificationModal";
import { useEffect, useState } from "react";

export default function HomeContent() {
  const { t, isLoading } = useI18n();
  const { user, loading: authLoading } = useAuth();
  const [mounted, setMounted] = useState(false);
  const [showEmailVerificationModal, setShowEmailVerificationModal] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Check for email verification status when user is loaded
  useEffect(() => {
    if (mounted && !authLoading && user) {
      // Check if user signed up with email/password and email is not verified
      const isEmailPasswordUser = user.providerData.some(provider => provider.providerId === 'password');
      const shouldShowModal = isEmailPasswordUser && !user.emailVerified;

      if (shouldShowModal) {
        // Check if email verification was just completed in this session
        const completedKey = `emailVerificationCompleted_${user.uid}`;
        const justCompleted = sessionStorage.getItem(completedKey);

        // Check if modal was already shown in this session
        const sessionKey = `emailVerificationShown_${user.uid}`;
        const shownInSession = sessionStorage.getItem(sessionKey);

        // Don't show modal if verification was just completed or already shown
        if (!justCompleted && !shownInSession) {
          // Show modal after a short delay to avoid jarring experience
          const timer = setTimeout(() => {
            setShowEmailVerificationModal(true);
            // Mark as shown in this session
            sessionStorage.setItem(sessionKey, 'true');
          }, 1000);

          return () => clearTimeout(timer);
        }
      }
    }
  }, [mounted, authLoading, user]);

  // Prevent hydration mismatch by showing loading state until mounted
  if (!mounted || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold text-primary mb-4">
            AiLuvChat
          </h1>
          <p className="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto">
            Experience the future of AI companionship. Chat with your perfect virtual partner.
          </p>
        </div>
        <CharacterGrid />
      </div>
    );
  }

  return (
    <>
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold text-primary mb-4">
            {t('title')}
          </h1>
          <p className="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto">
            {t('subtitle')}
          </p>
        </div>
        <CharacterGrid />
      </div>

      {/* Email Verification Modal */}
      <EmailVerificationModal
        isOpen={showEmailVerificationModal}
        onClose={() => setShowEmailVerificationModal(false)}
        userEmail={user?.email || ''}
      />
    </>
  );
}
