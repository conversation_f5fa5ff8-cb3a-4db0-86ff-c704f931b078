"use client";

import { useI18n } from "@/contexts/I18nProvider";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

export default function JaTokushoPage() {
  const { language } = useI18n();

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-white mb-4">
                特定商取引法に基づく表記
              </h1>
              <p className="text-gray-300 text-lg">
                最終更新日: 2025年6月4日
              </p>
            </div>

            <div className="bg-white/10 backdrop-blur-md rounded-xl p-8 space-y-6">
              <div className="space-y-8 text-white">
                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">事業者名</h2>
                  <p className="text-gray-300">
                    Deepath
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">住所</h2>
                  <p className="text-gray-300">
                    開示の求めがあった場合、遅滞なく開示します
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">電話番号</h2>
                  <p className="text-gray-300">
                    開示の求めがあった場合、遅滞なく開示します
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">メールアドレス</h2>
                  <p className="text-gray-300">
                    <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300">
                      <EMAIL>
                    </a>
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">オペレーション責任者</h2>
                  <p className="text-gray-300">
                    開示の求めがあった場合、遅滞なく開示します
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">追加手数料等の追加料金</h2>
                  <p className="text-gray-300">
                    なし
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">交換および返品（返金ポリシー）</h2>
                  <p className="text-gray-300">
                    なし
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">配達時間</h2>
                  <p className="text-gray-300">
                    ポイント購入後、即時追加されます
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">利用可能な支払い方法</h2>
                  <p className="text-gray-300">
                    クレジットカード
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">支払期間</h2>
                  <p className="text-gray-300">
                    クレジットカードによるお支払いは即時処理されます。
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">価格</h2>
                  <div className="text-gray-300 space-y-2">
                    <p>5ドル</p>
                    <p>10ドル</p>
                  </div>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">お問い合わせ</h2>
                  <p className="text-gray-300">
                    特定商取引法に関するご質問は、
                    <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300">
                      <EMAIL>
                    </a>
                    までお問い合わせください。
                  </p>
                  <p className="text-gray-300 mt-2">
                    または、<a href="/contact" className="text-blue-400 hover:text-blue-300">お問い合わせフォーム</a>をご利用ください。
                  </p>
                </section>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
