"use client";

import React from 'react';

// Updated: 2025-06-08 - Legal compliance improvements

export default function TokushoPage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Simple header for tokusho page */}
      <header className="bg-white shadow-md">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center py-4">
            <a href="/ja" className="text-2xl font-bold text-primary">
              AiLuvChat
            </a>
            <nav className="flex items-center space-x-6">
              <a href="/ja" className="text-gray-600 hover:text-primary transition-colors">
                ホーム
              </a>
            </nav>
          </div>
        </div>
      </header>
      <div className="bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 min-h-screen">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-white mb-4">
                特定商取引法に基づく表記
              </h1>
              <p className="text-gray-300 text-lg">
                最終更新日: 2025年6月8日（更新）
              </p>
            </div>

            <div className="bg-white/10 backdrop-blur-md rounded-xl p-8 space-y-6">
              <div className="space-y-8 text-white">
                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">販売業社の名称</h2>
                  <p className="text-gray-300">
                    Deepath
                    ※事業者の氏名（本名）は、ご請求に基づき遅滞なく開示いたします。

                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">所在地</h2>
                  <p className="text-gray-300">
                    開示の求めがあった場合、遅滞なく開示します
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">電話番号</h2>
                  <p className="text-gray-300">
                    開示の求めがあった場合、遅滞なく開示します
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">メールアドレス</h2>
                  <p className="text-gray-300">
                    <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300">
                      <EMAIL>
                    </a>
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">運営統括責任者	</h2>
                  <p className="text-gray-300">
                    開示の求めがあった場合、遅滞なく開示します
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">追加手数料等の追加料金</h2>
                  <p className="text-gray-300">
                    なし
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">交換および返品（返金ポリシー）</h2>
                  <p className="text-gray-300">
                    デジタルコンテンツの特性上、ポイント購入完了後の返品・返金・交換は原則としてお受けできません。
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">引渡時期</h2>
                  <p className="text-gray-300">
                    ポイント購入後、即時アカウントに付与されます。
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">受け付け可能な決済手段	</h2>
                  <p className="text-gray-300">
                    クレジットカード
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">決済期間</h2>
                  <p className="text-gray-300">
                    クレジットカードによるお支払いは即時処理されます。
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">販売価格</h2>
                  <div className="text-gray-300 space-y-2">
                    <p>$5.00 USD</p>
                    <p> $10.00 USD</p>
                    <p> $20.00 USD</p>
                    ※上記は米ドル建ての価格です。 ※日本円でのご請求額は、お客様がご利用のクレジットカード会社または決済代行会社が定める決済時点の為替レートによって算出されます。このため、実際の請求額は日々変動いたします。
                  </div>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold text-white mb-4">お問い合わせ</h2>
                  <p className="text-gray-300">
                    特定商取引法に関するご質問は、
                    <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300">
                      <EMAIL>
                    </a>
                    までお問い合わせください。
                  </p>
                  <p className="text-gray-300 mt-2">
                    または、<a href="/contact" className="text-blue-400 hover:text-blue-300">お問い合わせフォーム</a>をご利用ください。
                  </p>
                </section>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Simple footer for tokusho page */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="mb-4">
              <h3 className="text-xl font-bold mb-2">AiLuvChat</h3>
              <p className="text-gray-400">AI Character Chat Platform</p>
            </div>
            <div className="flex justify-center space-x-6 mb-4">
              <a href="/ja/privacy" className="text-gray-400 hover:text-white transition-colors">
                プライバシーポリシー
              </a>
              <a href="/ja/terms" className="text-gray-400 hover:text-white transition-colors">
                利用規約
              </a>
              <a href="/ja/tokusho" className="text-gray-400 hover:text-white transition-colors">
                特定商取引法に基づく表記
              </a>
            </div>
            <p className="text-gray-500 text-sm">
              © 2025 AiLuvChat. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}