-- =====================================================
-- AiLuvChat 有料ユーザー限定記憶システム セットアップ
-- =====================================================

-- 1. Vector拡張機能を有効化
CREATE EXTENSION IF NOT EXISTS vector;

-- 2. 長期記憶テーブル作成
CREATE TABLE IF NOT EXISTS long_term_memories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id TEXT NOT NULL,
    character_id TEXT NOT NULL,
    content TEXT NOT NULL,
    embedding vector(1024) NOT NULL,
    importance_score FLOAT DEFAULT 0.5,
    emotion_context TEXT,
    conversation_context JSONB,
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- インデックス用
    CONSTRAINT valid_importance CHECK (importance_score >= 0 AND importance_score <= 1)
);

-- 3. インデックス作成（パフォーマンス向上）
CREATE INDEX IF NOT EXISTS idx_memories_user_character 
ON long_term_memories(user_id, character_id);

CREATE INDEX IF NOT EXISTS idx_memories_created_at 
ON long_term_memories(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_memories_importance 
ON long_term_memories(importance_score DESC);

-- 4. ベクトル類似度検索用インデックス（HNSW）
CREATE INDEX IF NOT EXISTS idx_memories_embedding_cosine 
ON long_term_memories USING hnsw (embedding vector_cosine_ops);

-- 5. 更新日時自動更新トリガー
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_memories_updated_at 
    BEFORE UPDATE ON long_term_memories 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 6. ベクトル類似度検索RPC関数
CREATE OR REPLACE FUNCTION search_similar_memories(
    query_embedding vector(1024),
    target_user_id TEXT,
    target_character_id TEXT,
    similarity_threshold FLOAT DEFAULT 0.7,
    max_results INT DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    content TEXT,
    importance_score FLOAT,
    emotion_context TEXT,
    conversation_context JSONB,
    tags TEXT[],
    similarity FLOAT,
    created_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.content,
        m.importance_score,
        m.emotion_context,
        m.conversation_context,
        m.tags,
        (1 - (m.embedding <=> query_embedding)) as similarity,
        m.created_at
    FROM long_term_memories m
    WHERE 
        m.user_id = target_user_id 
        AND m.character_id = target_character_id
        AND (1 - (m.embedding <=> query_embedding)) >= similarity_threshold
    ORDER BY 
        (1 - (m.embedding <=> query_embedding)) DESC,
        m.importance_score DESC,
        m.created_at DESC
    LIMIT max_results;
END;
$$;

-- 7. 記憶の重要度更新関数
CREATE OR REPLACE FUNCTION update_memory_importance(
    memory_id UUID,
    new_importance FLOAT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
    UPDATE long_term_memories 
    SET 
        importance_score = new_importance,
        updated_at = NOW()
    WHERE id = memory_id;
    
    RETURN FOUND;
END;
$$;

-- 8. 古い記憶の自動削除関数（オプション）
CREATE OR REPLACE FUNCTION cleanup_old_memories(
    days_to_keep INT DEFAULT 365
)
RETURNS INT
LANGUAGE plpgsql
AS $$
DECLARE
    deleted_count INT;
BEGIN
    DELETE FROM long_term_memories 
    WHERE created_at < NOW() - INTERVAL '1 day' * days_to_keep
    AND importance_score < 0.3;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;

-- 9. Row Level Security (RLS) 設定
ALTER TABLE long_term_memories ENABLE ROW LEVEL SECURITY;

-- サービスロールのみアクセス可能
CREATE POLICY "Service role can manage all memories" ON long_term_memories
    FOR ALL USING (auth.role() = 'service_role');

-- 10. 統計情報更新
ANALYZE long_term_memories;

-- セットアップ完了確認
SELECT 'Memory system setup completed successfully!' as status;
