// Real-time streaming voice generation API
import { NextRequest, NextResponse } from 'next/server';
import SecretCache from '@/lib/server/secretCache';

export const dynamic = 'force-dynamic';

interface StreamingVoiceRequest {
  text: string;
  voiceId: string;
  language: 'ja' | 'en';
  characterId: string;
  provider?: 'elevenlabs' | 'minimax';
}

// POST: Stream voice audio in real-time
export async function POST(request: NextRequest) {
  try {
    // Initialize secret cache if not already done
    await SecretCache.initialize();

    const { text, voiceId, language, characterId, provider }: StreamingVoiceRequest = await request.json();
    // Voice stream request received (detailed logging reduced for performance)

    if (!text || !voiceId) {
      return NextResponse.json(
        { success: false, error: 'Text and voiceId are required' },
        { status: 400 }
      );
    }

    console.log(`=== Real-time Voice Streaming ===`);
    console.log(`Provider: ${provider || 'auto-detect'}`);
    console.log(`Text: ${text.substring(0, 50)}...`);

    // Determine provider
    let detectedProvider = provider;
    if (!detectedProvider && characterId) {
      try {
        const { CharacterService } = await import('@/lib/characterService');
        const character = CharacterService.getCharacter(characterId);
        const provider = character?.voiceProvider || character?.voiceConfig?.provider || 'elevenlabs';
        detectedProvider = provider === 'fal-minimax' ? 'minimax' : provider as 'elevenlabs' | 'minimax';
      } catch (error) {
        console.warn('Failed to load character data:', error);
        detectedProvider = 'elevenlabs';
      }
    }

    // Using streaming provider (logging reduced for performance)

    // Route to appropriate streaming handler
    if (detectedProvider === 'minimax') {
      return await streamMiniMaxVoice(text, voiceId, characterId);
    } else {
      return await streamElevenLabsVoice(text, voiceId, language);
    }

  } catch (error) {
    console.error('🚨 Streaming voice API error:', error);
    console.error('🚨 Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Stream MiniMax voice generation
async function streamMiniMaxVoice(text: string, voiceId: string, characterId?: string) {
  // Starting MiniMax streaming (detailed logging reduced for performance)

  const apiKey = SecretCache.get('MINIMAX_API_KEY');
  const groupId = SecretCache.get('MINIMAX_GROUP_ID');

  if (!apiKey || !groupId) {
    console.error('❌ Missing MiniMax credentials');
    throw new Error('MiniMax API credentials not configured');
  }

  // Get character-specific model if available
  let model = 'speech-01-turbo';
  if (characterId) {
    try {
      const { CharacterService } = await import('@/lib/characterService');
      const character = CharacterService.getCharacter(characterId);
      model = character?.minimaxModel || 'speech-01-turbo'; // Use character's model or default
      // Using MiniMax model (logging reduced for performance)
    } catch (error) {
      console.warn('Failed to load character model:', error);
    }
  }

  // Detect if this is a custom voice ID
  // Custom voices can start with 'Voice' or be user-defined names like 'Japanese_DecisivePrincess'
  const isCustomVoice = voiceId.startsWith('Voice') ||
                       (voiceId.length > 10 && voiceId.includes('_')) ||
                       (voiceId.length > 15); // Long names are likely custom
  // Voice ID type detected (logging reduced for performance)

  // Use different endpoint for custom voices if needed
  let apiUrl = `https://api.minimaxi.chat/v1/t2a_v2?GroupId=${groupId}`;

  if (isCustomVoice) {
    // Try alternative endpoint for custom voices
    // Using custom voice endpoint (logging reduced for performance)
    // apiUrl = `https://api.minimaxi.chat/v1/t2a_custom?GroupId=${groupId}`;
    // Keep same endpoint but with different parameters for now
  }

  const requestBody: any = {
    text,
    voice_setting: {
      voice_id: voiceId,
      speed: 1.0, // Normal speech speed
      vol: 1.0,
      pitch: 0
    },
    model,
    audio_setting: {
      sample_rate: 16000, // Standard sample rate for better quality
      bitrate: 64000, // Standard bitrate
      format: 'mp3'
    },
    stream: true
  };

  // Add custom voice specific parameters if needed
  if (isCustomVoice) {
    // Using custom voice parameters (logging reduced for performance)
    // Custom voices might need different settings
    requestBody.voice_setting = {
      ...requestBody.voice_setting,
      // Try different parameter combinations for custom voices
      voice_type: 'custom', // This might be needed for custom voices
      // Alternative parameter names that might work
      custom_voice_id: voiceId,
      voice_name: voiceId
    };

    // Also try different model for custom voices
    if (model === 'speech-01-turbo') {
      requestBody.model = 'speech-02-turbo'; // Try newer model for custom voices
      // Using speech-02-turbo model for custom voice (logging reduced for performance)
    }
  }

  // MiniMax API request prepared (detailed logging reduced for performance)

  const response = await fetch(apiUrl, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(requestBody)
  });

  // MiniMax API response received (detailed logging reduced for performance)

  if (!response.ok) {
    const errorText = await response.text();
    console.error('🎵 MiniMax API Error:', response.status);
    throw new Error(`MiniMax API error: ${response.status} - ${errorText}`);
  }

  // Create streaming response
  const stream = new ReadableStream({
    async start(controller) {
      const reader = response.body?.getReader();
      if (!reader) {
        controller.error(new Error('No response body'));
        return;
      }

      const decoder = new TextDecoder();
      let buffer = '';

      // Helper function to convert hex to bytes
      function hexToBytes(hex: string): Uint8Array {
        const bytes = new Uint8Array(hex.length / 2);
        for (let i = 0; i < hex.length; i += 2) {
          bytes[i / 2] = parseInt(hex.substring(i, i + 2), 16);
        }
        return bytes;
      }

      try {
        // Starting MiniMax stream processing
        let chunkCount = 0;
        let totalBytes = 0;

        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            console.log(`🎵 MiniMax stream completed: ${chunkCount} chunks, ${totalBytes} bytes`);
            break;
          }

          const chunk = decoder.decode(value, { stream: true });
          // Removed verbose chunk logging to reduce terminal output

          buffer += chunk;
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim()) {
              // Processing line (logging reduced for performance)
              try {
                // MiniMax returns SSE format: "data: {json}"
                let jsonData: any;
                if (line.startsWith('data: ')) {
                  // Extract JSON from SSE format
                  const jsonStr = line.substring(6).trim();
                  // Removed verbose JSON parsing logs to reduce terminal output
                  jsonData = JSON.parse(jsonStr);
                } else {
                  // Try direct JSON parsing as fallback
                  jsonData = JSON.parse(line.trim());
                }

                // Removed verbose JSON structure logging to reduce terminal output

                if (jsonData.data && jsonData.data.audio && !jsonData.extra_info) {
                  const hexAudio = jsonData.data.audio;
                  const audioBytes = hexToBytes(hexAudio);
                  chunkCount++;
                  totalBytes += audioBytes.length;
                  // Audio chunk sent (logging reduced for performance)

                  // Convert to Base64 for efficient transfer
                  const base64Audio = Buffer.from(audioBytes).toString('base64');

                  // Send audio chunk immediately
                  const chunkData = {
                    type: 'audio_chunk',
                    data: base64Audio,
                    timestamp: Date.now()
                  };

                  controller.enqueue(
                    new TextEncoder().encode(`data: ${JSON.stringify(chunkData)}\n\n`)
                  );
                } else {
                  console.log(`🎵 Skipping non-audio data:`, jsonData);
                }
              } catch (parseError) {
                console.error('Error parsing MiniMax chunk:', parseError, 'Line:', line.substring(0, 100));
                // Skip invalid JSON lines
              }
            }
          }
        }

        if (chunkCount === 0) {
          console.error('🚨 No audio chunks received from MiniMax Direct');

          if (isCustomVoice) {
            console.error('🚨 Custom Voice ID may not be supported or requires different parameters');
            console.error('🚨 Voice ID:', voiceId);
            console.error('🚨 Try using a standard voice ID from the dropdown instead');
            throw new Error(`Custom Voice ID "${voiceId}" may not be supported. Please try using a standard voice from the dropdown.`);
          } else {
            throw new Error('No audio chunks received from MiniMax Direct');
          }
        }

        // Send completion signal
        controller.enqueue(
          new TextEncoder().encode(`data: ${JSON.stringify({ type: 'complete' })}\n\n`)
        );
        controller.close();

      } catch (error) {
        console.error('Streaming error:', error);
        controller.error(error);
      }
    }
  });

  return new NextResponse(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}

// Stream ElevenLabs voice generation
async function streamElevenLabsVoice(text: string, voiceId: string, _language: 'ja' | 'en') {
  const apiKey = SecretCache.get('ELEVENLABS_API_KEY');
  if (!apiKey) {
    throw new Error('ElevenLabs API key not configured');
  }

  const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voiceId}/stream`, {
    method: 'POST',
    headers: {
      'Accept': 'audio/mpeg',
      'Content-Type': 'application/json',
      'xi-api-key': apiKey
    },
    body: JSON.stringify({
      text,
      model_id: 'eleven_turbo_v2_5',
      voice_settings: {
        stability: 0.3,
        similarity_boost: 0.3,
        style: 0.0,
        use_speaker_boost: false
      },
      optimize_streaming_latency: 4,
      output_format: "mp3_22050_32"
    })
  });

  if (!response.ok) {
    throw new Error(`ElevenLabs API error: ${response.status}`);
  }

  // Create streaming response
  const stream = new ReadableStream({
    async start(controller) {
      const reader = response.body?.getReader();
      if (!reader) {
        controller.error(new Error('No response body'));
        return;
      }

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          // Convert to Base64 for efficient transfer
          const base64Audio = Buffer.from(value).toString('base64');

          // Send audio chunk immediately
          const chunkData = {
            type: 'audio_chunk',
            data: base64Audio,
            timestamp: Date.now()
          };
          
          controller.enqueue(
            new TextEncoder().encode(`data: ${JSON.stringify(chunkData)}\n\n`)
          );
        }

        // Send completion signal
        controller.enqueue(
          new TextEncoder().encode(`data: ${JSON.stringify({ type: 'complete' })}\n\n`)
        );
        controller.close();

      } catch (error) {
        console.error('ElevenLabs streaming error:', error);
        controller.error(error);
      }
    }
  });

  return new NextResponse(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}
