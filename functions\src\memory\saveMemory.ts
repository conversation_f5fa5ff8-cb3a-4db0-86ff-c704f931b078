/**
 * 有料ユーザー限定 - 長期記憶保存Cloud Function
 * Firestoreの会話保存をトリガーに、ベクトル化してSupabaseに保存
 */

import * as functions from 'firebase-functions';
import { createClient } from '@supabase/supabase-js';

// Supabase初期化（Firebase Functions Config使用）
const supabaseUrl = process.env.SUPABASE_PROJECT_URL || functions.config().supabase?.project_url;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || functions.config().supabase?.service_role_key;

let supabase: any = null;
if (supabaseUrl && supabaseKey) {
  supabase = createClient(supabaseUrl, supabaseKey);
  functions.logger.info('Supabase client initialized successfully');
} else {
  functions.logger.warn('Supabase credentials not configured. Memory system will be disabled.');
}

// Google AI API用の設定
const GOOGLE_AI_API_KEY = process.env.GOOGLE_AI_API_KEY || functions.config().gemini?.api_key;

interface MessageData {
  userId: string;
  characterId: string;
  content: string;
  sender: 'user' | 'ai';
  createdAt: any;
  metadata?: {
    emotion?: string;
    context?: any;
  };
}

interface UserData {
  subscriptionTier: string;
  email: string;
  displayName?: string;
}

/**
 * 課金状態確認
 */
async function checkUserSubscription(userId: string): Promise<boolean> {
  try {
    // Firebase Admin SDKを使用してユーザー情報を取得
    const admin = require('firebase-admin');
    const db = admin.firestore();

    const userDoc = await db.collection('users').doc(userId).get();

    if (!userDoc.exists) {
      functions.logger.warn(`User document not found: ${userId}`);
      return false;
    }

    const userData = userDoc.data() as UserData;
    const subscriptionTier = userData.subscriptionTier || 'free';

    // "free"以外は有料ユーザー
    const isPaidUser = subscriptionTier !== 'free';

    functions.logger.info(`User ${userId} subscription check:`, {
      tier: subscriptionTier,
      isPaid: isPaidUser
    });

    return isPaidUser;
  } catch (error) {
    functions.logger.error('Error checking user subscription:', error);
    return false;
  }
}

/**
 * テキストをベクトル化（Google AI API使用）
 */
async function generateEmbedding(text: string): Promise<number[]> {
  try {
    if (!GOOGLE_AI_API_KEY) {
      throw new Error('Google AI API key not configured');
    }

    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:embedContent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-goog-api-key': GOOGLE_AI_API_KEY
      },
      body: JSON.stringify({
        content: {
          parts: [{ text: text }]
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Embedding API error: ${response.status}`);
    }

    const data = await response.json();

    if (!data.embedding?.values) {
      throw new Error('No embedding values returned');
    }

    return data.embedding.values;
  } catch (error) {
    functions.logger.error('Error generating embedding:', error);
    throw error;
  }
}

/**
 * 記憶の重要度スコア計算
 */
function calculateImportanceScore(message: MessageData): number {
  let score = 0.5; // ベーススコア
  
  const content = message.content.toLowerCase();
  
  // 感情的な内容
  const emotionalKeywords = ['好き', 'love', '嫌い', 'hate', '悲しい', 'sad', '嬉しい', 'happy', '怒り', 'angry'];
  if (emotionalKeywords.some(keyword => content.includes(keyword))) {
    score += 0.2;
  }
  
  // 個人情報
  const personalKeywords = ['名前', 'name', '年齢', 'age', '仕事', 'job', '家族', 'family', '趣味', 'hobby'];
  if (personalKeywords.some(keyword => content.includes(keyword))) {
    score += 0.3;
  }
  
  // 長いメッセージ（詳細な情報）
  if (content.length > 100) {
    score += 0.1;
  }
  
  // AIからの重要な応答
  if (message.sender === 'ai' && content.length > 50) {
    score += 0.1;
  }
  
  return Math.min(score, 1.0);
}

/**
 * Supabaseに記憶を保存
 */
async function saveToSupabase(
  userId: string,
  characterId: string,
  content: string,
  embedding: number[],
  importance: number,
  metadata?: any
) {
  try {
    if (!supabase) {
      functions.logger.warn('Supabase not configured, skipping memory save');
      return null;
    }

    const { data, error } = await supabase
      .from('long_term_memories')
      .insert({
        user_id: userId,
        character_id: characterId,
        content: content,
        embedding: embedding,
        importance_score: importance,
        emotion_context: metadata?.emotion || null,
        conversation_context: metadata?.context || null,
        tags: metadata?.tags || []
      });

    if (error) {
      throw error;
    }

    functions.logger.info('Memory saved to Supabase successfully:', {
      userId,
      characterId,
      importance,
      contentLength: content.length
    });

    return data;
  } catch (error) {
    functions.logger.error('Error saving to Supabase:', error);
    throw error;
  }
}

/**
 * メイン処理：メッセージ保存トリガー（1st Gen）
 */
export const saveMemoryOnMessage = functions.firestore
  .document('messages/{messageId}')
  .onCreate(async (snap, context) => {
    const messageData = snap.data() as MessageData;

    if (!messageData) {
      functions.logger.error('No message data found');
      return;
    }

    const { userId, characterId, content, sender } = messageData;

    functions.logger.info('Memory save triggered:', {
      userId,
      characterId,
      sender,
      contentLength: content?.length || 0
    });
    
    try {
      // 1. 課金状態確認（最重要）
      const isPaidUser = await checkUserSubscription(userId);
      
      if (!isPaidUser) {
        functions.logger.info(`Skipping memory save for free user: ${userId}`);
        return; // 無料ユーザーは処理終了
      }

      // 2. 有料ユーザーのみ以下の処理を実行
      functions.logger.info(`Processing memory for paid user: ${userId}`);

      // 3. 内容の妥当性チェック
      if (!content || content.trim().length < 10) {
        functions.logger.info('Content too short, skipping memory save');
        return;
      }
      
      // 4. ベクトル化
      const embedding = await generateEmbedding(content);
      
      // 5. 重要度スコア計算
      const importance = calculateImportanceScore(messageData);
      
      // 6. Supabaseに保存
      await saveToSupabase(
        userId,
        characterId,
        content,
        embedding,
        importance,
        messageData.metadata
      );

      functions.logger.info('Memory processing completed successfully');

    } catch (error) {
      functions.logger.error('Error in memory save process:', error);
      // エラーでも処理は継続（メッセージ保存は成功している）
    }
  });
