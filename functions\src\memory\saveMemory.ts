/**
 * 有料ユーザー限定 - 長期記憶保存Cloud Function
 * Firestoreの会話保存をトリガーに、ベクトル化してSupabaseに保存
 */

import { onDocumentCreated } from 'firebase-functions/v2/firestore';
import { logger } from 'firebase-functions';
import { initializeApp } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { createClient } from '@supabase/supabase-js';
import { VertexAI } from '@google-cloud/vertexai';

// Firebase Admin初期化
if (!getApps().length) {
  initializeApp();
}
const db = getFirestore();

// Supabase初期化
const supabaseUrl = process.env.SUPABASE_PROJECT_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

// Vertex AI初期化
const vertexAI = new VertexAI({
  project: process.env.GOOGLE_CLOUD_PROJECT!,
  location: 'us-central1'
});

interface MessageData {
  userId: string;
  characterId: string;
  content: string;
  sender: 'user' | 'ai';
  createdAt: any;
  metadata?: {
    emotion?: string;
    context?: any;
  };
}

interface UserData {
  subscriptionTier: string;
  email: string;
  displayName?: string;
}

/**
 * 課金状態確認
 */
async function checkUserSubscription(userId: string): Promise<boolean> {
  try {
    const userDoc = await db.collection('users').doc(userId).get();
    
    if (!userDoc.exists) {
      logger.warn(`User document not found: ${userId}`);
      return false;
    }
    
    const userData = userDoc.data() as UserData;
    const subscriptionTier = userData.subscriptionTier || 'free';
    
    // "free"以外は有料ユーザー
    const isPaidUser = subscriptionTier !== 'free';
    
    logger.info(`User ${userId} subscription check:`, {
      tier: subscriptionTier,
      isPaid: isPaidUser
    });
    
    return isPaidUser;
  } catch (error) {
    logger.error('Error checking user subscription:', error);
    return false;
  }
}

/**
 * テキストをベクトル化
 */
async function generateEmbedding(text: string): Promise<number[]> {
  try {
    const model = vertexAI.getGenerativeModel({
      model: 'text-multilingual-embedding-002'
    });
    
    const result = await model.embedContent(text);
    
    if (!result.embedding?.values) {
      throw new Error('No embedding values returned');
    }
    
    return result.embedding.values;
  } catch (error) {
    logger.error('Error generating embedding:', error);
    throw error;
  }
}

/**
 * 記憶の重要度スコア計算
 */
function calculateImportanceScore(message: MessageData): number {
  let score = 0.5; // ベーススコア
  
  const content = message.content.toLowerCase();
  
  // 感情的な内容
  const emotionalKeywords = ['好き', 'love', '嫌い', 'hate', '悲しい', 'sad', '嬉しい', 'happy', '怒り', 'angry'];
  if (emotionalKeywords.some(keyword => content.includes(keyword))) {
    score += 0.2;
  }
  
  // 個人情報
  const personalKeywords = ['名前', 'name', '年齢', 'age', '仕事', 'job', '家族', 'family', '趣味', 'hobby'];
  if (personalKeywords.some(keyword => content.includes(keyword))) {
    score += 0.3;
  }
  
  // 長いメッセージ（詳細な情報）
  if (content.length > 100) {
    score += 0.1;
  }
  
  // AIからの重要な応答
  if (message.sender === 'ai' && content.length > 50) {
    score += 0.1;
  }
  
  return Math.min(score, 1.0);
}

/**
 * Supabaseに記憶を保存
 */
async function saveToSupabase(
  userId: string,
  characterId: string,
  content: string,
  embedding: number[],
  importance: number,
  metadata?: any
) {
  try {
    const { data, error } = await supabase
      .from('long_term_memories')
      .insert({
        user_id: userId,
        character_id: characterId,
        content: content,
        embedding: embedding,
        importance_score: importance,
        emotion_context: metadata?.emotion || null,
        conversation_context: metadata?.context || null,
        tags: metadata?.tags || []
      });
    
    if (error) {
      throw error;
    }
    
    logger.info('Memory saved to Supabase successfully:', {
      userId,
      characterId,
      importance,
      contentLength: content.length
    });
    
    return data;
  } catch (error) {
    logger.error('Error saving to Supabase:', error);
    throw error;
  }
}

/**
 * メイン処理：メッセージ保存トリガー
 */
export const saveMemoryOnMessage = onDocumentCreated(
  'messages/{messageId}',
  async (event) => {
    const messageData = event.data?.data() as MessageData;
    
    if (!messageData) {
      logger.error('No message data found');
      return;
    }
    
    const { userId, characterId, content, sender } = messageData;
    
    logger.info('Memory save triggered:', {
      userId,
      characterId,
      sender,
      contentLength: content?.length || 0
    });
    
    try {
      // 1. 課金状態確認（最重要）
      const isPaidUser = await checkUserSubscription(userId);
      
      if (!isPaidUser) {
        logger.info(`Skipping memory save for free user: ${userId}`);
        return; // 無料ユーザーは処理終了
      }
      
      // 2. 有料ユーザーのみ以下の処理を実行
      logger.info(`Processing memory for paid user: ${userId}`);
      
      // 3. 内容の妥当性チェック
      if (!content || content.trim().length < 10) {
        logger.info('Content too short, skipping memory save');
        return;
      }
      
      // 4. ベクトル化
      const embedding = await generateEmbedding(content);
      
      // 5. 重要度スコア計算
      const importance = calculateImportanceScore(messageData);
      
      // 6. Supabaseに保存
      await saveToSupabase(
        userId,
        characterId,
        content,
        embedding,
        importance,
        messageData.metadata
      );
      
      logger.info('Memory processing completed successfully');
      
    } catch (error) {
      logger.error('Error in memory save process:', error);
      // エラーでも処理は継続（メッセージ保存は成功している）
    }
  }
);
