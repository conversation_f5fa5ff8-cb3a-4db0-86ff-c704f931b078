<svg width="400" height="600" viewBox="0 0 400 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="600" fill="url(#gradient)"/>
  <circle cx="200" cy="200" r="60" fill="white" opacity="0.3"/>
  <path d="M160 180C160 168.954 168.954 160 180 160H220C231.046 160 240 168.954 240 180V200C240 211.046 231.046 220 220 220H180C168.954 220 160 211.046 160 200V180Z" fill="white" opacity="0.5"/>
  <circle cx="185" cy="185" r="5" fill="#29ABE2"/>
  <circle cx="215" cy="185" r="5" fill="#29ABE2"/>
  <path d="M190 205C190 208.314 192.686 211 196 211H204C207.314 211 210 208.314 210 205" stroke="#29ABE2" stroke-width="2" stroke-linecap="round"/>
  <text x="200" y="350" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" opacity="0.8">AI Character</text>
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#29ABE2;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#FFA500;stop-opacity:0.8" />
    </linearGradient>
  </defs>
</svg>
