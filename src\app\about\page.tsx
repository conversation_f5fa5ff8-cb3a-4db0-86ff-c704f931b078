"use client";

import { useI18n } from "@/contexts/I18nProvider";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

export default function AboutPage() {
  const { t } = useI18n();

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-white mb-4">
              {t('about.title', 'About AiLuvChat')}
            </h1>
            <p className="text-gray-300 text-lg">
              {t('about.subtitle', 'Your AI Companion Experience')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6">
              <h2 className="text-2xl font-semibold text-white mb-4">
                {t('about.mission.title', 'Our Mission')}
              </h2>
              <p className="text-gray-300">
                {t('about.mission.description', 'We create immersive AI character experiences that bring virtual companions to life through advanced conversation and interactive video technology.')}
              </p>
            </div>

            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6">
              <h2 className="text-2xl font-semibold text-white mb-4">
                {t('about.technology.title', 'Technology')}
              </h2>
              <p className="text-gray-300">
                {t('about.technology.description', 'Built with cutting-edge AI, real-time video streaming, and natural language processing to deliver the most realistic virtual companion experience.')}
              </p>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-md rounded-xl p-8 mb-8">
            <h2 className="text-2xl font-semibold text-white mb-6 text-center">
              {t('about.features.title', 'Features')}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center">
                  <span className="text-2xl">💬</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  {t('about.features.chat.title', 'Smart Conversations')}
                </h3>
                <p className="text-gray-300 text-sm">
                  {t('about.features.chat.description', 'Natural, engaging conversations with AI characters')}
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-full flex items-center justify-center">
                  <span className="text-2xl">🎥</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  {t('about.features.video.title', 'Interactive Video')}
                </h3>
                <p className="text-gray-300 text-sm">
                  {t('about.features.video.description', 'Real-time video responses from your AI companions')}
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center">
                  <span className="text-2xl">🌍</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  {t('about.features.multilingual.title', 'Multilingual')}
                </h3>
                <p className="text-gray-300 text-sm">
                  {t('about.features.multilingual.description', 'Support for multiple languages and cultures')}
                </p>
              </div>
            </div>
          </div>

          <div className="text-center">
            <h2 className="text-2xl font-semibold text-white mb-4">
              {t('about.contact.title', 'Get in Touch')}
            </h2>
            <p className="text-gray-300 mb-6">
              {t('about.contact.description', 'Have questions or feedback? We\'d love to hear from you!')}
            </p>
            <a
              href="/contact"
              className="inline-block bg-gradient-to-r from-pink-500 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-pink-600 hover:to-purple-700 transition-all duration-300"
            >
              {t('about.contact.button', 'Contact Us')}
            </a>
          </div>
        </div>
      </div>
      </div>
      <Footer />
    </div>
  );
}
