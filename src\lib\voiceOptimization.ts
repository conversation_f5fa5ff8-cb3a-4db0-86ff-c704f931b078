// Voice optimization initialization and management
import { startCacheMaintenance, voiceCache } from './voiceCache';
import { getActiveCharactersWithVoice } from './clientCharacterManager';

interface OptimizationConfig {
  enableWarmup: boolean;
  enableAdvancedCache: boolean;
  enablePreloading: boolean;
  warmupInterval: number;
  cacheMaintenanceInterval: number;
}

const defaultConfig: OptimizationConfig = {
  enableWarmup: true,
  enableAdvancedCache: true,
  enablePreloading: true,
  warmupInterval: 30 * 60 * 1000, // 30 minutes
  cacheMaintenanceInterval: 60 * 60 * 1000 // 1 hour
};

let isInitialized = false;
let optimizationConfig = defaultConfig;

// Initialize voice optimization systems
export function initializeVoiceOptimization(config: Partial<OptimizationConfig> = {}): void {
  if (isInitialized) {
    console.log('Voice optimization already initialized');
    return;
  }

  optimizationConfig = { ...defaultConfig, ...config };
  
  console.log('=== Initializing Voice Optimization Systems ===');
  console.log('Config:', optimizationConfig);

  try {
    // Start cache maintenance
    if (optimizationConfig.enableAdvancedCache) {
      startCacheMaintenance();
      console.log('✅ Advanced cache system started');
    }

    // Start warmup and preloading
    if (optimizationConfig.enableWarmup || optimizationConfig.enablePreloading) {
      setTimeout(() => {
        preloadCommonPhrasesForAllCharacters();
      }, 5000); // Wait 5 seconds after startup
      console.log('✅ Voice warmup and preloading scheduled');
    }

    isInitialized = true;
    console.log('🚀 Voice optimization systems initialized successfully');

  } catch (error) {
    console.error('Failed to initialize voice optimization:', error);
  }
}

// Preload common phrases for all active characters (client-side safe)
async function preloadCommonPhrasesForAllCharacters(): Promise<void> {
  console.log('=== Starting Common Phrases Preloading ===');

  try {
    const activeCharacters = await getActiveCharactersWithVoice();

    if (activeCharacters.length === 0) {
      console.log('No active characters with voice IDs found for preloading');
      return;
    }

    console.log(`Found ${activeCharacters.length} characters for preloading`);
    const preloadPromises: Promise<void>[] = [];

    activeCharacters.forEach(({ id, voiceId }) => {
      // Preload for both languages
      preloadPromises.push(
        voiceCache.preloadCommonPhrases(id, voiceId, 'ja')
          .catch(error => console.warn(`Failed to preload JP phrases for ${id}:`, error))
      );

      preloadPromises.push(
        voiceCache.preloadCommonPhrases(id, voiceId, 'en')
          .catch(error => console.warn(`Failed to preload EN phrases for ${id}:`, error))
      );
    });

    await Promise.allSettled(preloadPromises);
    console.log('✅ Common phrases preloading completed');

    // Log cache statistics
    const stats = voiceCache.getStats();
    console.log('Cache stats after preloading:', stats);

  } catch (error) {
    console.error('Failed to preload common phrases:', error);
  }
}

// Get optimization status
export function getOptimizationStatus(): {
  initialized: boolean;
  config: OptimizationConfig;
  cacheStats: any;
} {
  return {
    initialized: isInitialized,
    config: optimizationConfig,
    cacheStats: isInitialized ? voiceCache.getStats() : null
  };
}

// Manual cache warming for specific character (client-side safe)
export async function warmCharacterCache(characterId: string, language: 'ja' | 'en' = 'ja'): Promise<void> {
  console.log(`=== Manual Cache Warming for ${characterId} (${language}) ===`);

  try {
    const activeCharacters = await getActiveCharactersWithVoice();
    const character = activeCharacters.find(char => char.id === characterId);

    if (!character) {
      throw new Error(`Character ${characterId} not found or missing voice ID`);
    }

    await voiceCache.preloadCommonPhrases(characterId, character.voiceId, language);
    console.log(`✅ Cache warming completed for ${characterId}`);

  } catch (error) {
    console.error(`Failed to warm cache for ${characterId}:`, error);
    throw error;
  }
}

// Performance monitoring
export function logPerformanceMetrics(): void {
  if (!isInitialized) {
    console.log('Voice optimization not initialized');
    return;
  }

  const stats = voiceCache.getStats();
  
  console.log('=== Voice Optimization Performance Metrics ===');
  console.log(`Cache Hit Rate: ${stats.hitRate.toFixed(2)}%`);
  console.log(`Total Requests: ${stats.totalRequests}`);
  console.log(`Cache Hits: ${stats.hits}`);
  console.log(`Cache Misses: ${stats.misses}`);
  console.log(`Cache Size: ${stats.cacheSize} entries`);
  
  // Performance recommendations
  if (stats.hitRate < 30 && stats.totalRequests > 50) {
    console.warn('⚠️  Low cache hit rate detected. Consider preloading more common phrases.');
  }
  
  if (stats.cacheSize > 400) {
    console.warn('⚠️  Cache size is getting large. Consider clearing expired entries.');
  }
  
  if (stats.hitRate > 70) {
    console.log('✅ Excellent cache performance!');
  }
}

// Emergency cache clear
export function emergencyCacheClear(): void {
  console.log('🚨 Emergency cache clear initiated');
  voiceCache.clear();
  console.log('✅ Cache cleared successfully');
}

// Optimization health check
export function healthCheck(): {
  status: 'healthy' | 'warning' | 'error';
  issues: string[];
  recommendations: string[];
} {
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  if (!isInitialized) {
    issues.push('Voice optimization not initialized');
    recommendations.push('Call initializeVoiceOptimization()');
  }
  
  if (isInitialized) {
    const stats = voiceCache.getStats();
    
    if (stats.hitRate < 20 && stats.totalRequests > 100) {
      issues.push('Very low cache hit rate');
      recommendations.push('Increase preloading of common phrases');
    }
    
    if (stats.cacheSize === 0 && stats.totalRequests > 10) {
      issues.push('Cache not being populated');
      recommendations.push('Check cache implementation');
    }
  }
  
  const status = issues.length === 0 ? 'healthy' : 
                 issues.length <= 2 ? 'warning' : 'error';
  
  return { status, issues, recommendations };
}

// Auto-optimization based on usage patterns
export function autoOptimize(): void {
  if (!isInitialized) return;
  
  const stats = voiceCache.getStats();
  
  console.log('=== Auto-Optimization Analysis ===');
  
  // If hit rate is low, trigger more aggressive preloading
  if (stats.hitRate < 40 && stats.totalRequests > 50) {
    console.log('Low hit rate detected, triggering additional preloading');
    preloadCommonPhrasesForAllCharacters();
  }
  
  // If cache is getting full, clear expired entries
  if (stats.cacheSize > 450) {
    console.log('Cache size high, clearing expired entries');
    voiceCache.clearExpired();
  }
  
  console.log('Auto-optimization completed');
}
