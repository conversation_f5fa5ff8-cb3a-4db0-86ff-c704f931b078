# 🎭 キャラクター作成ガイド

このガイドでは、新しいAIキャラクターを作成する手順を説明します。

## 📁 ファイル配置構造

```
public/characters/
├── images/
│   ├── [character-id]-profile.jpg    # プロフィール画像 (512x512px推奨)
│   └── [character-id]-thumb.jpg      # サムネイル画像 (256x256px推奨)
├── videos/
│   ├── [character-id]-idle.mp4       # 待機状態の動画
│   ├── [character-id]-speaking.mp4   # 話している状態の動画
│   └── [character-id]-listening.mp4  # 聞いている状態の動画（オプション）
└── audio/
    └── [character-id]-voice.mp3      # 音声サンプル（オプション）

src/lib/
├── promptManager.ts                   # システムプロンプト管理（セキュア）
└── characterManager.ts               # キャラクターデータ管理
```

## 🔐 セキュリティ重要事項

### システムプロンプトのセキュリティ
- **絶対にクライアントサイドに露出しない**
- `promptManager.ts` はサーバーサイドでのみ実行
- ブラウザの開発者ツールからアクセス不可
- 環境変数やデータベースでの暗号化も推奨

### アクセス制御
- 管理者APIは認証トークンで保護
- メディアファイルは適切な権限設定
- システムプロンプトは管理者のみ編集可能

## 🎨 新しいキャラクター作成手順

### 1. メディアファイルの準備

#### 画像要件
- **プロフィール画像**: 512x512px, JPG/PNG, 1MB以下
- **サムネイル画像**: 256x256px, JPG/PNG, 500KB以下
- 高品質で魅力的なアニメ/イラストスタイル推奨

#### 動画要件
- **解像度**: 1080x1920px (9:16 縦向き)
- **フォーマット**: MP4, H.264エンコード
- **長さ**: 
  - 待機動画: 10-30秒（ループ可能）
  - 話している動画: 5-15秒（ループ可能）
  - 聞いている動画: 5-15秒（ループ可能）
- **ファイルサイズ**: 各5MB以下推奨

### 2. ファイル配置

```bash
# 例: "yuki" というキャラクターの場合
public/characters/images/yuki-profile.jpg
public/characters/images/yuki-thumb.jpg
public/characters/videos/yuki-idle.mp4
public/characters/videos/yuki-speaking.mp4
public/characters/videos/yuki-listening.mp4
public/characters/audio/yuki-voice.mp3
```

### 3. システムプロンプトの作成

`src/lib/promptManager.ts` の `CHARACTER_PROMPTS` に追加：

```typescript
yuki: `You are Yuki, a [character description] with these characteristics:

PERSONALITY:
- [personality trait 1]
- [personality trait 2]
- [personality trait 3]

COMMUNICATION STYLE:
- [communication style 1]
- [communication style 2]

BACKGROUND:
- [background information]

RESPONSE GUIDELINES:
- [specific response guidelines]
- [behavioral instructions]`
```

### 4. キャラクターデータの追加

`src/lib/characterManager.ts` の `CHARACTER_DATABASE` に追加：

```typescript
yuki: {
  id: 'yuki',
  name: {
    en: 'Yuki',
    ja: 'ユキ'
  },
  description: {
    en: 'Character description in English',
    ja: 'キャラクターの日本語説明'
  },
  personality: {
    en: ['Trait1', 'Trait2', 'Trait3'],
    ja: ['特徴1', '特徴2', '特徴3']
  },
  interests: {
    en: ['Interest1', 'Interest2'],
    ja: ['興味1', '興味2']
  },
  age: 25,
  occupation: {
    en: 'Occupation in English',
    ja: '職業の日本語'
  },
  background: {
    en: 'Background story in English',
    ja: 'バックグラウンドストーリーの日本語'
  },
  isActive: true,
  isPremium: false,
  sortOrder: 5,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  media: {
    profileImage: '/characters/images/yuki-profile.jpg',
    thumbnailImage: '/characters/images/yuki-thumb.jpg',
    idleVideo: '/characters/videos/yuki-idle.mp4',
    speakingVideo: '/characters/videos/yuki-speaking.mp4',
    listeningVideo: '/characters/videos/yuki-listening.mp4',
    voiceSample: '/characters/audio/yuki-voice.mp3'
  }
}
```

## 🔧 管理者API使用方法

### 認証
```bash
# 環境変数でトークンを設定
ADMIN_TOKEN=your-secure-admin-token
```

### キャラクター一覧取得
```bash
curl -H "x-admin-token: your-secure-admin-token" \
     http://localhost:3000/api/admin/characters
```

### 新しいキャラクター作成
```bash
curl -X POST \
     -H "Content-Type: application/json" \
     -H "x-admin-token: your-secure-admin-token" \
     -d '{"id":"yuki","name":{"en":"Yuki","ja":"ユキ"},...}' \
     http://localhost:3000/api/admin/characters
```

### キャラクター更新
```bash
curl -X PUT \
     -H "Content-Type: application/json" \
     -H "x-admin-token: your-secure-admin-token" \
     -d '{"characterId":"yuki","isActive":false}' \
     http://localhost:3000/api/admin/characters
```

## 📝 システムプロンプト設計のベストプラクティス

### 1. グローバルプロンプト
- 安全性とエチックスのガイドライン
- 技術的制約の説明
- 一般的な会話ルール

### 2. キャラクター固有プロンプト
- 詳細な性格設定
- 話し方やコミュニケーションスタイル
- バックグラウンドストーリー
- 特定の行動指針

### 3. セキュリティ考慮事項
- プロンプトインジェクション対策
- 不適切なコンテンツの防止
- ユーザープライバシーの保護

## 🚀 デプロイ後の確認

1. **メディアファイルの表示確認**
2. **キャラクター選択画面での表示**
3. **チャット機能の動作確認**
4. **システムプロンプトの適用確認**
5. **多言語対応の確認**

## 🔍 トラブルシューティング

### よくある問題
- メディアファイルが表示されない → パスとファイル名を確認
- システムプロンプトが適用されない → promptManager.ts の設定を確認
- キャラクターが表示されない → isActive フラグを確認

### デバッグ方法
- ブラウザの開発者ツールでネットワークタブを確認
- サーバーログでエラーメッセージを確認
- API レスポンスの内容を確認
