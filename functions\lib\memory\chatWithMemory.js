"use strict";
/**
 * 有料ユーザー限定 - 記憶検索付きチャット応答生成Cloud Function
 * 長期記憶（Supabase）+ 短期記憶（Firestore）を組み合わせたハイブリッド記憶システム
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.chatWithMemory = void 0;
const https_1 = require("firebase-functions/v2/https");
const firebase_functions_1 = require("firebase-functions");
const app_1 = require("firebase-admin/app");
const firestore_1 = require("firebase-admin/firestore");
const supabase_js_1 = require("@supabase/supabase-js");
const vertexai_1 = require("@google-cloud/vertexai");
// Firebase Admin初期化
if (!getApps().length) {
    (0, app_1.initializeApp)();
}
const db = (0, firestore_1.getFirestore)();
// Supabase初期化
const supabaseUrl = process.env.SUPABASE_PROJECT_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = (0, supabase_js_1.createClient)(supabaseUrl, supabaseKey);
// Vertex AI初期化
const vertexAI = new vertexai_1.VertexAI({
    project: process.env.GOOGLE_CLOUD_PROJECT,
    location: 'us-central1'
});
/**
 * 課金状態確認
 */
async function checkUserSubscription(userId) {
    try {
        const userDoc = await db.collection('users').doc(userId).get();
        if (!userDoc.exists) {
            return false;
        }
        const userData = userDoc.data();
        const subscriptionTier = (userData === null || userData === void 0 ? void 0 : userData.subscriptionTier) || 'free';
        return subscriptionTier !== 'free';
    }
    catch (error) {
        firebase_functions_1.logger.error('Error checking user subscription:', error);
        return false;
    }
}
/**
 * テキストをベクトル化
 */
async function generateEmbedding(text) {
    var _a;
    try {
        const model = vertexAI.getGenerativeModel({
            model: 'text-multilingual-embedding-002'
        });
        const result = await model.embedContent(text);
        if (!((_a = result.embedding) === null || _a === void 0 ? void 0 : _a.values)) {
            throw new Error('No embedding values returned');
        }
        return result.embedding.values;
    }
    catch (error) {
        firebase_functions_1.logger.error('Error generating embedding:', error);
        throw error;
    }
}
/**
 * 長期記憶検索（有料ユーザー限定）
 */
async function searchLongTermMemories(userId, characterId, queryEmbedding) {
    try {
        const { data, error } = await supabase.rpc('search_similar_memories', {
            query_embedding: queryEmbedding,
            target_user_id: userId,
            target_character_id: characterId,
            similarity_threshold: 0.7,
            max_results: 5
        });
        if (error) {
            throw error;
        }
        return data || [];
    }
    catch (error) {
        firebase_functions_1.logger.error('Error searching long-term memories:', error);
        return [];
    }
}
/**
 * 短期記憶取得（全ユーザー共通）
 */
async function getShortTermMemories(userId, characterId) {
    try {
        const messagesRef = db.collection('messages')
            .where('userId', '==', userId)
            .where('characterId', '==', characterId)
            .orderBy('createdAt', 'desc')
            .limit(10);
        const snapshot = await messagesRef.get();
        return snapshot.docs.map(doc => {
            const data = doc.data();
            return {
                content: data.content,
                sender: data.sender,
                createdAt: data.createdAt
            };
        }).reverse(); // 時系列順に並び替え
    }
    catch (error) {
        firebase_functions_1.logger.error('Error getting short-term memories:', error);
        return [];
    }
}
/**
 * キャラクター情報取得
 */
async function getCharacterInfo(characterId) {
    try {
        // CharacterServiceから取得（既存の実装を利用）
        const response = await fetch(`${process.env.FUNCTIONS_URL}/api/characters/${characterId}`);
        const data = await response.json();
        if (!data.success) {
            throw new Error(`Character not found: ${characterId}`);
        }
        return data.character;
    }
    catch (error) {
        firebase_functions_1.logger.error('Error getting character info:', error);
        throw error;
    }
}
/**
 * 動的プロンプト生成
 */
function buildPrompt(character, shortTermMemories, longTermMemories, currentMessage, language, isPaidUser) {
    var _a, _b;
    const systemPrompt = ((_a = character.systemPrompt) === null || _a === void 0 ? void 0 : _a[language]) || ((_b = character.systemPrompt) === null || _b === void 0 ? void 0 : _b.en) || '';
    let prompt = `${systemPrompt}\n\n`;
    // 有料ユーザーの場合のみ長期記憶を追加
    if (isPaidUser && longTermMemories.length > 0) {
        prompt += `=== 長期記憶 (Long-term Memory) ===\n`;
        prompt += `以下は過去の重要な会話や出来事です：\n\n`;
        longTermMemories.forEach((memory, index) => {
            prompt += `記憶${index + 1} (重要度: ${memory.importance_score.toFixed(2)}, 類似度: ${memory.similarity.toFixed(2)}):\n`;
            prompt += `${memory.content}\n`;
            if (memory.emotion_context) {
                prompt += `感情的文脈: ${memory.emotion_context}\n`;
            }
            prompt += `\n`;
        });
        prompt += `=== 長期記憶終了 ===\n\n`;
    }
    // 短期記憶（全ユーザー共通）
    if (shortTermMemories.length > 0) {
        prompt += `=== 短期記憶 (Recent Conversation) ===\n`;
        shortTermMemories.forEach(msg => {
            var _a;
            const speaker = msg.sender === 'user' ? 'ユーザー' : ((_a = character.name) === null || _a === void 0 ? void 0 : _a[language]) || 'AI';
            prompt += `${speaker}: ${msg.content}\n`;
        });
        prompt += `=== 短期記憶終了 ===\n\n`;
    }
    // 現在のメッセージ
    prompt += `現在のユーザーメッセージ: ${currentMessage}\n\n`;
    // 応答指示
    if (isPaidUser) {
        prompt += `上記の長期記憶と短期記憶を参考に、自然で一貫性のある応答をしてください。`;
    }
    else {
        prompt += `上記の短期記憶を参考に、自然で一貫性のある応答をしてください。`;
    }
    return prompt;
}
/**
 * AI応答生成
 */
async function generateAIResponse(prompt) {
    try {
        const model = vertexAI.getGenerativeModel({
            model: 'gemini-1.5-pro',
            generationConfig: {
                temperature: 0.8,
                maxOutputTokens: 1000,
            }
        });
        const result = await model.generateContent(prompt);
        const response = result.response;
        return response.text() || 'すみません、応答を生成できませんでした。';
    }
    catch (error) {
        firebase_functions_1.logger.error('Error generating AI response:', error);
        throw error;
    }
}
/**
 * メイン処理：記憶検索付きチャット
 */
exports.chatWithMemory = (0, https_1.onCall)({ cors: true }, async (request) => {
    const { userId, characterId, message, language = 'ja' } = request.data;
    // 入力検証
    if (!userId || !characterId || !message) {
        throw new https_1.HttpsError('invalid-argument', 'Missing required parameters');
    }
    firebase_functions_1.logger.info('Chat with memory request:', {
        userId,
        characterId,
        messageLength: message.length,
        language
    });
    try {
        // 1. 課金状態確認
        const isPaidUser = await checkUserSubscription(userId);
        firebase_functions_1.logger.info(`User ${userId} subscription status:`, {
            isPaid: isPaidUser
        });
        // 2. キャラクター情報取得
        const character = await getCharacterInfo(characterId);
        // 3. 短期記憶取得（全ユーザー共通）
        const shortTermMemories = await getShortTermMemories(userId, characterId);
        let longTermMemories = [];
        // 4. 長期記憶検索（有料ユーザー限定）
        if (isPaidUser) {
            firebase_functions_1.logger.info('Searching long-term memories for paid user');
            const queryEmbedding = await generateEmbedding(message);
            longTermMemories = await searchLongTermMemories(userId, characterId, queryEmbedding);
            firebase_functions_1.logger.info(`Found ${longTermMemories.length} relevant long-term memories`);
        }
        else {
            firebase_functions_1.logger.info('Skipping long-term memory search for free user');
        }
        // 5. 動的プロンプト生成
        const prompt = buildPrompt(character, shortTermMemories, longTermMemories, message, language, isPaidUser);
        // 6. AI応答生成
        const aiResponse = await generateAIResponse(prompt);
        // 7. 応答返却
        return {
            success: true,
            response: aiResponse,
            memoryStats: {
                shortTermCount: shortTermMemories.length,
                longTermCount: longTermMemories.length,
                isPaidUser
            }
        };
    }
    catch (error) {
        firebase_functions_1.logger.error('Error in chat with memory:', error);
        throw new https_1.HttpsError('internal', 'Failed to generate response');
    }
});
//# sourceMappingURL=chatWithMemory.js.map