import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

const STATS_FILE = path.join(process.cwd(), 'data', 'statistics.json');

// Admin authentication check
function isAdmin(request: NextRequest): boolean {
  const adminToken = request.headers.get('x-admin-token');
  const expectedToken = process.env.NEXT_PUBLIC_ADMIN_TOKEN || 'your-secure-admin-token-here-change-this';
  return adminToken === expectedToken;
}

interface MessageStat {
  id: string;
  characterId: string;
  userId: string;
  timestamp: string;
  messageLength: number;
  language: 'ja' | 'en';
  success: boolean;
  error?: string;
  responseTime?: number;
}

interface CharacterStats {
  characterId: string;
  messageCount: number;
  uniqueUsers: number;
  averageMessageLength: number;
  errorCount: number;
  averageResponseTime: number;
}

interface GlobalStats {
  totalMessages: number;
  uniqueUsers: number;
  averageMessageLength: number;
  totalErrors: number;
  averageResponseTime: number;
}

// Load statistics from file
async function loadStatistics(): Promise<MessageStat[]> {
  try {
    const dataDir = path.dirname(STATS_FILE);
    await fs.mkdir(dataDir, { recursive: true });

    const data = await fs.readFile(STATS_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.log('Statistics file not found, starting fresh');
    return [];
  }
}

// Save statistics to file
async function saveStatistics(stats: MessageStat[]): Promise<boolean> {
  try {
    const dataDir = path.dirname(STATS_FILE);
    await fs.mkdir(dataDir, { recursive: true });

    await fs.writeFile(STATS_FILE, JSON.stringify(stats, null, 2), 'utf-8');
    return true;
  } catch (error) {
    console.error('Error saving statistics:', error);
    return false;
  }
}

// Filter stats for last 24 hours
function filterLast24Hours(stats: MessageStat[]): MessageStat[] {
  const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
  return stats.filter(stat => new Date(stat.timestamp) >= twentyFourHoursAgo);
}

// Calculate character statistics
function calculateCharacterStats(stats: MessageStat[], characterId: string): CharacterStats {
  const characterStats = stats.filter(stat => stat.characterId === characterId);
  const uniqueUsers = new Set(characterStats.map(stat => stat.userId)).size;
  const successfulStats = characterStats.filter(stat => stat.success);
  
  return {
    characterId,
    messageCount: characterStats.length,
    uniqueUsers,
    averageMessageLength: successfulStats.length > 0 
      ? Math.round(successfulStats.reduce((sum, stat) => sum + stat.messageLength, 0) / successfulStats.length)
      : 0,
    errorCount: characterStats.filter(stat => !stat.success).length,
    averageResponseTime: successfulStats.length > 0
      ? Math.round(successfulStats.reduce((sum, stat) => sum + (stat.responseTime || 0), 0) / successfulStats.length)
      : 0
  };
}

// Calculate global statistics
function calculateGlobalStats(stats: MessageStat[]): GlobalStats {
  const uniqueUsers = new Set(stats.map(stat => stat.userId)).size;
  const successfulStats = stats.filter(stat => stat.success);
  
  return {
    totalMessages: stats.length,
    uniqueUsers,
    averageMessageLength: successfulStats.length > 0
      ? Math.round(successfulStats.reduce((sum, stat) => sum + stat.messageLength, 0) / successfulStats.length)
      : 0,
    totalErrors: stats.filter(stat => !stat.success).length,
    averageResponseTime: successfulStats.length > 0
      ? Math.round(successfulStats.reduce((sum, stat) => sum + (stat.responseTime || 0), 0) / successfulStats.length)
      : 0
  };
}

// GET - Get statistics
export async function GET(request: NextRequest) {
  if (!isAdmin(request)) {
    return NextResponse.json(
      { success: false, error: 'Unauthorized' },
      { status: 401 }
    );
  }

  try {
    const allStats = await loadStatistics();
    const last24HourStats = filterLast24Hours(allStats);
    
    // Get unique character IDs
    const characterIds = [...new Set(last24HourStats.map(stat => stat.characterId))];
    
    // Calculate stats for each character
    const characterStats = characterIds.map(characterId => 
      calculateCharacterStats(last24HourStats, characterId)
    );
    
    // Calculate global stats
    const globalStats = calculateGlobalStats(last24HourStats);
    
    // Get recent errors for debugging
    const recentErrors = last24HourStats
      .filter(stat => !stat.success && stat.error)
      .slice(-10) // Last 10 errors
      .map(stat => ({
        characterId: stat.characterId,
        timestamp: stat.timestamp,
        error: stat.error,
        userId: stat.userId.substring(0, 8) + '...' // Partial user ID for privacy
      }));

    return NextResponse.json({
      success: true,
      globalStats,
      characterStats,
      recentErrors,
      dataRange: '24 hours'
    });
  } catch (error) {
    console.error('Error getting statistics:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get statistics' },
      { status: 500 }
    );
  }
}

// POST - Record new message statistic
export async function POST(request: NextRequest) {
  try {
    const { 
      characterId, 
      userId, 
      messageLength, 
      language, 
      success, 
      error, 
      responseTime 
    }: {
      characterId: string;
      userId: string;
      messageLength: number;
      language: 'ja' | 'en';
      success: boolean;
      error?: string;
      responseTime?: number;
    } = await request.json();

    const newStat: MessageStat = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      characterId,
      userId,
      timestamp: new Date().toISOString(),
      messageLength,
      language,
      success,
      error,
      responseTime
    };

    const allStats = await loadStatistics();
    allStats.push(newStat);
    
    // Keep only last 1000 records to prevent file from growing too large
    if (allStats.length > 1000) {
      allStats.splice(0, allStats.length - 1000);
    }
    
    await saveStatistics(allStats);

    return NextResponse.json({
      success: true,
      message: 'Statistic recorded'
    });
  } catch (error) {
    console.error('Error recording statistic:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to record statistic' },
      { status: 500 }
    );
  }
}

// Export function to record statistics from other parts of the app
export async function recordMessageStat(
  characterId: string,
  userId: string,
  messageLength: number,
  language: 'ja' | 'en',
  success: boolean,
  error?: string,
  responseTime?: number
): Promise<void> {
  try {
    const newStat: MessageStat = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      characterId,
      userId,
      timestamp: new Date().toISOString(),
      messageLength,
      language,
      success,
      error,
      responseTime
    };

    const allStats = await loadStatistics();
    allStats.push(newStat);
    
    // Keep only last 1000 records
    if (allStats.length > 1000) {
      allStats.splice(0, allStats.length - 1000);
    }
    
    await saveStatistics(allStats);
  } catch (error) {
    console.error('Error recording message stat:', error);
  }
}
