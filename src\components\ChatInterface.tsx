"use client";

import { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import VideoBackground from "./VideoBackground";
import MessageBubble from "./MessageBubble";
import ChatInput from "./ChatInput";
import AuthModal from "./AuthModal";
import { useAuth } from "@/contexts/AuthContext";
import { useI18n } from "@/contexts/I18nProvider";
import {
  getChatHistory,
  UIMessage
} from "@/lib/firebaseService";
import { Character } from "@/types/firebase";


// Use UIMessage type from firebaseService
type Message = UIMessage;

interface ChatInterfaceProps {
  characterId: string;
  language?: 'ja' | 'en';
}

interface VoiceInputStatus {
  isVoiceModeActive: boolean;
  isListening: boolean;
  audioLevel: number;
}

export default function ChatInterface({ characterId, language: propLanguage }: ChatInterfaceProps) {
  const router = useRouter();
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [character, setCharacter] = useState<Character | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [voiceInputStatus, setVoiceInputStatus] = useState<VoiceInputStatus>({
    isVoiceModeActive: false,
    isListening: false,
    audioLevel: 0
  });
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const { user, userData } = useAuth();
  const { t, language: contextLanguage } = useI18n();

  // Use prop language if provided, otherwise use context language
  const language = propLanguage || contextLanguage;



  // Helper function to determine if character uses MiniMax voice provider
  const isMinimaxVoiceProvider = (character: Character | null): boolean => {
    return character?.voiceConfig?.provider === 'minimax' ||
           (character as any)?.voiceProvider === 'minimax';
  };

  // Preload voice system for faster response
  const preloadVoiceSystem = async () => {
    if (!character || !isMinimaxVoiceProvider(character)) {
      console.log('🎵 Skipping preload - not a MiniMax character');
      return;
    }

    // Check if character has MiniMax voice configuration with admin settings priority
    const minimaxVoiceId = character?.voiceConfig?.voiceId ||
                          (character as any)?.customVoiceId ||
                          (character as any)?.minimaxVoiceId;
    if (!minimaxVoiceId) {
      console.log('🎵 Skipping preload - no MiniMax voice ID configured');
      return;
    }

    try {
      console.log('🎵 Preloading MiniMax voice system');
      const { startStreamingAudio } = await import('@/lib/streamingAudio');

      // Preload with a short test phrase
      const testText = language === 'ja' ? 'こんにちは' : 'Hello';
      const voiceId = minimaxVoiceId;

      console.log('🎵 Preloading with voiceId:', voiceId);

      // Start preload but don't destroy immediately to preserve cache
      startStreamingAudio(testText, voiceId, language, characterId, 'minimax')
        .then(player => {
          // Don't destroy immediately - let it stay in cache for actual use
          console.log('🎵 Voice system preloaded successfully');
          // Clean up after a longer delay to allow for actual usage
          setTimeout(() => {
            player.destroy();
          }, 30000); // 30 seconds delay
        })
        .catch(error => {
          console.warn('Voice system preload failed:', error);
          // Don't throw error for preload failures - just log
        });
    } catch (error) {
      console.warn('Voice system preload error:', error);
      // Don't throw error for preload failures
    }
  };

  // Handle MiniMax streaming audio playback
  const handleMinimaxStreamingAudio = async (text: string, voiceId: string, characterId: string, onAudioStart?: () => void) => {
    try {
      console.log('🎯 handleMinimaxStreamingAudio called at', new Date().toISOString());
      // Don't set isSpeaking=true here - wait for actual audio start

      // Use the streaming audio utility
      console.log('🎯 Importing streamingAudio at', new Date().toISOString());
      const { startStreamingAudio } = await import('@/lib/streamingAudio');
      console.log('🎯 streamingAudio imported at', new Date().toISOString());

      console.log('🎯 Calling startStreamingAudio at', new Date().toISOString());
      const player = await startStreamingAudio(
        text,
        voiceId,
        language,
        characterId,
        'minimax',
        onAudioStart
      );
      console.log('🎯 startStreamingAudio returned at', new Date().toISOString());

      // Check if autoplay was blocked and try to start on next user interaction
      if ((window as any).pendingAudioPlayer) {
        const pendingPlayer = (window as any).pendingAudioPlayer;
        delete (window as any).pendingAudioPlayer;

        // Try to start the pending player
        try {
          pendingPlayer.play();
        } catch (error) {
          console.warn('Manual play also failed:', error);
        }
      }

      // Monitor playback state
      let monitoringStarted = false;
      const checkPlaybackEnd = () => {
        if (player.isPlaying && !monitoringStarted) {
          monitoringStarted = true;
        }

        if (monitoringStarted && !player.isPlaying) {
          setIsSpeaking(false);
          player.destroy();
          return;
        }

        setTimeout(checkPlaybackEnd, 200);
      };

      setTimeout(checkPlaybackEnd, 500);

    } catch (error) {
      setIsSpeaking(false);
      throw error;
    }
  };

  // Handle standard audio playback (ElevenLabs, etc.)
  const handleStandardAudioPlayback = async (audioUrl: string) => {
    try {
      setIsSpeaking(true);

      const audio = new Audio();
      audio.preload = 'auto';
      audio.volume = 1.0;

      if (audioUrl.startsWith('data:')) {
        audio.src = audioUrl;

        const playPromise = audio.play();
        if (playPromise !== undefined) {
          await playPromise;
          console.log('🎵 Standard audio playback started');
        }
      } else {
        audio.oncanplaythrough = () => {
          audio.play().catch(err => {
            console.warn('Audio play failed:', err);
            setIsSpeaking(false);
          });
        };
        audio.src = audioUrl;
      }

      audio.onended = () => {
        setIsSpeaking(false);
      };

      audio.onerror = () => {
        setIsSpeaking(false);
      };

    } catch (error) {
      console.error('🚫 Standard audio playback failed:', error);
      setIsSpeaking(false);
      throw error;
    }
  };

  // Load character data and chat history
  useEffect(() => {
    const loadCharacterAndHistory = async () => {
      try {
        setLoading(true);
        console.log('ChatInterface: Loading character with ID:', characterId);

        let characterData = null;

        try {
          // Try to fetch character from API first
          const response = await fetch(`/api/characters?t=${Date.now()}`);
          if (response.ok) {
            const data = await response.json();
            if (data.success && data.characters) {
              const foundCharacter = data.characters.find((char: any) => char.id === characterId);
              if (foundCharacter) {
                characterData = foundCharacter;
                console.log('ChatInterface: Character loaded from API:', characterData);
              }
            }
          }
        } catch (apiError) {
          console.warn('ChatInterface: API fetch failed, trying CharacterService:', apiError);
        }

        // Fallback to CharacterService if API failed
        if (!characterData) {
          try {
            const { CharacterService } = await import('@/lib/characterService');
            CharacterService.clearCache();

            characterData = CharacterService.getCharacter(characterId);
            console.log('ChatInterface: Character data received from service:', characterData);
          } catch (getCharacterError) {
            console.warn('ChatInterface: CharacterService failed, using fallback:', getCharacterError);
          }
        }

        // If still no character data, use fallback
        if (!characterData) {
          // Fallback: Create a basic character object based on characterId
          // SECURITY: systemPrompt is intentionally excluded to prevent client-side exposure
          const fallbackCharacters: { [key: string]: any } = {
            'alice': {
              id: 'alice',
              name: { en: 'Alice', ja: 'アリス' },
              description: { en: 'A friendly AI character', ja: 'フレンドリーなAIキャラクター' },
              profileImage: '/characters/images/alice-profile.jpg',
              aiConfig: {
                model: 'gpt-3.5-turbo',
                temperature: 0.7,
                maxTokens: 500
              },
              voiceProvider: 'elevenlabs',
              voiceConfig: {
                provider: 'elevenlabs',
                voiceId: '1LfCuIldvlGQ7B987Q6L'
              }
            },
            'sakura': {
              id: 'sakura',
              name: { en: 'Sakura', ja: 'さくら' },
              description: { en: 'A cheerful AI companion', ja: '明るいAIコンパニオン' },
              profileImage: '/characters/images/sakura-profile.jpg',
              aiConfig: {
                model: 'gpt-3.5-turbo',
                temperature: 0.8,
                maxTokens: 500
              },
              voiceProvider: 'elevenlabs',
              voiceConfig: {
                provider: 'elevenlabs',
                voiceId: '1LfCuIldvlGQ7B987Q6L'
              }
            }
          };

          characterData = fallbackCharacters[characterId] || {
            id: characterId,
            name: { en: 'AI Character', ja: 'AIキャラクター' },
            description: { en: 'A friendly AI character', ja: 'フレンドリーなAIキャラクター' },
            profileImage: '/characters/images/default-profile.jpg',
            // SECURITY: systemPrompt is intentionally excluded to prevent client-side exposure
            aiConfig: {
              model: 'gpt-3.5-turbo',
              temperature: 0.7,
              maxTokens: 500
            },
            voiceProvider: 'elevenlabs',
            voiceConfig: {
              provider: 'elevenlabs',
              voiceId: '1LfCuIldvlGQ7B987Q6L'
            }
          };

          console.log('ChatInterface: Using fallback character data:', characterData);
        }

        if (characterData) {
          setCharacter(characterData);
          console.log('ChatInterface: Character set successfully');

          // Preload voice system for MiniMax characters
          setTimeout(() => preloadVoiceSystem(), 1000);

          // Load chat history if user is authenticated
          if (user) {
            try {
              const history = await getChatHistory(user.uid, characterId);
              if (history.length > 0) {
                setMessages(history);
                return; // Don't add welcome message if we have history
              }
            } catch (error) {
              console.error('Error loading chat history:', error);
              // Continue to show welcome message
            }
          }
        } else {
          // Character not found, show error instead of redirecting
          console.error('ChatInterface: Character not found');
          setError(`Character with ID "${characterId}" not found`);
        }
      } catch (error) {
        console.error('ChatInterface: Error loading character:', error);
        setError(`Error loading character: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } finally {
        setLoading(false);
      }
    };

    if (characterId) {
      loadCharacterAndHistory();
    } else {
      console.error('ChatInterface: No characterId provided');
      setError('No character ID provided');
    }
  }, [characterId, user]);

  useEffect(() => {
    if (!character || messages.length > 0) return;

    // Add welcome message only if no messages exist
    const characterName = typeof character.name === 'object'
      ? (character.name[language] || character.name.en || 'Unknown')
      : String(character.name);

    const characterDesc = typeof character.description === 'object'
      ? (character.description[language] || character.description.en || 'AI Companion')
      : String(character.description);

    // Use custom welcome message if available, otherwise use default
    let welcomeContent = '';
    if (character.welcomeMessage && character.welcomeMessage[language]) {
      welcomeContent = character.welcomeMessage[language];
    } else if (character.welcomeMessage && character.welcomeMessage.en) {
      // Fallback to English if current language not available
      welcomeContent = character.welcomeMessage.en;
    } else {
      // Default welcome message
      welcomeContent = language === 'ja'
        ? `こんにちは！私は${characterName}です。${characterDesc}。今日はいかがですか？`
        : `Hello! I'm ${characterName}. ${characterDesc}. How are you today?`;
    }

    const welcomeMessage: Message = {
      id: "welcome",
      content: welcomeContent,
      sender: "ai",
      timestamp: new Date()
    };
    setMessages([welcomeMessage]);
  }, [character, language, messages.length]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleSendMessage = async (content: string) => {
    console.log('ChatInterface: handleSendMessage called');
    console.log('ChatInterface: content:', content);
    console.log('ChatInterface: user:', !!user);
    console.log('ChatInterface: userData:', userData);
    console.log('ChatInterface: user points:', userData?.points);

    if (content.trim() === "") {
      console.log('ChatInterface: Empty content, returning');
      return;
    }

    // Check if user is authenticated
    if (!user) {
      console.log('ChatInterface: User not authenticated, showing auth modal');
      setShowAuthModal(true);
      return;
    }

    // Check if email is verified for email/password users
    if (user.providerData.some(provider => provider.providerId === 'password') && !user.emailVerified) {
      console.log('ChatInterface: Email not verified, blocking message');
      const errorMessage = language === 'ja'
        ? 'メッセージを送信するには、まずメールアドレスの確認を完了してください。ご登録のメールアドレスをご確認いただき、認証リンクをクリックしてください。'
        : 'Please verify your email address before sending messages. Check your email and click the verification link.';

      setError(errorMessage);

      // Auto-clear error after 8 seconds
      setTimeout(() => {
        setError(null);
      }, 8000);

      return;
    }

    // Check if user has enough points
    const currentPoints = userData?.points || 0;
    console.log('ChatInterface: Current points:', currentPoints);
    if (currentPoints < 1) {
      console.log('ChatInterface: Insufficient points, showing alert');
      alert(language === 'ja' ? 'ポイントが足りません！翌日に50ポイント追加されます。' : "You're out of points! 50 points will be added tomorrow.");
      return;
    }

    console.log('ChatInterface: All checks passed, proceeding with message');

    // Add user message immediately (instant display)
    const userMessage: Message = {
      id: Date.now().toString(),
      content: content.trim(),
      sender: "user",
      timestamp: new Date()
    };

    // Start typing indicator immediately
    setIsTyping(true);

    // Send message to chat API and get AI response
    try {
      // Use current messages state for conversation history (before adding new user message)
      const conversationHistory = messages.slice(-10).map(msg => ({
        role: msg.sender === 'user' ? 'user' : 'assistant',
        content: msg.content
      }));

      const requestData = {
        message: content.trim(),
        characterId,
        userId: user.uid,
        userName: userData?.displayName || 'User',
        language,
        conversationHistory
      };

      // Add user message to UI after preparing request data
      setMessages(prev => [...prev, userMessage]);

      console.log('Sending message to API:', requestData.message);

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      });

      console.log('API response status:', response.status);

      if (response.ok) {
        const data = await response.json();

        if (data.success) {
          // Check if character uses MiniMax streaming
          if (isMinimaxVoiceProvider(character)) {
            console.log('🎯 Starting MiniMax audio at', new Date().toISOString());
            // Keep typing indicator until audio starts

            // Get dynamic voice ID from character configuration with admin settings priority
            const voiceId = character?.voiceConfig?.voiceId ||
                           (character as any)?.customVoiceId ||
                           (character as any)?.minimaxVoiceId ||
                           'Friendly_Person';

            console.log('🎯 Using dynamic voice ID:', voiceId);

            // Start audio streaming with callback to display text when audio starts
            handleMinimaxStreamingAudio(data.response, voiceId, characterId, () => {
              console.log('🎯 Audio started - syncing all elements at', new Date().toISOString());
              // Sync all elements when audio actually starts
              setIsTyping(false);  // Stop typing indicator
              setIsSpeaking(true);  // Start Lip-Sync Video

              // Display text simultaneously
              const aiMessage: Message = {
                id: Date.now().toString() + '_ai',
                content: data.response,
                sender: "ai",
                timestamp: new Date()
              };
              setMessages(prev => [...prev, aiMessage]);
            }).catch(() => {
              setIsTyping(false);
              setIsSpeaking(false);
            });
            return;
          }

          // For other characters, stop typing immediately
          setIsTyping(false);

          // For other characters, display text immediately
          const aiMessage: Message = {
            id: Date.now().toString() + '_ai',
            content: data.response,
            sender: "ai",
            timestamp: new Date()
          };
          setMessages(prev => [...prev, aiMessage]);

          // Standard audio playback for other characters
          if (data.audioUrl) {
            try {
              await handleStandardAudioPlayback(data.audioUrl);
            } catch (audioError) {
              setIsSpeaking(false);
            }
          } else {
            // No audio, simulate speaking duration
            const estimatedDuration = Math.max(2000, data.response.length * 50);
            setIsSpeaking(true);
            setTimeout(() => {
              setIsSpeaking(false);
            }, estimatedDuration);
          }
        } else {
          setIsTyping(false);
          throw new Error(data.error || 'Failed to get AI response');
        }
      } else {
        setIsTyping(false);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error sending message:', error);

      // Stop typing indicator
      setIsTyping(false);

      // Enhanced fallback with context-aware responses
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: generateContextualResponse(content, language),
        sender: "ai",
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiMessage]);

      // Simulate speaking duration for fallback
      setIsSpeaking(true);
      setTimeout(() => {
        setIsSpeaking(false);
      }, 2000);

      // Show user-friendly error message
      console.log('Using offline mode due to API connection issues');
    }
  };

  const generateContextualResponse = (userMessage: string, lang: string): string => {
    const message = userMessage.toLowerCase();

    // Context-aware responses based on user input
    if (lang === 'ja') {
      if (message.includes('こんにちは') || message.includes('はじめまして') || message.includes('hi')) {
        return "こんにちは！お会いできて嬉しいです。今日はどんなお話をしましょうか？";
      } else if (message.includes('ありがとう')) {
        return "どういたしまして！他にも何かお手伝いできることがあれば、遠慮なくおっしゃってくださいね。";
      } else if (message.includes('元気') || message.includes('調子')) {
        return "ありがとうございます！私はとても元気です。あなたはいかがですか？";
      } else if (message.includes('好き') || message.includes('趣味')) {
        return "それは素敵ですね！私もそういうお話を聞くのが大好きです。もっと詳しく教えてください。";
      } else {
        const responses = [
          "それは本当に興味深いですね！もっと詳しく教えてください。",
          "あなたの気持ちがわかります。私はここで聞いています。",
          "それは素晴らしいですね！もっと聞きたいです。",
          "あなたはとても思いやりがありますね。シェアしてくれてありがとうございます。",
          "それは良い質問ですね！ちょっと考えさせてください...",
          "なるほど、面白いお話ですね。続きを聞かせてください。",
          "そのことについて、どう感じていらっしゃいますか？",
          "とても魅力的なお話ですね。もう少し詳しく教えてください。"
        ];
        return responses[Math.floor(Math.random() * responses.length)];
      }
    } else {
      if (message.includes('hello') || message.includes('hi')) {
        return "Hello! It's wonderful to meet you! How are you doing today?";
      } else if (message.includes('thank')) {
        return "You're very welcome! I'm always here if you need anything else.";
      } else if (message.includes('how are you')) {
        return "I'm doing great, thank you for asking! How about you?";
      } else if (message.includes('like') || message.includes('love')) {
        return "That sounds wonderful! I'd love to hear more about what you enjoy.";
      } else {
        const responses = [
          "That's really interesting! Tell me more about that.",
          "I understand how you feel. I'm here to listen.",
          "That sounds wonderful! I'd love to hear more.",
          "You're so thoughtful. I appreciate you sharing that with me.",
          "That's a great question! Let me think about that...",
          "How fascinating! Please continue with your story.",
          "How do you feel about that?",
          "That's such an intriguing topic. Could you elaborate?"
        ];
        return responses[Math.floor(Math.random() * responses.length)];
      }
    }
  };



  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white">
            {language === 'ja' ? '読み込み中' : 'Loading'} {characterId}...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <div className="text-center bg-white p-8 rounded-lg shadow-lg max-w-md">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Error Loading Character</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <div className="space-y-2">
            <button
              onClick={() => {
                setError(null);
                setLoading(true);
                // Retry loading
                window.location.reload();
              }}
              className="w-full bg-blue-500 text-white px-6 py-2 rounded-md hover:bg-blue-600 transition-colors mb-2"
            >
              Retry
            </button>
            <button
              onClick={() => {
                const homeUrl = language === 'ja' ? '/ja' : '/';
                router.push(homeUrl);
              }}
              className="w-full bg-gray-500 text-white px-6 py-2 rounded-md hover:bg-gray-600 transition-colors"
            >
              Go Back Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!character) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">{t('characterNotFound')}</h1>
          <button
            onClick={() => {
              const homeUrl = language === 'ja' ? '/ja' : '/';
              router.push(homeUrl);
            }}
            className="bg-primary text-white px-6 py-2 rounded-md hover:bg-blue-600 transition-colors"
          >
            {t('goBackHome')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="chat-container">
      {/* Full-screen Video Background */}
      <VideoBackground
        character={character}
        isSpeaking={isSpeaking}
      />



      {/* Chat Overlay */}
      <div className="chat-overlay">
        {/* Header */}
        <div className="chat-header">
          <div className="flex items-center justify-between h-full px-4">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => {
                  const homeUrl = language === 'ja' ? '/ja' : '/';
                  router.push(homeUrl);
                }}
                className="text-white hover:text-gray-300 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <div>
                <h1 className="text-lg font-semibold text-white">
                  {typeof character.name === 'object'
                    ? (character.name[language] || character.name.en || 'Unknown')
                    : String(character.name)
                  }
                </h1>
                <p className="text-sm text-white/80">{t('online')}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-white/80">{t('points')}</p>
              <p className="text-lg font-semibold text-white">
                {user ? (userData?.points || 0) : '--'}
              </p>
            </div>
          </div>
        </div>

        {/* Live Chat Messages Area */}
        <div className="chat-messages-area">
          <div className="live-chat-messages">
            {messages.map((message) => (
              <MessageBubble
                key={message.id}
                message={message}
                characterName={character?.name[language] || character?.name.en}
              />
            ))}
            {isTyping && (
              <div className="message-bubble ai fade-in-up">
                <div className="sender-name">{character?.name[language] || character?.name.en || 'AI'}</div>
                <div className="flex items-center gap-2">
                  <span className="text-sm opacity-80">
                    {language === 'ja' ? '入力中' : 'typing'}
                  </span>
                  <div className="typing-indicator">
                    <div className="typing-dot"></div>
                    <div className="typing-dot"></div>
                    <div className="typing-dot"></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Voice Input Animation - Bottom Position */}
        {voiceInputStatus.isListening && (
          <div className="fixed bottom-20 left-1/2 transform -translate-x-1/2 z-50 pointer-events-none">
            <div className="bg-black/50 rounded-full px-6 py-3 backdrop-blur-sm">
              <div className="flex items-center justify-center space-x-2">
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse animation-delay-100"></div>
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse animation-delay-200"></div>
              </div>
              <div className="text-white text-center mt-2 text-xs">
                {language === 'ja' ? '音声入力中...' : 'Listening...'}
              </div>
            </div>
          </div>
        )}

        {/* Chat Input Area */}
        <div className="chat-input-area">
          <div className="flex items-center h-full px-4">
            <ChatInput
              onSendMessage={handleSendMessage}
              disabled={!!user && (userData?.points || 0) < 1}
              onVoiceStatusChange={setVoiceInputStatus}
              isSpeaking={isSpeaking}
            />
          </div>
        </div>
      </div>

      {/* Error Snackbar */}
      {error && (
        <div className="fixed bottom-4 left-4 right-4 z-50 max-w-md mx-auto">
          <div className="bg-red-500 text-white px-4 py-3 rounded-lg shadow-lg flex items-start justify-between">
            <div className="flex items-start space-x-3">
              <svg className="w-5 h-5 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-sm leading-relaxed">{error}</p>
            </div>
            <button
              onClick={() => setError(null)}
              className="ml-2 text-white/80 hover:text-white transition-colors flex-shrink-0"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        mode="signup"
      />
    </div>
  );
}
