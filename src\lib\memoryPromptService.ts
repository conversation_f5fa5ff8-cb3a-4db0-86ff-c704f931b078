/**
 * 有料ユーザー限定ハイブリッド記憶システム - プロンプトテンプレート
 * subscriptionTierに応じて動的にプロンプトを生成
 */

export interface LongTermMemory {
  id: string;
  content: string;
  importance_score: number;
  emotion_context?: string;
  conversation_context?: any;
  tags?: string[];
  similarity: number;
  created_at: string;
}

export interface ShortTermMessage {
  content: string;
  sender: 'user' | 'ai';
  createdAt: any;
}

export interface CharacterData {
  id: string;
  name: {
    en: string;
    ja: string;
  };
  systemPrompt: {
    en: string;
    ja: string;
  };
  personality: {
    en: string[];
    ja: string[];
  };
}

export interface PromptContext {
  character: CharacterData;
  currentMessage: string;
  language: 'ja' | 'en';
  shortTermMemories: ShortTermMessage[];
  longTermMemories?: LongTermMemory[]; // 有料ユーザーのみ
  isPaidUser: boolean;
  userName?: string;
}

/**
 * 記憶セクションのフォーマット
 */
export class MemoryPromptFormatter {
  
  /**
   * 長期記憶セクション生成（有料ユーザー限定）
   */
  static formatLongTermMemories(
    memories: LongTermMemory[],
    language: 'ja' | 'en'
  ): string {
    if (!memories || memories.length === 0) {
      return '';
    }

    const sectionTitle = language === 'ja' 
      ? '=== 🧠 長期記憶 (重要な過去の出来事) ===' 
      : '=== 🧠 Long-term Memory (Important Past Events) ===';
    
    const intro = language === 'ja'
      ? 'あなたが覚えている重要な過去の会話や出来事：'
      : 'Important past conversations and events you remember:';

    let section = `${sectionTitle}\n${intro}\n\n`;

    memories.forEach((memory, index) => {
      const memoryLabel = language === 'ja' ? '記憶' : 'Memory';
      const importanceLabel = language === 'ja' ? '重要度' : 'Importance';
      const relevanceLabel = language === 'ja' ? '関連度' : 'Relevance';
      
      section += `${memoryLabel} ${index + 1}:\n`;
      section += `📝 ${memory.content}\n`;
      section += `⭐ ${importanceLabel}: ${(memory.importance_score * 100).toFixed(0)}%\n`;
      section += `🔗 ${relevanceLabel}: ${(memory.similarity * 100).toFixed(0)}%\n`;
      
      if (memory.emotion_context) {
        const emotionLabel = language === 'ja' ? '感情的文脈' : 'Emotional Context';
        section += `💭 ${emotionLabel}: ${memory.emotion_context}\n`;
      }
      
      if (memory.tags && memory.tags.length > 0) {
        const tagsLabel = language === 'ja' ? 'タグ' : 'Tags';
        section += `🏷️ ${tagsLabel}: ${memory.tags.join(', ')}\n`;
      }
      
      section += `📅 ${new Date(memory.created_at).toLocaleDateString()}\n\n`;
    });

    const endMarker = language === 'ja' 
      ? '=== 長期記憶終了 ===' 
      : '=== End of Long-term Memory ===';
    
    section += `${endMarker}\n\n`;
    return section;
  }

  /**
   * 短期記憶セクション生成（全ユーザー共通）
   */
  static formatShortTermMemories(
    memories: ShortTermMessage[],
    characterName: string,
    language: 'ja' | 'en'
  ): string {
    if (!memories || memories.length === 0) {
      return '';
    }

    const sectionTitle = language === 'ja' 
      ? '=== 💬 最近の会話 (短期記憶) ===' 
      : '=== 💬 Recent Conversation (Short-term Memory) ===';

    let section = `${sectionTitle}\n`;

    memories.forEach(msg => {
      const speaker = msg.sender === 'user' 
        ? (language === 'ja' ? 'ユーザー' : 'User')
        : characterName;
      
      section += `${speaker}: ${msg.content}\n`;
    });

    const endMarker = language === 'ja' 
      ? '=== 最近の会話終了 ===' 
      : '=== End of Recent Conversation ===';
    
    section += `${endMarker}\n\n`;
    return section;
  }

  /**
   * 課金状態に応じた指示セクション生成
   */
  static formatInstructions(
    isPaidUser: boolean,
    language: 'ja' | 'en'
  ): string {
    if (isPaidUser) {
      return language === 'ja'
        ? `📋 応答指示:
上記の長期記憶と短期記憶の両方を活用して、一貫性があり、過去の出来事を適切に参照した自然な応答をしてください。
重要度の高い記憶や関連度の高い記憶を優先的に参考にしてください。
ユーザーとの関係性の深さを感じられる、人間らしい応答を心がけてください。`
        : `📋 Response Instructions:
Use both long-term and short-term memories to provide consistent, natural responses that appropriately reference past events.
Prioritize memories with higher importance and relevance scores.
Aim for human-like responses that reflect the depth of your relationship with the user.`;
    } else {
      return language === 'ja'
        ? `📋 応答指示:
上記の最近の会話を参考に、自然で一貫性のある応答をしてください。
会話の流れを大切にし、ユーザーとの関係性を意識した応答を心がけてください。`
        : `📋 Response Instructions:
Use the recent conversation above to provide natural and consistent responses.
Value the flow of conversation and be mindful of your relationship with the user.`;
    }
  }
}

/**
 * メインプロンプト生成クラス
 */
export class MemoryPromptService {
  
  /**
   * 課金状態に応じた動的プロンプト生成
   */
  static buildDynamicPrompt(context: PromptContext): string {
    const {
      character,
      currentMessage,
      language,
      shortTermMemories,
      longTermMemories,
      isPaidUser,
      userName
    } = context;

    // 1. システムプロンプト
    const systemPrompt = character.systemPrompt[language] || character.systemPrompt.en;
    let prompt = `${systemPrompt}\n\n`;

    // 2. ユーザー情報（あれば）
    if (userName) {
      const userLabel = language === 'ja' ? 'ユーザー名' : 'User Name';
      prompt += `👤 ${userLabel}: ${userName}\n\n`;
    }

    // 3. 課金状態表示（デバッグ用、本番では削除可能）
    if (process.env.NODE_ENV === 'development') {
      const statusLabel = language === 'ja' ? '記憶システム状態' : 'Memory System Status';
      const status = isPaidUser 
        ? (language === 'ja' ? '有料プラン（長期記憶有効）' : 'Paid Plan (Long-term Memory Enabled)')
        : (language === 'ja' ? '無料プラン（短期記憶のみ）' : 'Free Plan (Short-term Memory Only)');
      
      prompt += `🔧 ${statusLabel}: ${status}\n\n`;
    }

    // 4. 長期記憶セクション（有料ユーザー限定）
    if (isPaidUser && longTermMemories && longTermMemories.length > 0) {
      prompt += MemoryPromptFormatter.formatLongTermMemories(longTermMemories, language);
    }

    // 5. 短期記憶セクション（全ユーザー共通）
    if (shortTermMemories && shortTermMemories.length > 0) {
      const characterName = character.name[language] || character.name.en;
      prompt += MemoryPromptFormatter.formatShortTermMemories(
        shortTermMemories,
        characterName,
        language
      );
    }

    // 6. 現在のメッセージ
    const currentLabel = language === 'ja' ? '現在のメッセージ' : 'Current Message';
    prompt += `💬 ${currentLabel}: ${currentMessage}\n\n`;

    // 7. 応答指示
    prompt += MemoryPromptFormatter.formatInstructions(isPaidUser, language);

    return prompt;
  }

  /**
   * 記憶統計情報生成
   */
  static generateMemoryStats(context: PromptContext) {
    return {
      isPaidUser: context.isPaidUser,
      shortTermCount: context.shortTermMemories?.length || 0,
      longTermCount: context.longTermMemories?.length || 0,
      totalMemories: (context.shortTermMemories?.length || 0) + (context.longTermMemories?.length || 0),
      memorySystemActive: context.isPaidUser && (context.longTermMemories?.length || 0) > 0
    };
  }

  /**
   * プロンプトサイズ計算（トークン数概算）
   */
  static estimateTokenCount(prompt: string): number {
    // 簡易的なトークン数計算（実際のトークナイザーを使用することを推奨）
    return Math.ceil(prompt.length / 4);
  }

  /**
   * プロンプト最適化（長すぎる場合の調整）
   */
  static optimizePrompt(
    context: PromptContext,
    maxTokens: number = 3000
  ): { prompt: string; truncated: boolean } {
    let prompt = this.buildDynamicPrompt(context);
    let truncated = false;

    if (this.estimateTokenCount(prompt) > maxTokens) {
      // 長期記憶を削減
      if (context.longTermMemories && context.longTermMemories.length > 3) {
        const optimizedContext = {
          ...context,
          longTermMemories: context.longTermMemories.slice(0, 3)
        };
        prompt = this.buildDynamicPrompt(optimizedContext);
        truncated = true;
      }

      // それでも長い場合は短期記憶を削減
      if (this.estimateTokenCount(prompt) > maxTokens && context.shortTermMemories && context.shortTermMemories.length > 5) {
        const optimizedContext = {
          ...context,
          shortTermMemories: context.shortTermMemories.slice(-5)
        };
        prompt = this.buildDynamicPrompt(optimizedContext);
        truncated = true;
      }
    }

    return { prompt, truncated };
  }
}
