import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import { doc, updateDoc, addDoc, collection, getDoc, increment } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { createTimestamp } from '@/lib/firebaseService';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export async function POST(request: NextRequest) {
  try {
    if (!stripe || !db) {
      return NextResponse.json(
        { error: 'Services not configured' },
        { status: 500 }
      );
    }

    const body = await request.text();
    const signature = request.headers.get('stripe-signature')!;

    let event;

    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }

    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object;
        await handleSuccessfulPayment(session);
        break;

      case 'payment_intent.payment_failed':
        const paymentIntent = event.data.object;
        await handleFailedPayment(paymentIntent);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function handleSuccessfulPayment(session: any) {
  try {
    if (!db) {
      throw new Error('Firebase not initialized');
    }

    const { userId, packageId, points } = session.metadata;
    const pointsToAdd = parseInt(points);

    // Update user points in Firebase
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      points: increment(pointsToAdd),
      updatedAt: createTimestamp(),
    });

    // Create point transaction record
    await addDoc(collection(db, 'pointTransactions'), {
      userId,
      type: 'purchase',
      amount: pointsToAdd,
      packageId,
      stripeSessionId: session.id,
      status: 'completed',
      createdAt: createTimestamp(),
      metadata: {
        paymentAmount: session.amount_total,
        currency: session.currency,
      },
    });

    console.log(`Successfully added ${pointsToAdd} points to user ${userId}`);
  } catch (error) {
    console.error('Error handling successful payment:', error);
    throw error;
  }
}

async function handleFailedPayment(paymentIntent: any) {
  try {
    // Log failed payment for monitoring
    console.error('Payment failed:', {
      paymentIntentId: paymentIntent.id,
      lastPaymentError: paymentIntent.last_payment_error,
    });

    // You could also create a failed transaction record here
    // or send notification to user about failed payment
  } catch (error) {
    console.error('Error handling failed payment:', error);
    throw error;
  }
}
