/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'picsum.photos',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'commondatastorage.googleapis.com',
        port: '',
        pathname: '/gtv-videos-bucket/**',
      },
      {
        protocol: 'https',
        hostname: 'firebasestorage.googleapis.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'storage.googleapis.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '*.googleusercontent.com',
        port: '',
        pathname: '/**',
      },
    ],
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  serverExternalPackages: ['firebase-admin'],
  // Exclude functions directory from TypeScript checking
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    dirs: ['src'], // Only lint src directory, not functions
  },
  // Security: Block access to sensitive files
  async headers() {
    return [
      {
        source: '/data/:path*',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow, nosnippet, noarchive',
          },
        ],
      },
    ];
  },
  async rewrites() {
    return [
      // Firebase Auth action handler - ensure proper routing
      {
        source: '/auth/action',
        destination: '/auth/action',
      },
      // Block direct access to data directory
      {
        source: '/data/:path*',
        destination: '/404',
      },
      // Block direct access to admin files in production
      {
        source: '/admin/:path*',
        destination: process.env.NODE_ENV === 'production' ? '/404' : '/admin/:path*',
      },
    ];
  },
  // Webpack configuration for Firebase compatibility
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
      };

      // Exclude server-side only modules from client bundle
      config.resolve.alias = {
        ...config.resolve.alias,
        '@/lib/server': false,
        '@/lib/secretManager': false,
        '@google-cloud/secret-manager': false,
      };
    }

    // Exclude functions directory from webpack processing
    config.externals = config.externals || [];
    if (isServer) {
      config.externals.push({
        'firebase-functions': 'firebase-functions',
        'firebase-admin': 'firebase-admin',
      });
    }

    return config;
  },
};

module.exports = nextConfig;
