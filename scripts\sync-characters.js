#!/usr/bin/env node

/**
 * Character Synchronization Script
 * 
 * This script synchronizes character data between local development and production.
 * It ensures that character information is properly synced while keeping
 * admin interfaces local-only.
 */

const fs = require('fs');
const path = require('path');

// Paths
const DATA_DIR = path.join(process.cwd(), 'data');
const CHARACTERS_FILE = path.join(DATA_DIR, 'characters.json');
const PUBLIC_CHARACTERS_DIR = path.join(process.cwd(), 'public', 'characters');

/**
 * Ensure directories exist
 */
function ensureDirectories() {
  if (!fs.existsSync(DATA_DIR)) {
    fs.mkdirSync(DATA_DIR, { recursive: true });
  }
  if (!fs.existsSync(PUBLIC_CHARACTERS_DIR)) {
    fs.mkdirSync(PUBLIC_CHARACTERS_DIR, { recursive: true });
  }
}

/**
 * Load characters from file
 */
function loadCharacters() {
  try {
    if (fs.existsSync(CHARACTERS_FILE)) {
      const data = fs.readFileSync(CHARACTERS_FILE, 'utf8');
      return JSON.parse(data);
    }
    return {};
  } catch (error) {
    console.error('Error loading characters:', error);
    return {};
  }
}

/**
 * Save characters to file
 */
function saveCharacters(characters) {
  try {
    ensureDirectories();
    fs.writeFileSync(CHARACTERS_FILE, JSON.stringify(characters, null, 2));
    console.log('✅ Characters saved successfully');
    return true;
  } catch (error) {
    console.error('❌ Error saving characters:', error);
    return false;
  }
}

/**
 * Sanitize character data for production
 * Removes sensitive information that shouldn't be exposed
 */
function sanitizeCharacterData(characters) {
  const sanitized = {};
  
  for (const [id, character] of Object.entries(characters)) {
    sanitized[id] = {
      id: character.id,
      name: character.name,
      description: character.description,
      age: character.age,
      occupation: character.occupation,
      personality: character.personality,
      background: character.background,
      welcomeMessage: character.welcomeMessage,
      media: character.media,
      aiProvider: character.aiProvider,
      aiModel: character.aiModel,
      elevenLabsVoiceId: character.elevenLabsVoiceId,
      videoSets: character.videoSets,
      createdAt: character.createdAt,
      updatedAt: character.updatedAt
    };
  }
  
  return sanitized;
}

/**
 * Sync character data for deployment
 */
function syncForDeployment() {
  console.log('🔄 Syncing characters for deployment...');
  
  const characters = loadCharacters();
  const sanitizedCharacters = sanitizeCharacterData(characters);
  
  // Save sanitized data
  if (saveCharacters(sanitizedCharacters)) {
    console.log(`✅ Synced ${Object.keys(sanitizedCharacters).length} characters`);
    
    // List synced characters
    for (const [id, character] of Object.entries(sanitizedCharacters)) {
      console.log(`   - ${character.name.en} (${id})`);
    }
  }
}

/**
 * Validate character data
 */
function validateCharacters() {
  console.log('🔍 Validating character data...');
  
  const characters = loadCharacters();
  const issues = [];
  
  for (const [id, character] of Object.entries(characters)) {
    // Check required fields
    if (!character.name?.en || !character.name?.ja) {
      issues.push(`${id}: Missing name (en/ja)`);
    }
    
    if (!character.media?.profileImage) {
      issues.push(`${id}: Missing profile image`);
    }
    
    if (!character.aiProvider || !character.aiModel) {
      issues.push(`${id}: Missing AI configuration`);
    }
  }
  
  if (issues.length > 0) {
    console.log('⚠️  Validation issues found:');
    issues.forEach(issue => console.log(`   - ${issue}`));
  } else {
    console.log('✅ All characters validated successfully');
  }
  
  return issues.length === 0;
}

/**
 * Main execution
 */
function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'sync':
      syncForDeployment();
      break;
    case 'validate':
      validateCharacters();
      break;
    case 'list':
      const characters = loadCharacters();
      console.log('📋 Current characters:');
      for (const [id, character] of Object.entries(characters)) {
        console.log(`   - ${character.name?.en || 'Unknown'} (${id})`);
      }
      break;
    default:
      console.log(`
Usage: node scripts/sync-characters.js <command>

Commands:
  sync      - Sync characters for deployment
  validate  - Validate character data
  list      - List all characters

Examples:
  node scripts/sync-characters.js sync
  node scripts/sync-characters.js validate
      `);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  loadCharacters,
  saveCharacters,
  sanitizeCharacterData,
  syncForDeployment,
  validateCharacters
};
