import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";
import { I18nProvider } from "@/contexts/I18nProvider";
import { VoiceOptimizationProvider } from "@/components/VoiceOptimizationProvider";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "AiLuvChat - AI Character Chat",
  description: "Interactive AI character chat experience with virtual boyfriends and girlfriends",
  keywords: "AI chat, virtual girlfriend, virtual boyfriend, AI character, interactive chat",
  icons: {
    icon: '/icon.svg',
    shortcut: '/favicon.ico',
    apple: '/icon.svg',
  },
  openGraph: {
    title: "AiLuvChat - AI Character Chat",
    description: "Interactive AI character chat experience",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className} suppressHydrationWarning>
        <VoiceOptimizationProvider>
          <I18nProvider>
            <AuthProvider>
              {children}
            </AuthProvider>
          </I18nProvider>
        </VoiceOptimizationProvider>
      </body>
    </html>
  );
}
