# Firebase を活用した実装アプローチ

## キャラクター表示制御

1. **Firestore ルール設定**
   ```javascript
   // firestore.rules
   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {
       match /characters/{characterId} {
         // 管理者のみが全キャラクターにアクセス可能
         allow read: if request.auth != null && request.auth.token.admin == true;
         // 一般ユーザーはアクティブなキャラクターのみ閲覧可能
         allow read: if request.auth != null && resource.data.isActive == true;
       }
     }
   }
   ```

2. **Cloud Functions でのフィルタリング**
   - クライアントからの直接アクセスではなく、Cloud Functions経由でキャラクター取得
   - アクティブフラグによるフィルタリングをサーバーサイドで実施

## システムプロンプト保護

1. **Firestore 構造設計**
   - プロンプト情報を別コレクションに分離
   - 管理者のみアクセス可能なセキュリティルール設定

2. **Cloud Functions による処理**
   - チャットリクエストはすべてCloud Functions経由
   - プロンプトはCloud Functions内部でのみ参照
   - クライアントには応答テキストのみ返却

## 動画リソース保護

1. **Firebase Storage セキュリティ**
   - 動画ファイルへの直接アクセスを禁止
   - Cloud Functions経由での署名付きURL生成

2. **署名付きURL生成**
   - 短時間（数分）有効な署名付きURLをCloud Functionsで生成
   - セッションごとに異なるURLを提供

3. **動画アクセス制御**
   - 再生開始時にCloud Functionsで認証確認
   - 認証済みユーザーにのみ署名付きURLを提供