"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateOptimizedVoice = void 0;
// import * as functions from 'firebase-functions';
const admin = __importStar(require("firebase-admin"));
// Redis互換のキャッシュ（Firebase Extensions等で実装）またはFirestoreキャッシュ
const voiceCache = new Map();
const CACHE_TTL = 24 * 60 * 60 * 1000; // 24時間キャッシュ
// 音声生成の一貫性向上
const generateOptimizedVoice = async (text, characterId, language = 'en') => {
    var _a;
    try {
        // 1. キャラクター情報取得
        const characterDoc = await admin.firestore().collection('characters').doc(characterId).get();
        if (!characterDoc.exists) {
            throw new Error('Character not found');
        }
        const character = characterDoc.data();
        const voiceId = (_a = character === null || character === void 0 ? void 0 : character.voiceConfig) === null || _a === void 0 ? void 0 : _a.voiceId;
        if (!voiceId) {
            throw new Error('Voice configuration not found');
        }
        // 2. キャッシュチェック - 高速化のため
        const cacheKey = `voice_${voiceId}_${language}_${text.substring(0, 100)}`;
        const cachedVoice = await checkVoiceCache(cacheKey);
        if (cachedVoice) {
            console.log('Voice cache hit:', cacheKey);
            return {
                success: true,
                audioUrl: cachedVoice,
                duration: 0 // キャッシュからは正確な長さは取得できない
            };
        }
        // 3. 音声生成 - 一貫したアプローチ
        console.log(`Generating voice for character ${characterId}, language: ${language}`);
        const startTime = Date.now();
        // ElevenLabs API呼び出し - 最適化されたパラメータ
        const apiKey = process.env.ELEVENLABS_API_KEY;
        const modelId = 'eleven_turbo_v2_5'; // 最速モデル
        const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voiceId}/stream`, {
            method: 'POST',
            headers: {
                'Accept': 'audio/mpeg',
                'Content-Type': 'application/json',
                'xi-api-key': apiKey || ''
            },
            body: JSON.stringify({
                text: text,
                model_id: modelId,
                voice_settings: {
                    stability: 0.3,
                    similarity_boost: 0.3,
                    style: 0.0,
                    use_speaker_boost: false
                },
                optimize_streaming_latency: 4, // 最大最適化
                output_format: "mp3_22050_32" // 最速フォーマット
            })
        });
        if (!response.ok) {
            throw new Error(`ElevenLabs API error: ${response.status}`);
        }
        // 4. 音声データ処理
        const audioBuffer = await response.arrayBuffer();
        // 5. Firebase Storageに保存
        const fileName = `voices/${characterId}/${Date.now()}.mp3`;
        const file = admin.storage().bucket().file(fileName);
        await file.save(Buffer.from(audioBuffer), {
            metadata: { contentType: 'audio/mpeg' }
        });
        // 6. 署名付きURL生成
        const [signedUrl] = await file.getSignedUrl({
            action: 'read',
            expires: Date.now() + 60 * 60 * 1000 // 1時間
        });
        // 7. キャッシュに保存
        await saveToVoiceCache(cacheKey, signedUrl);
        const duration = Date.now() - startTime;
        console.log(`Voice generated in ${duration}ms`);
        return {
            success: true,
            audioUrl: signedUrl,
            duration: duration
        };
    }
    catch (error) {
        console.error('Error generating voice:', error);
        return {
            success: false,
            audioUrl: '',
            duration: 0
        };
    }
};
exports.generateOptimizedVoice = generateOptimizedVoice;
// キャッシュチェック関数
async function checkVoiceCache(key) {
    // メモリキャッシュチェック
    if (voiceCache.has(key)) {
        const cached = voiceCache.get(key);
        if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
            return cached.url;
        }
        // 期限切れは削除
        voiceCache.delete(key);
    }
    // Firestoreキャッシュチェック（オプション）
    try {
        const doc = await admin.firestore().collection('voiceCache').doc(key).get();
        if (doc.exists) {
            const data = doc.data();
            if (data && Date.now() - data.timestamp < CACHE_TTL) {
                // メモリキャッシュに追加
                voiceCache.set(key, { url: data.url, timestamp: data.timestamp });
                return data.url;
            }
            // 期限切れは削除
            await doc.ref.delete();
        }
    }
    catch (err) {
        console.warn('Firestore cache check error:', err);
    }
    return null;
}
// キャッシュ保存関数
async function saveToVoiceCache(key, url) {
    const timestamp = Date.now();
    // メモリキャッシュに保存
    if (voiceCache.size >= 100) {
        // キャッシュサイズ制限
        const oldestKey = [...voiceCache.entries()]
            .sort((a, b) => a[1].timestamp - b[1].timestamp)[0][0];
        voiceCache.delete(oldestKey);
    }
    voiceCache.set(key, { url, timestamp });
    // Firestoreにも保存（オプション）
    try {
        await admin.firestore().collection('voiceCache').doc(key).set({
            url,
            timestamp,
            expiresAt: timestamp + CACHE_TTL
        });
    }
    catch (err) {
        console.warn('Firestore cache save error:', err);
    }
}
//# sourceMappingURL=voiceService.js.map