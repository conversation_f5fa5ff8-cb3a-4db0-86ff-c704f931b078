import { SecretManagerServiceClient } from '@google-cloud/secret-manager';

// Initialize Secret Manager client
let secretClient: SecretManagerServiceClient | null = null;

function getSecretClient(): SecretManagerServiceClient {
  if (!secretClient) {
    secretClient = new SecretManagerServiceClient();
  }
  return secretClient;
}

// Cache for secrets to avoid repeated API calls
const secretCache = new Map<string, { value: string; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Get secret from Google Cloud Secret Manager
 * @param secretName - Name of the secret (e.g., 'OPENROUTER_API_KEY')
 * @param projectId - GCP Project ID (defaults to 'ailuvchat')
 * @returns Secret value or null if not found
 */
export async function getSecret(secretName: string, projectId: string = 'ailuvchat'): Promise<string | null> {
  try {
    // Check cache first
    const cached = secretCache.get(secretName);
    if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
      console.log(`Secret ${secretName}: Using cached value`);
      return cached.value;
    }

    // Fallback to environment variable first (for local development)
    const envValue = process.env[secretName];
    if (envValue) {
      console.log(`Secret ${secretName}: Found in environment variables`);
      secretCache.set(secretName, { value: envValue, timestamp: Date.now() });
      return envValue;
    }

    // Try to get from Secret Manager
    console.log(`Secret ${secretName}: Attempting to fetch from Secret Manager`);
    
    const client = getSecretClient();
    const name = `projects/${projectId}/secrets/${secretName}/versions/latest`;
    
    const [version] = await client.accessSecretVersion({ name });
    const secretValue = version.payload?.data?.toString();
    
    if (secretValue) {
      console.log(`Secret ${secretName}: Successfully retrieved from Secret Manager`);
      secretCache.set(secretName, { value: secretValue, timestamp: Date.now() });
      return secretValue;
    }

    console.warn(`Secret ${secretName}: Not found in Secret Manager`);
    return null;

  } catch (error) {
    console.error(`Error accessing secret ${secretName}:`, error);
    
    // Final fallback to environment variable
    const envValue = process.env[secretName];
    if (envValue) {
      console.log(`Secret ${secretName}: Using environment variable as fallback`);
      return envValue;
    }
    
    return null;
  }
}

/**
 * Get multiple secrets at once
 * @param secretNames - Array of secret names
 * @param projectId - GCP Project ID
 * @returns Object with secret names as keys and values as values
 */
export async function getSecrets(secretNames: string[], projectId: string = 'ailuvchat'): Promise<Record<string, string | null>> {
  const results: Record<string, string | null> = {};
  
  await Promise.all(
    secretNames.map(async (secretName) => {
      results[secretName] = await getSecret(secretName, projectId);
    })
  );
  
  return results;
}

/**
 * Clear secret cache (useful for testing or forced refresh)
 */
export function clearSecretCache(): void {
  secretCache.clear();
  console.log('Secret cache cleared');
}

/**
 * Get API key for specific provider with Secret Manager support
 * @param provider - AI provider name
 * @returns API key or null
 */
export async function getAPIKey(provider: string): Promise<string | null> {
  console.log(`=== API KEY DEBUG for ${provider} ===`);

  let secretName: string;
  
  switch (provider) {
    case 'openrouter.ai':
      secretName = 'OPENROUTER_API_KEY';
      break;
    case 'venice.ai':
      secretName = 'VENICE_API_KEY';
      break;
    case 'openai':
      secretName = 'OPENAI_API_KEY';
      break;
    case 'anthropic':
      secretName = 'ANTHROPIC_API_KEY';
      break;
    case 'google':
      secretName = 'GOOGLE_AI_API_KEY';
      break;
    case 'cohere':
      secretName = 'COHERE_API_KEY';
      break;
    case 'mistral':
      secretName = 'MISTRAL_API_KEY';
      break;
    case 'together':
      secretName = 'TOGETHER_API_KEY';
      break;
    case 'replicate':
      secretName = 'REPLICATE_API_TOKEN';
      break;
    case 'huggingface':
      secretName = 'HUGGINGFACE_API_KEY';
      break;
    default:
      console.warn(`Unknown provider: ${provider}`);
      return null;
  }

  const apiKey = await getSecret(secretName);
  
  console.log(`Provider: ${provider}`);
  console.log(`API Key Available: ${apiKey ? 'YES' : 'NO'}`);
  console.log(`API Key Length: ${apiKey ? apiKey.length : 0}`);
  console.log(`API Key Preview: ${apiKey ? apiKey.substring(0, 10) + '...' : 'N/A'}`);

  // Log environment variables for debugging if key not found
  if (!apiKey) {
    console.log('=== ENVIRONMENT VARIABLES DEBUG ===');
    console.log('NODE_ENV:', process.env.NODE_ENV);
    console.log('Available env vars:', Object.keys(process.env).filter(key =>
      key.includes('API') || key.includes('KEY') || key.includes('SECRET')
    ));
  }

  return apiKey;
}
