rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Helper function to check if user is admin
    function isAdmin() {
      return request.auth != null && request.auth.token.admin == true;
    }

    // Character images - public read for authenticated users
    match /characters/{characterId}/images/{imageFile} {
      allow read: if request.auth != null;
      allow write: if isAdmin();
    }

    // Character videos - restricted access (use signed URLs)
    match /characters/{characterId}/videos/{videoFile} {
      allow read: if false; // Access only through signed URLs
      allow write: if isAdmin();
    }

    // Character video sets - restricted access (use signed URLs)
    match /characters/video-sets/{characterId}/{videoFile} {
      allow read: if false; // Access only through signed URLs
      allow write: if isAdmin();
    }

    // Generated voice files - read-only for authenticated users
    match /voice/{characterId}/{voiceFile} {
      allow read: if request.auth != null;
      allow write: if false; // Only server can generate voice files
    }

    // User profile images
    match /users/{userId}/profile/{imageFile} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null; // Others can view profile images
    }

    // Video segments for HLS streaming
    match /videos/{characterId}/{videoType}/{segment} {
      allow read: if request.auth != null;
      allow write: if false; // Only admin can upload videos
    }

    // HLS manifests
    match /videos/{characterId}/{videoType}/playlist.m3u8 {
      allow read: if request.auth != null;
      allow write: if false; // Only admin can upload manifests
    }

    // Temporary files (auto-delete after 24 hours)
    match /temp/{userId}/{tempFile} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Data export files (GDPR compliance)
    match /exports/{userId}/{exportFile} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if false; // Only server can generate exports
    }

    // Default deny all other access
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
