"use client";

import { useState, useRef, useEffect } from "react";
import { useI18n } from "@/contexts/I18nProvider";

interface VoiceInputStatus {
  isVoiceModeActive: boolean;
  isListening: boolean;
  audioLevel: number;
}

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  onVoiceStatusChange?: (status: VoiceInputStatus) => void;
  isSpeaking?: boolean;
}

export default function ChatInput({ onSendMessage, disabled = false, onVoiceStatusChange, isSpeaking = false }: ChatInputProps) {
  const [message, setMessage] = useState("");
  const [isListening, setIsListening] = useState(false);
  const [voiceModeActive, setVoiceModeActive] = useState(false);

  // Debug logging for component initialization
  console.log('ChatInput: Component initialized', {
    onSendMessage: typeof onSendMessage,
    disabled,
    hasOnSendMessage: !!onSendMessage
  });

  // Use refs to track current state values in callbacks
  const voiceModeActiveRef = useRef(false);
  const isSpeakingRef = useRef(false);
  const isListeningRef = useRef(false);

  const [speechSupported, setSpeechSupported] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const { t, language } = useI18n();
  // Authentication and points are handled by ChatInterface

  // Update refs when state changes
  useEffect(() => {
    voiceModeActiveRef.current = voiceModeActive;
  }, [voiceModeActive]);

  useEffect(() => {
    isSpeakingRef.current = isSpeaking;
  }, [isSpeaking]);

  useEffect(() => {
    isListeningRef.current = isListening;
  }, [isListening]);

  useEffect(() => {
    // Check if speech recognition is supported
    if (typeof window !== 'undefined') {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      if (SpeechRecognition) {
        setSpeechSupported(true);
        recognitionRef.current = new SpeechRecognition();
        recognitionRef.current.continuous = true;
        recognitionRef.current.interimResults = false;
        recognitionRef.current.lang = language === 'ja' ? 'ja-JP' : 'en-US';

        recognitionRef.current.onresult = (event: SpeechRecognitionEvent) => {
          const transcript = event.results[event.results.length - 1][0].transcript.trim();
          console.log('Speech recognition result:', transcript);

          // Auto-send the voice input immediately
          const maxLength = language === 'en' ? 100 : 50;
          if (transcript && transcript.length > 0 && transcript.length <= maxLength) {
            console.log('Auto-sending voice input:', transcript);
            // Use a ref to get the current onSendMessage function
            const currentOnSendMessage = onSendMessage;
            if (currentOnSendMessage) {
              currentOnSendMessage(transcript);
            }
            setMessage(""); // Clear input after sending
          } else if (transcript.length > maxLength) {
            console.log('Voice input too long, setting in text field:', transcript);
            setMessage(transcript); // Set in text field if too long
          }
        };

        recognitionRef.current.onerror = (event) => {
          if (event.error !== 'no-speech') {
            console.error('Speech recognition error:', event.error);
          }
          setIsListening(false);
        };

        recognitionRef.current.onend = () => {
          console.log('Speech recognition ended, voiceModeActive:', voiceModeActiveRef.current, 'isSpeaking:', isSpeakingRef.current);
          setIsListening(false);

          // Auto-restart if voice mode is still active and not speaking
          if (voiceModeActiveRef.current && !isSpeakingRef.current) {
            setTimeout(() => {
              if (recognitionRef.current && voiceModeActiveRef.current && !isSpeakingRef.current) {
                try {
                  console.log('Attempting to restart voice recognition...');
                  recognitionRef.current.start();
                  console.log('Voice recognition auto-restarted successfully');
                } catch (error) {
                  if (error instanceof Error && error.name === 'InvalidStateError') {
                    console.log('Voice recognition already started during auto-restart, ignoring error');
                  } else {
                    console.error('Failed to restart voice recognition:', error);
                  }
                }
              } else {
                console.log('Not restarting voice recognition - voiceModeActive:', voiceModeActiveRef.current, 'isSpeaking:', isSpeakingRef.current);
              }
            }, 1000); // Increased delay to 1 second
          }
        };

        recognitionRef.current.onstart = () => {
          setIsListening(true);
        };
      }
    }

    return () => {
      // Cleanup speech recognition
      if (recognitionRef.current) {
        recognitionRef.current.stop();
        recognitionRef.current = null;
      }
    };
  }, [language]); // Removed onSendMessage dependency to prevent re-initialization

  // Notify parent component of voice status changes
  useEffect(() => {
    if (onVoiceStatusChange) {
      onVoiceStatusChange({
        isVoiceModeActive: false,
        isListening,
        audioLevel: 0
      });
    }
  }, [isListening, onVoiceStatusChange]);

  const handleSubmit = async (e: React.FormEvent) => {
    console.log('ChatInput: handleSubmit called');
    console.log('ChatInput: message:', message);
    console.log('ChatInput: disabled:', disabled);
    console.log('ChatInput: onSendMessage function:', typeof onSendMessage);
    e.preventDefault();

    if (!message.trim() || disabled) {
      console.log('ChatInput: Submission blocked - empty message or disabled');
      return;
    }

    // Check character limit (English: 100, Japanese: 50)
    const maxLength = language === 'en' ? 100 : 50;
    if (message.trim().length > maxLength) {
      alert(t('messageTooLong'));
      return;
    }

    // Authentication check is handled by ChatInterface
    // ChatInput focuses on input validation and UI

    // Points check is handled by ChatInterface
    // ChatInput focuses on input validation and UI

    // Store message and clear input immediately
    const messageToSend = message.trim();
    setMessage("");

    // Reset textarea height immediately
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }

    // Immediately restore focus after clearing
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        console.log('Focus restored immediately in ChatInput');
      }
    }, 0);

    // Send the message immediately (optimistic update)
    console.log('ChatInput: Calling onSendMessage');
    onSendMessage(messageToSend);

    // Additional focus restoration
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        console.log('Additional focus restoration in ChatInput');
      }
    }, 10);

    // Focus restoration after message send
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus();
        console.log('Focus restoration in ChatInput');
      }
    }, 10);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      console.log('ChatInput: Enter key pressed');
      console.log('ChatInput: Message:', message);
      console.log('ChatInput: Disabled:', disabled);
      console.log('ChatInput: Over limit:', isOverLimit);
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);

    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };



  // Stop voice input when AI is speaking, restart when AI stops speaking
  useEffect(() => {
    if (isSpeaking && isListening && recognitionRef.current) {
      recognitionRef.current.stop();
      console.log('Voice input stopped because AI is speaking');
    } else if (!isSpeaking && voiceModeActive && !isListening && recognitionRef.current) {
      // Restart voice recognition when AI stops speaking and voice mode is active
      const timeoutId = setTimeout(() => {
        // Check if recognition is not already running
        if (recognitionRef.current && voiceModeActiveRef.current && !isSpeakingRef.current && !isListening) {
          try {
            recognitionRef.current.start();
            console.log('Voice recognition restarted after AI finished speaking');
          } catch (error) {
            if (error instanceof Error && error.name === 'InvalidStateError') {
              console.log('Voice recognition already started, ignoring error');
            } else {
              console.error('Failed to restart voice recognition after AI speech:', error);
            }
          }
        }
      }, 1500); // Wait 1.5 seconds after AI stops speaking

      // Cleanup timeout if component unmounts or dependencies change
      return () => clearTimeout(timeoutId);
    }
  }, [isSpeaking, isListening, voiceModeActive]);

  // Update voice status for parent component
  useEffect(() => {
    if (onVoiceStatusChange) {
      onVoiceStatusChange({
        isVoiceModeActive: voiceModeActive,
        isListening: isListening,
        audioLevel: 0
      });
    }
  }, [voiceModeActive, isListening, onVoiceStatusChange]);

  const toggleVoiceInput = async () => {
    if (!speechSupported || disabled || isSpeaking) {
      console.log('Voice input toggle blocked:', { speechSupported, disabled, isSpeaking });
      return;
    }

    try {
      if (voiceModeActive) {
        // Turn off voice mode completely
        console.log('Turning off voice mode');
        setVoiceModeActive(false);
        if (recognitionRef.current) {
          recognitionRef.current.stop();
          console.log('Voice mode turned off');
        }
      } else {
        // Turn on voice mode
        console.log('Turning on voice mode');
        setVoiceModeActive(true);
        if (recognitionRef.current) {
          recognitionRef.current.start();
          console.log('Voice mode turned on');
        }
      }
    } catch (error) {
      console.error('Failed to toggle voice input:', error);
      setVoiceModeActive(false);
    }
  };





  const characterCount = message.length;
  const maxLength = language === 'en' ? 100 : 50; // English: 100 chars, Japanese: 50 chars
  const isOverLimit = characterCount > maxLength;

  return (
    <div className="w-full">
      {disabled && (
        <div className="mb-2 mx-4 p-2 bg-red-900/80 border border-red-500/50 rounded-md text-center backdrop-blur-sm">
          <p className="text-red-200 text-sm font-medium">{t('outOfPoints')}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="flex items-center space-x-2 px-4">
        <div className="flex-1">
          <div className="relative">
            <textarea
              ref={textareaRef}
              value={message}
              onChange={handleTextareaChange}
              onKeyDown={handleKeyDown}
              placeholder={disabled ? t('needPointsToSend') : t('typeMessage')}
              disabled={false} // Keep input unlocked during AI response
              className={`w-full px-3 py-2 pr-16 bg-black/60 border border-white/20 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:bg-gray-900/60 disabled:cursor-not-allowed text-white placeholder-white/60 backdrop-blur-sm transition-colors ${
                isOverLimit ? 'border-red-500' : 'border-white/20'
              }`}
              rows={1}
              style={{ minHeight: '40px', maxHeight: '80px' }}
            />

            {/* Character counter */}
            <div className={`absolute bottom-1 right-2 text-xs ${
              isOverLimit ? 'text-red-400' : characterCount > (maxLength * 0.8) ? 'text-orange-400' : 'text-white/60'
            }`}>
              {characterCount}/{maxLength}
            </div>
          </div>
        </div>

        {/* Voice input button */}
        {speechSupported && (
          <button
            type="button"
            onClick={toggleVoiceInput}
            disabled={disabled}
            className={`p-2 rounded-lg transition-colors backdrop-blur-sm relative ${
              voiceModeActive
                ? 'bg-red-500 text-white'
                : disabled
                ? 'bg-gray-900/60 text-gray-500'
                : 'bg-white/20 text-white hover:bg-white/30'
            }`}
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2a3 3 0 0 1 3 3v6a3 3 0 0 1-6 0V5a3 3 0 0 1 3-3z"/>
              <path d="M19 10v1a7 7 0 0 1-14 0v-1a1 1 0 0 1 2 0v1a5 5 0 0 0 10 0v-1a1 1 0 0 1 2 0z"/>
              <path d="M12 18.5a1 1 0 0 1 1 1V22a1 1 0 0 1-2 0v-2.5a1 1 0 0 1 1-1z"/>
              <path d="M8 22h8a1 1 0 0 1 0 2H8a1 1 0 0 1 0-2z"/>
            </svg>
            {voiceModeActive && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-400 rounded-full animate-pulse"></div>
            )}
          </button>
        )}



        {/* Send button */}
        <button
          type="submit"
          tabIndex={-1}
          onMouseDown={(e) => {
            e.preventDefault(); // Prevent button from taking focus
          }}
          onFocus={(e) => {
            e.preventDefault();
            e.target.blur(); // Force blur if somehow focused
            if (textareaRef.current) {
              textareaRef.current.focus();
            }
          }}
          onClick={() => {
            console.log('Send button clicked!');
            console.log('Button disabled:', disabled || !message.trim() || isOverLimit);
            console.log('Props disabled:', disabled);
            console.log('Message empty:', !message.trim());
            console.log('Over limit:', isOverLimit);
            console.log('Message:', message);
          }}
          disabled={disabled || !message.trim() || isOverLimit}
          className={`p-2 rounded-lg transition-colors backdrop-blur-sm ${
            disabled || !message.trim() || isOverLimit
              ? 'bg-gray-900/60 text-gray-500'
              : 'bg-primary/80 text-white hover:bg-primary'
          }`}
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
          </svg>
        </button>
      </form>

      {/* Voice input status */}
      {isListening && (
        <div className="mt-1 text-center">
          <p className="text-sm text-white/80">{language === 'ja' ? '聞いています...' : 'Listening...'}</p>
        </div>
      )}


    </div>
  );
}
