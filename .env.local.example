# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# AI API Keys for Character Responses
# OpenRouter.ai - Access to multiple AI models via unified API
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Venice.ai - Privacy-focused AI platform
VENICE_API_KEY=your_venice_api_key_here

# OpenAI - GPT models directly from OpenAI
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic - Claude models from Anthropic
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google AI - Gemini models from Google
GOOGLE_AI_API_KEY=your_google_ai_api_key_here

# Cohere - Command models from Cohere
COHERE_API_KEY=your_cohere_api_key_here

# Mistral AI - Mistral models
MISTRAL_API_KEY=your_mistral_api_key_here

# Together AI - Open source models via Together
TOGETHER_API_KEY=your_together_api_key_here

# Replicate - Various models via Replicate
REPLICATE_API_TOKEN=your_replicate_api_token_here

# Hugging Face - Open source models via HF Inference
HUGGINGFACE_API_KEY=your_huggingface_api_key_here

# ElevenLabs - Voice synthesis
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# Admin Configuration
NEXT_PUBLIC_ADMIN_TOKEN=your-secure-admin-token-here-change-this

# Development
NODE_ENV=development

# =============================================================================
# AI API Keys Setup Guide
# =============================================================================
#
# 1. OpenRouter.ai (Recommended for multiple models)
#    - Sign up: https://openrouter.ai/
#    - Get API key: https://openrouter.ai/keys
#    - Models: x-ai/grok-3-mini-beta, deepseek/deepseek-r1-0528-qwen3-8b:free
#
# 2. Venice.ai (Privacy-focused)
#    - Sign up: https://venice.ai/
#    - Get API key from dashboard
#    - Models: claude-3-sonnet, gpt-4o, llama-3.1-405b
#
# 3. OpenAI (Direct access to GPT models)
#    - Sign up: https://platform.openai.com/
#    - Get API key: https://platform.openai.com/api-keys
#    - Models: gpt-4o, gpt-4o-mini, gpt-4-turbo
#
# 4. Anthropic (Claude models)
#    - Sign up: https://console.anthropic.com/
#    - Get API key from console
#    - Models: claude-3-5-sonnet-20241022, claude-3-5-haiku-20241022
#
# 5. Google AI (Gemini models)
#    - Sign up: https://aistudio.google.com/
#    - Get API key from AI Studio
#    - Models: gemini-2.0-flash-exp, gemini-1.5-pro
#
# 6. Cohere (Command models)
#    - Sign up: https://cohere.ai/
#    - Get API key from dashboard
#    - Models: command-r-plus, command-r
#
# 7. Mistral AI
#    - Sign up: https://console.mistral.ai/
#    - Get API key from console
#    - Models: mistral-large-2, mistral-medium
#
# 8. Together AI (Open source models)
#    - Sign up: https://together.ai/
#    - Get API key from dashboard
#    - Models: meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo
#
# 9. Replicate (Various models)
#    - Sign up: https://replicate.com/
#    - Get API token from account settings
#    - Models: meta/meta-llama-3.1-405b-instruct
#
# 10. Hugging Face (HF Inference)
#     - Sign up: https://huggingface.co/
#     - Get API key: https://huggingface.co/settings/tokens
#     - Models: microsoft/DialoGPT-large
#
# 11. ElevenLabs (Voice synthesis)
#     - Sign up: https://elevenlabs.io/
#     - Get API key from profile settings
#     - Used for character voice generation
#
# =============================================================================
