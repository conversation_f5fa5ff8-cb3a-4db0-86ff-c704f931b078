"use client";

import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useI18n } from "@/contexts/I18nProvider";
import { stripePromise, POINT_PACKAGES, formatPrice, isStripeConfigured } from "@/lib/stripe";

interface PointsPurchaseModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function PointsPurchaseModal({ isOpen, onClose }: PointsPurchaseModalProps) {
  const [loading, setLoading] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
  const { user } = useAuth();
  const { t, language } = useI18n();

  if (!isOpen) return null;

  const handlePurchase = async (packageId: string) => {
    if (!user) {
      alert(language === 'ja' ? 'ログインが必要です' : 'Please log in first');
      return;
    }

    if (!isStripeConfigured()) {
      alert(language === 'ja' ? '決済機能は現在利用できません' : 'Payment functionality is currently unavailable');
      return;
    }

    setLoading(true);
    setSelectedPackage(packageId);

    try {
      // Create checkout session
      const response = await fetch('/api/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          packageId,
          userId: user.uid,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create checkout session');
      }

      const { sessionId } = await response.json();

      // Redirect to Stripe Checkout
      const stripe = await stripePromise;
      if (stripe) {
        const { error } = await stripe.redirectToCheckout({ sessionId });
        if (error) {
          console.error('Stripe redirect error:', error);
          alert(language === 'ja' ? '決済エラーが発生しました' : 'Payment error occurred');
        }
      } else {
        throw new Error('Stripe not available');
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      alert(language === 'ja' ? '決済の準備中にエラーが発生しました' : 'Error preparing payment');
    } finally {
      setLoading(false);
      setSelectedPackage(null);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl mx-auto max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-2xl font-semibold text-gray-800">
            {language === 'ja' ? 'ポイント購入' : 'Purchase Points'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="text-center mb-8">
            <h3 className="text-lg font-medium text-gray-800 mb-2">
              {language === 'ja' ? 'チャットポイントを購入して会話を楽しもう！' : 'Purchase chat points to enjoy conversations!'}
            </h3>
            <p className="text-gray-600">
              {language === 'ja'
                ? '1メッセージ = 10ポイント。お得なパッケージをお選びください。'
                : '1 message = 10 points. Choose the best package for you.'
              }
            </p>
          </div>

          {/* Point Packages */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {POINT_PACKAGES.map((pkg) => (
              <div
                key={pkg.id}
                className={`relative border rounded-lg p-6 text-center transition-all hover:shadow-lg ${
                  pkg.popular
                    ? 'border-primary bg-primary/5 ring-2 ring-primary'
                    : 'border-gray-200 hover:border-primary'
                }`}
              >
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                      {language === 'ja' ? '人気' : 'Popular'}
                    </span>
                  </div>
                )}

                <div className="mb-4">
                  <h4 className="text-lg font-semibold text-gray-800 mb-1">
                    {pkg.name}
                  </h4>
                  <div className="text-3xl font-bold text-primary mb-2">
                    {pkg.points.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">
                    {language === 'ja' ? 'ポイント' : 'points'}
                  </div>
                </div>

                <div className="mb-4">
                  <div className="text-2xl font-bold text-gray-800">
                    {formatPrice(pkg.price)}
                  </div>
                  <div className="text-sm text-gray-600">
                    {(pkg.price / pkg.points * 10 / 100).toFixed(3)}¢ {language === 'ja' ? '/メッセージ' : '/message'}
                  </div>
                </div>

                <p className="text-sm text-gray-600 mb-6">
                  {pkg.description}
                </p>

                <button
                  onClick={() => handlePurchase(pkg.id)}
                  disabled={loading}
                  className={`w-full py-3 rounded-lg font-medium transition-colors ${
                    pkg.popular
                      ? 'bg-primary text-white hover:bg-blue-600'
                      : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                  } disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  {loading && selectedPackage === pkg.id ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                      {language === 'ja' ? '処理中...' : 'Processing...'}
                    </div>
                  ) : (
                    language === 'ja' ? '購入する' : 'Purchase'
                  )}
                </button>
              </div>
            ))}
          </div>

          {/* Security Notice */}
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-center text-sm text-gray-600">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              {language === 'ja'
                ? 'Stripeによる安全な決済処理'
                : 'Secure payment processing by Stripe'
              }
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
