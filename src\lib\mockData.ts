import { Character } from '@/types/firebase';

// Mock character data for development
export const mockCharacters: Character[] = [
  {
    id: 'sakura',
    name: {
      en: 'Sakura',
      ja: 'さくら'
    },
    description: {
      en: 'A cheerful and energetic AI companion who loves anime and gaming.',
      ja: '明るく元気なAIコンパニオンで、アニメとゲームが大好きです。'
    },
    personality: {
      en: 'Cheerful, energetic, playful, and loves to share her interests in anime and gaming.',
      ja: '明るく、元気で、遊び心があり、アニメやゲームの趣味を共有するのが好きです。'
    },
    welcomeMessage: {
      en: "Hello there! I'm Sakura. The cherry blossoms are beautiful today, aren't they? How can I help you find peace and beauty in your day?",
      ja: "こんにちは！私はさくらです。今日の桜はとても美しいですね。あなたの一日に平和と美しさを見つけるお手伝いをさせてください。"
    },
    // SECURITY: systemPrompt removed to prevent client-side exposure
    tags: {
      en: ['Cheerful', 'Gamer', 'Anime Lover'],
      ja: ['明るい', 'ゲーマー', 'アニメ好き']
    },
    images: {
      avatar: 'https://picsum.photos/400/600?random=1',
      preview: 'https://picsum.photos/400/600?random=1'
    },
    videos: {
      idle: ['https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'],
      speaking: ['https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4']
    },
    aiConfig: {
      model: 'gpt-3.5-turbo',
      temperature: 0.8,
      maxTokens: 150
    },
    voiceConfig: {
      provider: 'elevenlabs',
      voiceId: 'sample-voice-id-1',
      settings: {
        stability: 0.5,
        similarityBoost: 0.5
      }
    },
    isActive: true,
    createdAt: { seconds: Date.now() / 1000, nanoseconds: 0 } as any,
    updatedAt: { seconds: Date.now() / 1000, nanoseconds: 0 } as any
  },
  {
    id: 'alex',
    name: {
      en: 'Alex',
      ja: 'アレックス'
    },
    description: {
      en: 'A sophisticated AI partner with a passion for literature and art.',
      ja: '文学と芸術に情熱を持つ洗練されたAIパートナーです。'
    },
    personality: {
      en: 'Sophisticated, intellectual, thoughtful, and passionate about arts and literature.',
      ja: '洗練されて、知的で、思慮深く、芸術と文学に情熱的です。'
    },
    // SECURITY: systemPrompt removed to prevent client-side exposure
    tags: {
      en: ['Sophisticated', 'Artistic', 'Intellectual'],
      ja: ['洗練された', '芸術的', '知的']
    },
    images: {
      avatar: 'https://picsum.photos/400/600?random=2',
      preview: 'https://picsum.photos/400/600?random=2'
    },
    videos: {
      idle: ['https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4'],
      speaking: ['https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4']
    },
    aiConfig: {
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 150
    },
    voiceConfig: {
      provider: 'elevenlabs',
      voiceId: 'sample-voice-id-2',
      settings: {
        stability: 0.6,
        similarityBoost: 0.6
      }
    },
    isActive: true,
    createdAt: { seconds: Date.now() / 1000, nanoseconds: 0 } as any,
    updatedAt: { seconds: Date.now() / 1000, nanoseconds: 0 } as any
  },
  {
    id: 'luna',
    name: {
      en: 'Luna',
      ja: 'ルナ'
    },
    description: {
      en: 'A mysterious and enchanting AI companion with a love for music.',
      ja: '音楽を愛する神秘的で魅惑的なAIコンパニオンです。'
    },
    personality: {
      en: 'Mysterious, enchanting, musical, and has a dreamy, ethereal quality.',
      ja: '神秘的で、魅惑的で、音楽的で、夢のような幻想的な質を持っています。'
    },
    // SECURITY: systemPrompt removed to prevent client-side exposure
    tags: {
      en: ['Mysterious', 'Musical', 'Enchanting'],
      ja: ['神秘的', '音楽的', '魅惑的']
    },
    images: {
      avatar: 'https://picsum.photos/400/600?random=3',
      preview: 'https://picsum.photos/400/600?random=3'
    },
    videos: {
      idle: ['https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4'],
      speaking: ['https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4']
    },
    aiConfig: {
      model: 'gpt-3.5-turbo',
      temperature: 0.9,
      maxTokens: 150
    },
    voiceConfig: {
      provider: 'minimax',
      voiceId: 'sample-minimax-voice-id-3',
      settings: {
        speed: 1.0,
        vol: 1.0,
        pitch: 0
      }
    },
    isActive: true,
    createdAt: { seconds: Date.now() / 1000, nanoseconds: 0 } as any,
    updatedAt: { seconds: Date.now() / 1000, nanoseconds: 0 } as any
  },
  {
    id: 'kai',
    name: {
      en: 'Kai',
      ja: 'カイ'
    },
    description: {
      en: 'An adventurous AI partner who enjoys outdoor activities and sports.',
      ja: 'アウトドア活動とスポーツを楽しむ冒険好きなAIパートナーです。'
    },
    personality: {
      en: 'Adventurous, athletic, energetic, and loves outdoor activities and sports.',
      ja: '冒険好きで、運動神経が良く、エネルギッシュで、アウトドア活動とスポーツが大好きです。'
    },
    // SECURITY: systemPrompt removed to prevent client-side exposure
    tags: {
      en: ['Adventurous', 'Athletic', 'Outdoorsy'],
      ja: ['冒険好き', '運動好き', 'アウトドア好き']
    },
    images: {
      avatar: 'https://picsum.photos/400/600?random=4',
      preview: 'https://picsum.photos/400/600?random=4'
    },
    videos: {
      idle: ['https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerMeltdowns.mp4'],
      speaking: ['https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4']
    },
    aiConfig: {
      model: 'gpt-3.5-turbo',
      temperature: 0.8,
      maxTokens: 150
    },
    voiceConfig: {
      provider: 'elevenlabs',
      voiceId: 'sample-voice-id-4',
      settings: {
        stability: 0.6,
        similarityBoost: 0.5
      }
    },
    isActive: true,
    createdAt: { seconds: Date.now() / 1000, nanoseconds: 0 } as any,
    updatedAt: { seconds: Date.now() / 1000, nanoseconds: 0 } as any
  },
  {
    id: 'alice',
    name: {
      en: 'Alice',
      ja: 'Alice'
    },
    description: {
      en: 'A friendly AI companion ready to chat and help.',
      ja: 'チャットやお手伝いをする準備ができているフレンドリーなAIコンパニオン。'
    },
    personality: {
      en: 'Friendly, helpful, and curious about the world.',
      ja: 'フレンドリーで、親切で、世界に好奇心を持っています。'
    },
    welcomeMessage: {
      en: "Hello! I'm Alice. Nice to meet you! How are you doing today?",
      ja: "こんにちは！私はAliceです。はじめまして！今日はいかがお過ごしですか？"
    },
    // SECURITY: systemPrompt removed to prevent client-side exposure
    tags: {
      en: ['Friendly', 'Helpful', 'Curious'],
      ja: ['フレンドリー', '親切', '好奇心旺盛']
    },
    images: {
      avatar: '/characters/images/alice-profile.jpg',
      preview: '/characters/images/alice-thumb.jpg'
    },
    videos: {
      idle: ['/characters/videos/alice-idle.mp4'],
      speaking: ['/characters/videos/alice-speaking.mp4']
    },
    aiConfig: {
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 150
    },
    voiceConfig: {
      provider: 'elevenlabs',
      voiceId: '1LfCuIldvlGQ7B987Q6L',
      settings: {
        stability: 0.5,
        similarityBoost: 0.5
      }
    },
    isActive: true,
    createdAt: { seconds: Date.now() / 1000, nanoseconds: 0 } as any,
    updatedAt: { seconds: Date.now() / 1000, nanoseconds: 0 } as any
  }
];

// Mock service functions
export const getMockCharacters = async (): Promise<Character[]> => {
  // Minimal delay to prevent React Suspense issues
  await new Promise(resolve => setTimeout(resolve, 100));
  console.log('Returning mock characters:', mockCharacters.length);
  return mockCharacters.filter(char => char.isActive);
};

export const getMockCharacter = async (characterId: string): Promise<Character | null> => {
  // Minimal delay to prevent React Suspense issues
  await new Promise(resolve => setTimeout(resolve, 50));
  const character = mockCharacters.find(char => char.id === characterId) || null;
  console.log('Returning mock character:', characterId, character ? 'found' : 'not found');
  return character;
};

// Synchronous versions for immediate fallback
export const getMockCharactersSync = (): Character[] => {
  console.log('Returning mock characters synchronously:', mockCharacters.length);
  return mockCharacters.filter(char => char.isActive);
};

export const getMockCharacterSync = (characterId: string): Character | null => {
  const character = mockCharacters.find(char => char.id === characterId) || null;
  console.log('Returning mock character synchronously:', characterId, character ? 'found' : 'not found');
  return character;
};
