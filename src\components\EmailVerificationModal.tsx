"use client";

import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useI18n } from "@/contexts/I18nProvider";
import { sendEmailVerification } from "firebase/auth";

interface EmailVerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  userEmail: string;
}

export default function EmailVerificationModal({ 
  isOpen, 
  onClose, 
  userEmail 
}: EmailVerificationModalProps) {
  const [isResending, setIsResending] = useState(false);
  const [resendSuccess, setResendSuccess] = useState(false);
  const [resendError, setResendError] = useState("");
  
  const { user } = useAuth();
  const { language } = useI18n();

  if (!isOpen) return null;

  const handleResendEmail = async () => {
    if (!user) return;
    
    setIsResending(true);
    setResendError("");
    setResendSuccess(false);

    try {
      await sendEmailVerification(user);
      setResendSuccess(true);
      console.log('Email verification sent successfully');
    } catch (error: any) {
      console.error('Error sending verification email:', error);
      setResendError(
        language === 'ja' 
          ? 'メール送信に失敗しました。時間をおいて再度お試しください。'
          : 'Failed to send email. Please try again later.'
      );
    } finally {
      setIsResending(false);
    }
  };

  const handleClose = () => {
    setResendSuccess(false);
    setResendError("");
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-white">
              {language === 'ja' ? 'メールアドレスの確認をお願いします' : 'Please Verify Your Email'}
            </h2>
            <button
              onClick={handleClose}
              className="text-white/80 hover:text-white transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            
            <p className="text-gray-700 leading-relaxed">
              {language === 'ja' ? (
                <>
                  ご登録いただいたメールアドレス<br />
                  <span className="font-semibold text-blue-600">{userEmail}</span><br />
                  に確認メールを送信しました。<br /><br />
                  メールをご確認の上、記載されたリンクをクリックして認証を完了してください。<br />
                  <span className="text-orange-600 font-medium">認証が完了するまで一部機能が制限されます。</span>
                </>
              ) : (
                <>
                  We've sent a verification email to<br />
                  <span className="font-semibold text-blue-600">{userEmail}</span><br /><br />
                  Please check your email and click the verification link to complete the process.<br />
                  <span className="text-orange-600 font-medium">Some features are limited until verification is complete.</span>
                </>
              )}
            </p>
          </div>

          {/* Success Message */}
          {resendSuccess && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <p className="text-green-700 text-sm">
                  {language === 'ja' 
                    ? '確認メールを再送信しました。メールをご確認ください。'
                    : 'Verification email resent successfully. Please check your email.'
                  }
                </p>
              </div>
            </div>
          )}

          {/* Error Message */}
          {resendError && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-red-700 text-sm">{resendError}</p>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={handleResendEmail}
              disabled={isResending}
              className="w-full bg-blue-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
            >
              {isResending ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {language === 'ja' ? '送信中...' : 'Sending...'}
                </>
              ) : (
                language === 'ja' ? '確認メールを再送信する' : 'Resend Verification Email'
              )}
            </button>
            
            <button
              onClick={handleClose}
              className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors"
            >
              {language === 'ja' ? '後で確認する' : 'Verify Later'}
            </button>
          </div>

          {/* Help Text */}
          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500">
              {language === 'ja' 
                ? 'メールが届かない場合は、迷惑メールフォルダもご確認ください。'
                : 'If you don\'t see the email, please check your spam folder.'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
