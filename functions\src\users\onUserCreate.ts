/**
 * 新規ユーザー登録時のプロフィール自動作成機能
 * Firebase Authenticationで新しいユーザーが作成された際に、
 * 対応するユーザープロフィールをFirestoreに自動作成
 */

import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

/**
 * ユーザープロフィールのデータ構造
 */
interface UserProfile {
  displayName: string;
  email: string;
  photoURL: string;
  points: number;
  subscriptionTier: 'free' | 'standard' | 'middle' | 'big';
  subscriptionId: string | null;
  subscriptionStatus: string | null;
  preferences: {
    language: 'en' | 'ja';
    notifications: boolean;
  };
  createdAt: admin.firestore.FieldValue;
  updatedAt: admin.firestore.FieldValue;
}

/**
 * 新規ユーザー作成時のトリガー関数
 * Firebase Authenticationでユーザーが作成されると自動実行
 */
export const createUserProfile = functions.auth.user().onCreate(async (user) => {
  try {
    functions.logger.info('New user created, creating profile:', {
      uid: user.uid,
      email: user.email,
      displayName: user.displayName
    });

    // Firestoreのusersコレクションに新しいドキュメントを作成
    const userProfile: UserProfile = {
      displayName: user.displayName || user.email || '新規ユーザー',
      email: user.email || '',
      photoURL: user.photoURL || '',
      points: 100, // 新規ユーザーへの初期ポイント
      subscriptionTier: 'free', // デフォルトは必ず'free'プラン
      subscriptionId: null,
      subscriptionStatus: null,
      preferences: {
        language: 'en',
        notifications: true
      },
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    };

    // ユーザーのuidと同じIDでドキュメントを作成
    await admin.firestore()
      .collection('users')
      .doc(user.uid)
      .set(userProfile);

    // 初期ポイント付与のトランザクション記録も作成
    await admin.firestore()
      .collection('pointTransactions')
      .add({
        userId: user.uid,
        type: 'earned',
        amount: 100,
        reason: 'Welcome bonus - New user registration',
        description: '新規登録ボーナス',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        metadata: {
          source: 'user_registration',
          automatic: true
        }
      });

    functions.logger.info('User profile created successfully:', {
      uid: user.uid,
      email: user.email,
      initialPoints: 100,
      subscriptionTier: 'free'
    });

    // 成功メトリクス（オプション）
    functions.logger.info('User registration metrics:', {
      event: 'user_profile_created',
      uid: user.uid,
      timestamp: new Date().toISOString(),
      hasDisplayName: !!user.displayName,
      hasPhotoURL: !!user.photoURL,
      emailDomain: user.email ? user.email.split('@')[1] : null
    });

  } catch (error) {
    functions.logger.error('Error creating user profile:', {
      uid: user.uid,
      email: user.email,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });

    // エラーが発生してもユーザー作成は継続
    // 必要に応じて後でリトライ機能を追加可能
    throw error;
  }
});

/**
 * ユーザープロフィール削除時のクリーンアップ関数（オプション）
 * Firebase Authenticationでユーザーが削除された際に、
 * 関連するFirestoreデータもクリーンアップ
 */
export const cleanupUserData = functions.auth.user().onDelete(async (user) => {
  try {
    functions.logger.info('User deleted, cleaning up data:', {
      uid: user.uid,
      email: user.email
    });

    const batch = admin.firestore().batch();

    // ユーザープロフィールを削除
    const userRef = admin.firestore().collection('users').doc(user.uid);
    batch.delete(userRef);

    // ユーザーのメッセージを削除
    const messagesQuery = await admin.firestore()
      .collection('messages')
      .where('userId', '==', user.uid)
      .get();

    messagesQuery.docs.forEach(doc => {
      batch.delete(doc.ref);
    });

    // ユーザーのポイント取引履歴を削除
    const transactionsQuery = await admin.firestore()
      .collection('pointTransactions')
      .where('userId', '==', user.uid)
      .get();

    transactionsQuery.docs.forEach(doc => {
      batch.delete(doc.ref);
    });

    // バッチ実行
    await batch.commit();

    functions.logger.info('User data cleanup completed:', {
      uid: user.uid,
      deletedMessages: messagesQuery.size,
      deletedTransactions: transactionsQuery.size
    });

  } catch (error) {
    functions.logger.error('Error cleaning up user data:', {
      uid: user.uid,
      email: user.email,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    // クリーンアップエラーは記録するが、削除処理は継続
  }
});

/**
 * ユーザープロフィール更新ヘルパー関数
 * 外部から呼び出し可能なユーティリティ
 */
export const updateUserProfile = functions.https.onCall(async (data, context) => {
  // 認証チェック
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { displayName, photoURL, preferences } = data;
  const userId = context.auth.uid;

  try {
    const updateData: Partial<UserProfile> = {
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    };

    if (displayName !== undefined) {
      updateData.displayName = displayName;
    }

    if (photoURL !== undefined) {
      updateData.photoURL = photoURL;
    }

    if (preferences !== undefined) {
      updateData.preferences = preferences;
    }

    await admin.firestore()
      .collection('users')
      .doc(userId)
      .update(updateData);

    functions.logger.info('User profile updated:', {
      userId,
      updatedFields: Object.keys(updateData)
    });

    return {
      success: true,
      message: 'Profile updated successfully'
    };

  } catch (error) {
    functions.logger.error('Error updating user profile:', error);
    throw new functions.https.HttpsError('internal', 'Failed to update profile');
  }
});

/**
 * ユーザー課金プラン更新関数（管理者専用）
 */
export const updateUserSubscription = functions.https.onCall(async (data, context) => {
  // 管理者権限チェック
  if (!context.auth || !context.auth.token.admin) {
    throw new functions.https.HttpsError('permission-denied', 'Admin access required');
  }

  const { userId, subscriptionTier, subscriptionId, subscriptionStatus } = data;

  // 有効な課金プランかチェック
  const validTiers = ['free', 'standard', 'middle', 'big'];
  if (!validTiers.includes(subscriptionTier)) {
    throw new functions.https.HttpsError('invalid-argument', 'Invalid subscription tier');
  }

  try {
    await admin.firestore()
      .collection('users')
      .doc(userId)
      .update({
        subscriptionTier,
        subscriptionId: subscriptionId || null,
        subscriptionStatus: subscriptionStatus || null,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

    functions.logger.info('User subscription updated:', {
      userId,
      subscriptionTier,
      subscriptionId,
      subscriptionStatus
    });

    return {
      success: true,
      message: 'Subscription updated successfully'
    };

  } catch (error) {
    functions.logger.error('Error updating user subscription:', error);
    throw new functions.https.HttpsError('internal', 'Failed to update subscription');
  }
});
