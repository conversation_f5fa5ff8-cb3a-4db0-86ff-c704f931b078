{"alice": {"id": "alice", "name": {"en": "alice", "ja": "alice"}, "description": {"en": "alice", "ja": "alice"}, "age": {"en": "20", "ja": "20"}, "occupation": {"en": "", "ja": ""}, "personality": {"en": [""], "ja": [""]}, "background": {"en": "", "ja": ""}, "welcomeMessage": {"en": "Hello! I'm <PERSON>. Nice to meet you! How are you doing today?", "ja": "こんにちは！私はAliceです。はじめまして！今日はいかがお過ごしですか？"}, "media": {"profileImage": "/characters/images/alice-profile.jpg", "thumbnailImage": "/characters/images/alice-thumb.jpg", "idleVideo": "/characters/videos/alice-idle.mp4", "speakingVideo": "/characters/videos/alice-speaking.mp4"}, "aiProvider": "openrouter.ai", "aiModel": "mistralai/mistral-small-24b-instruct-2501", "aiConfig": {"provider": "openrouter.ai", "model": "mistralai/mistral-small-24b-instruct-2501", "temperature": 0.7, "maxTokens": 500}, "voiceConfig": {"voiceId": "cNYrMw9glwJZXR8RwbuR"}, "elevenLabsVoiceId": "cNYrMw9glwJZXR8RwbuR", "videoSets": [{"id": "1", "normalVideo": {}, "lipSyncVideo": {}, "uploaded": true, "normalVideoPath": "/characters/video-sets/alice/alice-set1-normal.mp4", "lipSyncVideoPath": "/characters/video-sets/alice/alice-set1-lipsync.mp4"}], "isActive": true, "createdAt": "2025-05-30T02:46:34.428Z", "updatedAt": "2025-06-08T01:00:50.288Z", "characterId": "alice", "interests": {"en": [""], "ja": [""]}, "isPremium": false, "sortOrder": 1, "voiceProvider": "minimax", "minimaxVoiceId": "Calm_Woman", "minimaxModel": "speech-02-turbo", "customVoiceId": "", "falMinimaxVoiceId": "Wise_Woman", "falMinimaxModel": "speech-02-turbo"}, "naomi": {"id": "naomi", "name": {"en": "naomi", "ja": "ナオミ"}, "description": {"en": "naomi", "ja": "ナオミ"}, "age": {"en": "20", "ja": "20"}, "occupation": {"en": "", "ja": ""}, "personality": {"en": [""], "ja": [""]}, "background": {"en": "", "ja": ""}, "welcomeMessage": {"en": "Hi! How are you?", "ja": "こんにちわ！元気かな？"}, "media": {"profileImage": "/characters/images/naomi-profile.jpg", "thumbnailImage": "/characters/images/naomi-thumb.jpg", "idleVideo": "/characters/videos/naomi-idle.mp4", "speakingVideo": "/characters/videos/naomi-speaking.mp4"}, "aiProvider": "openrouter.ai", "aiModel": "google/gemini-2.0-flash-001", "aiConfig": {"provider": "openrouter.ai", "model": "google/gemini-2.0-flash-001", "temperature": 0.7, "maxTokens": 500}, "voiceProvider": "minimax", "voiceConfig": {"provider": "minimax", "voiceId": "Japanese_DecisivePrincess"}, "minimaxVoiceId": "Japanese_DecisivePrincess", "minimaxModel": "speech-01-turbo", "customVoiceId": "Japanese_DecisivePrincess", "elevenLabsVoiceId": "4lOQ7A2l7HPuG7UIHiKA", "videoSets": [{"id": "1", "normalVideo": {}, "lipSyncVideo": {}, "uploaded": true, "normalVideoPath": "/characters/video-sets/naomi/naomi-set1-normal.mp4", "lipSyncVideoPath": "/characters/video-sets/naomi/naomi-set1-lipsync.mp4"}], "isActive": true, "createdAt": "2025-06-02T00:56:19.870Z", "updatedAt": "2025-06-08T00:51:16.984Z", "characterId": "naomi", "interests": {"en": [""], "ja": [""]}, "isPremium": false, "sortOrder": 1}, "jhon": {"id": "jhon", "name": {"en": "<PERSON>", "ja": "ジョン"}, "description": {"en": "", "ja": ""}, "age": {"en": "20", "ja": "20"}, "occupation": {"en": "", "ja": ""}, "personality": {"en": [""], "ja": [""]}, "background": {"en": "", "ja": ""}, "welcomeMessage": {"en": "Hello! I'm <PERSON>. How are you doing?", "ja": "こんにちは、僕はジョンだよ。元気？"}, "media": {"profileImage": "/characters/images/jhon-profile.jpg", "thumbnailImage": "/characters/images/jhon-thumb.jpg", "idleVideo": "/characters/videos/jhon-idle.mp4", "speakingVideo": "/characters/videos/jhon-speaking.mp4"}, "aiProvider": "openrouter.ai", "aiModel": "google/gemini-2.0-flash-001", "aiConfig": {"provider": "openrouter.ai", "model": "google/gemini-2.0-flash-001", "temperature": 0.7, "maxTokens": 500}, "voiceProvider": "minimax", "voiceConfig": {"provider": "minimax", "voiceId": "Japanese_LoyalKnight"}, "minimaxVoiceId": "Custom", "minimaxModel": "speech-01-turbo", "customVoiceId": "Japanese_LoyalKnight", "elevenLabsVoiceId": "Hbb2NXaf6CKJnlEHYM1D", "videoSets": [{"id": "1", "normalVideo": {}, "lipSyncVideo": {}, "uploaded": true, "normalVideoPath": "/characters/video-sets/jhon/jhon-set1-normal.mp4", "lipSyncVideoPath": "/characters/video-sets/jhon/jhon-set1-lipsync.mp4"}], "isActive": true, "createdAt": "2025-06-02T09:07:34.395Z", "updatedAt": "2025-06-08T01:08:12.406Z", "characterId": "jhon", "interests": {"en": [""], "ja": [""]}, "isPremium": false, "sortOrder": 1}, "jake": {"id": "jake", "name": {"en": "<PERSON>", "ja": "ジェイク"}, "description": {"en": "", "ja": ""}, "personality": {"en": [""], "ja": [""]}, "interests": {"en": [""], "ja": [""]}, "age": 25, "occupation": {"en": "", "ja": ""}, "background": {"en": "", "ja": ""}, "welcomeMessage": {"en": "Hey, I'm <PERSON>. I've been looking forward to chatting with you. Just relax, and feel free to start with whatever comes to mind. I'm all ears.", "ja": "やあ、ジェイクだよ。君と話せるのを楽しみにしてたんだ。リラックスして、好きなことから聞かせてくれると嬉しいな。"}, "isActive": true, "isPremium": false, "sortOrder": 1, "media": {"profileImage": "/characters/images/jake-profile.jpg", "thumbnailImage": "/characters/images/jake-thumb.jpg", "idleVideo": "/characters/videos/jake-idle.mp4", "speakingVideo": "/characters/videos/jake-speaking.mp4", "listeningVideo": "/characters/videos/jake-listening.mp4", "voiceSample": "/characters/audio/jake-voice.mp3"}, "aiProvider": "openrouter.ai", "aiModel": "mistralai/mistral-small-3.1-24b-instruct", "aiConfig": {"provider": "openrouter.ai", "model": "mistralai/mistral-small-3.1-24b-instruct", "temperature": 0.7, "maxTokens": 500}, "voiceProvider": "elevenlabs", "voiceConfig": {"provider": "elevenlabs", "voiceId": "UgBBYS2sOqTuMpoF3BR0"}, "elevenLabsVoiceId": "UgBBYS2sOqTuMpoF3BR0", "createdAt": "2025-06-03T05:05:51.522Z", "updatedAt": "2025-06-03T05:08:44.606Z", "videoSets": [{"id": "1", "normalVideo": {}, "lipSyncVideo": {}, "uploaded": true, "normalVideoPath": "/characters/video-sets/jake/jake-set1-normal.mp4", "lipSyncVideoPath": "/characters/video-sets/jake/jake-set1-lipsync.mp4"}], "characterId": "jake"}, "ava": {"id": "ava", "name": {"en": "Ava", "ja": "エヴァ"}, "description": {"en": "", "ja": ""}, "personality": {"en": [""], "ja": [""]}, "interests": {"en": [""], "ja": [""]}, "age": 22, "occupation": {"en": "", "ja": ""}, "background": {"en": "", "ja": ""}, "welcomeMessage": {"en": "", "ja": ""}, "isActive": true, "isPremium": false, "sortOrder": 1, "media": {"profileImage": "/characters/images/ava-profile.jpg", "thumbnailImage": "/characters/images/ava-thumb.jpg", "idleVideo": "/characters/videos/ava-idle.mp4", "speakingVideo": "/characters/videos/ava-speaking.mp4", "listeningVideo": "/characters/videos/ava-listening.mp4", "voiceSample": "/characters/audio/ava-voice.mp3"}, "aiProvider": "openrouter.ai", "aiModel": "mistralai/mistral-nemo", "aiConfig": {"provider": "openrouter.ai", "model": "mistralai/mistral-nemo", "temperature": 0.7, "maxTokens": 500}, "voiceProvider": "elevenlabs", "voiceConfig": {"provider": "elevenlabs", "voiceId": "QR8Ab6874jZU5xhELPFF"}, "elevenLabsVoiceId": "QR8Ab6874jZU5xhELPFF", "createdAt": "2025-06-03T05:33:44.563Z", "updatedAt": "2025-06-03T05:51:00.298Z", "videoSets": [{"id": "1", "normalVideo": {}, "lipSyncVideo": {}, "uploaded": true, "normalVideoPath": "/characters/video-sets/ava/ava-set1-normal.mp4", "lipSyncVideoPath": "/characters/video-sets/ava/ava-set1-lipsync.mp4"}], "characterId": "ava"}, "gogo": {"id": "gogo", "name": {"en": "gogo", "ja": "gogo"}, "description": {"en": "hi!", "ja": "はい！"}, "personality": {"en": [""], "ja": [""]}, "interests": {"en": [""], "ja": [""]}, "age": {"en": "20", "ja": "20"}, "occupation": {"en": "", "ja": ""}, "background": {"en": "", "ja": ""}, "welcomeMessage": {"en": "hi", "ja": "hi"}, "isActive": true, "isPremium": false, "sortOrder": 1, "media": {"profileImage": "/characters/images/gogo-profile.jpg", "thumbnailImage": "/characters/images/gogo-thumb.jpg", "idleVideo": "/characters/videos/gogo-idle.mp4", "speakingVideo": "/characters/videos/gogo-speaking.mp4"}, "aiProvider": "openrouter.ai", "aiModel": "nousresearch/deephermes-3-mistral-24b-preview:free", "elevenLabsVoiceId": "", "voiceProvider": "minimax", "minimaxVoiceId": "Custom", "minimaxModel": "speech-01-turbo", "customVoiceId": "Japanese_Innocent<PERSON><PERSON>", "falMinimaxVoiceId": "Wise_Woman", "falMinimaxModel": "speech-02-turbo", "createdAt": "2025-06-08T01:39:40.945Z", "updatedAt": "2025-06-08T08:02:46.563Z", "characterId": "gogo"}}