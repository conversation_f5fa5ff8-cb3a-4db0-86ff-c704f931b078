import { loadStripe } from '@stripe/stripe-js';
import Strip<PERSON> from 'stripe';

// Client-side Stripe - only initialize if key is available
export const stripePromise = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
  ? loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY)
  : Promise.resolve(null);

// Server-side Stripe
export const stripe = process.env.STRIPE_SECRET_KEY
  ? new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2025-05-28.basil',
    })
  : null;

// Check if Stripe is configured
export const isStripeConfigured = () => {
  return !!(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY && process.env.STRIPE_SECRET_KEY);
};

// Point packages configuration
export const POINT_PACKAGES = [
  {
    id: 'points_100',
    name: 'Starter Pack',
    points: 100,
    price: 499, // $4.99 in cents
    description: 'Perfect for trying out the service',
    popular: false,
  },
  {
    id: 'points_500',
    name: 'Popular Pack',
    points: 500,
    price: 1999, // $19.99 in cents
    description: 'Great value for regular users',
    popular: true,
  },
  {
    id: 'points_1000',
    name: 'Premium Pack',
    points: 1000,
    price: 3499, // $34.99 in cents
    description: 'Best value for power users',
    popular: false,
  },
  {
    id: 'points_2500',
    name: 'Ultimate Pack',
    points: 2500,
    price: 7999, // $79.99 in cents
    description: 'Maximum value for unlimited chatting',
    popular: false,
  },
];

export const formatPrice = (priceInCents: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(priceInCents / 100);
};

export const getPointPackage = (packageId: string) => {
  return POINT_PACKAGES.find(pkg => pkg.id === packageId);
};
