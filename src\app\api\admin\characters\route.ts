import { NextRequest, NextResponse } from 'next/server';
import { CharacterService, FullCharacterData } from '@/lib/characterService';
import { normalizeCharacterData, validateCharacterData } from '@/lib/characterTemplate';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// Admin authentication check
function isAdmin(request: NextRequest): boolean {
  const adminToken = request.headers.get('x-admin-token');
  const expectedToken = process.env.NEXT_PUBLIC_ADMIN_TOKEN || 'your-secure-admin-token-here-change-this';
  return adminToken === expectedToken;
}

// GET - List all characters
export async function GET(request: NextRequest) {
  try {
    if (!isAdmin(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Load characters using unified service
    const characters = CharacterService.getAllActiveCharacters();

    const charactersWithPrompts = characters.map(char => ({
      ...char,
      hasPrompt: true // All characters now have integrated prompts
    }));

    return NextResponse.json({
      success: true,
      characters: charactersWithPrompts,
      availablePrompts: [], // No longer needed with unified system
      total: characters.length
    });
  } catch (error) {
    console.error('Error loading characters:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to load characters' },
      { status: 500 }
    );
  }
}

// POST - Create new character
export async function POST(request: NextRequest) {
  try {
    if (!isAdmin(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const characterData = await request.json();

    // Normalize to unified format
    const normalizedCharacter = normalizeCharacterData(characterData);
    
    // Validate unified format
    const validationErrors = validateCharacterData(normalizedCharacter);
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Character validation failed', 
          details: validationErrors 
        },
        { status: 400 }
      );
    }

    // Check if character ID already exists
    const existingCharacter = CharacterService.getCharacter(normalizedCharacter.id);
    if (existingCharacter) {
      return NextResponse.json(
        { success: false, error: 'Character ID already exists' },
        { status: 409 }
      );
    }

    // Add timestamps
    const newCharacter: FullCharacterData = {
      ...normalizedCharacter,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true
    };

    // Save using unified service
    const success = CharacterService.saveCharacter(newCharacter);
    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Failed to create character' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      character: newCharacter,
      message: 'Character created successfully'
    });
  } catch (error) {
    console.error('Error creating character:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create character' },
      { status: 500 }
    );
  }
}

// PUT - Update existing character
export async function PUT(request: NextRequest) {
  try {
    if (!isAdmin(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { characterId, ...updates } = await request.json();

    if (!characterId) {
      return NextResponse.json(
        { success: false, error: 'Character ID is required' },
        { status: 400 }
      );
    }

    // Check if character exists
    const existingCharacter = CharacterService.getCharacter(characterId);
    if (!existingCharacter) {
      return NextResponse.json(
        { success: false, error: 'Character not found' },
        { status: 404 }
      );
    }

    // Merge updates with existing data and normalize
    const mergedData = {
      ...existingCharacter,
      ...updates,
      id: characterId, // Ensure ID doesn't change
    };
    
    const normalizedCharacter = normalizeCharacterData(mergedData);
    
    // Validate unified format
    const validationErrors = validateCharacterData(normalizedCharacter);
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Character validation failed', 
          details: validationErrors 
        },
        { status: 400 }
      );
    }

    const updatedCharacter: FullCharacterData = {
      ...normalizedCharacter,
      updatedAt: new Date().toISOString()
    };

    const success = CharacterService.saveCharacter(updatedCharacter);
    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Failed to update character' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      character: updatedCharacter,
      message: 'Character updated successfully'
    });
  } catch (error) {
    console.error('Error updating character:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update character' },
      { status: 500 }
    );
  }
}

// DELETE - Deactivate character (soft delete)
export async function DELETE(request: NextRequest) {
  try {
    if (!isAdmin(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const characterId = searchParams.get('id');

    if (!characterId) {
      return NextResponse.json(
        { success: false, error: 'Character ID is required' },
        { status: 400 }
      );
    }

    // Soft delete by setting isActive to false
    const success = CharacterService.deactivateCharacter(characterId);
    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Failed to deactivate character' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Character deactivated successfully'
    });
  } catch (error) {
    console.error('Error deactivating character:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to deactivate character' },
      { status: 500 }
    );
  }
}
