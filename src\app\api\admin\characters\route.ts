import { NextRequest, NextResponse } from 'next/server';
import { add<PERSON>hara<PERSON>, updateCharacter, getAllActiveCharacters, getCharacterData, FullCharacterData } from '@/lib/characterManager';
import { hasCharacterPrompt, getAvailableCharacters } from '@/lib/promptManager';
import {
  loadCharactersFromServer,
  saveCharacterToServer,
  deleteCharacterFromServer
} from '@/lib/serverFileManager';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// Admin authentication check
function isAdmin(request: NextRequest): boolean {
  const adminToken = request.headers.get('x-admin-token');
  const expectedToken = process.env.NEXT_PUBLIC_ADMIN_TOKEN || 'your-secure-admin-token-here-change-this';
  return adminToken === expectedToken;
}

// GET - List all characters
export async function GET(request: NextRequest) {
  try {
    if (!isAdmin(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Load characters from server files
    const serverCharacters = loadCharactersFromServer();
    const characters = Object.values(serverCharacters).filter(char => char.isActive);
    const availablePrompts = getAvailableCharacters();

    const charactersWithPrompts = characters.map(char => ({
      ...char,
      hasPrompt: hasCharacterPrompt(char.id)
    }));

    return NextResponse.json({
      success: true,
      characters: charactersWithPrompts,
      availablePrompts,
      total: characters.length
    });
  } catch (error) {
    console.error('Error fetching characters:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new character
export async function POST(request: NextRequest) {
  try {
    if (!isAdmin(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const characterData = await request.json();

    // Validate required fields
    const requiredFields = ['id', 'name', 'description', 'personality', 'interests', 'media'];
    for (const field of requiredFields) {
      if (!characterData[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Check if character ID already exists in server files
    const serverCharacters = loadCharactersFromServer();
    if (serverCharacters[characterData.id]) {
      return NextResponse.json(
        { success: false, error: 'Character ID already exists' },
        { status: 409 }
      );
    }

    // Check if system prompt exists for this character (warning only)
    if (!hasCharacterPrompt(characterData.id)) {
      console.warn(`No system prompt found for character: ${characterData.id}. Character will use global prompt only.`);
    }

    // Add default values
    const newCharacter = {
      ...characterData,
      isActive: characterData.isActive ?? true,
      isPremium: characterData.isPremium ?? false,
      sortOrder: characterData.sortOrder ?? 999,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Save to server files
    const success = saveCharacterToServer(newCharacter);
    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Failed to create character' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      character: newCharacter,
      message: 'Character created successfully'
    });
  } catch (error) {
    console.error('Error creating character:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update existing character
export async function PUT(request: NextRequest) {
  try {
    if (!isAdmin(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { characterId, ...updates } = await request.json();

    if (!characterId) {
      return NextResponse.json(
        { success: false, error: 'Character ID is required' },
        { status: 400 }
      );
    }

    // Check if character exists in server files
    const serverCharacters = loadCharactersFromServer();
    const existingCharacter = serverCharacters[characterId];
    if (!existingCharacter) {
      return NextResponse.json(
        { success: false, error: 'Character not found' },
        { status: 404 }
      );
    }

    // Update character in server files
    const updatedCharacter = {
      ...existingCharacter,
      ...updates,
      updatedAt: new Date().toISOString()
    };

    const success = saveCharacterToServer(updatedCharacter);
    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Failed to update character' },
        { status: 500 }
      );
    }
    return NextResponse.json({
      success: true,
      character: updatedCharacter,
      message: 'Character updated successfully'
    });
  } catch (error) {
    console.error('Error updating character:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Deactivate character (soft delete)
export async function DELETE(request: NextRequest) {
  try {
    if (!isAdmin(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const characterId = searchParams.get('id');

    if (!characterId) {
      return NextResponse.json(
        { success: false, error: 'Character ID is required' },
        { status: 400 }
      );
    }

    // Soft delete by setting isActive to false
    const success = updateCharacter(characterId, { isActive: false });
    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Failed to deactivate character' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Character deactivated successfully'
    });
  } catch (error) {
    console.error('Error deactivating character:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
