#!/usr/bin/env node

/**
 * Admin Setup Script
 * 
 * This script sets up admin privileges for the Firebase implementation.
 * Run this script to grant admin access to specific users.
 * 
 * Usage:
 *   node scripts/setup-admin.js <user-email>
 * 
 * Example:
 *   node scripts/setup-admin.js <EMAIL>
 */

const admin = require('firebase-admin');
const path = require('path');

// Initialize Firebase Admin SDK
const serviceAccountPath = path.join(__dirname, '../firebase-service-account.json');

try {
  const serviceAccount = require(serviceAccountPath);
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: serviceAccount.project_id
  });
  console.log('Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('Failed to initialize Firebase Admin SDK:', error.message);
  console.log('Please ensure firebase-service-account.json exists in the project root');
  process.exit(1);
}

async function setAdminClaim(email) {
  try {
    // Get user by email
    const userRecord = await admin.auth().getUserByEmail(email);
    console.log(`Found user: ${userRecord.uid} (${userRecord.email})`);

    // Set admin custom claim
    await admin.auth().setCustomUserClaims(userRecord.uid, { admin: true });
    console.log(`✅ Admin claim set successfully for ${email}`);

    // Verify the claim was set
    const updatedUser = await admin.auth().getUser(userRecord.uid);
    console.log('Custom claims:', updatedUser.customClaims);

    return true;
  } catch (error) {
    console.error('❌ Error setting admin claim:', error.message);
    return false;
  }
}

async function listAdmins() {
  try {
    console.log('\n📋 Current admin users:');
    
    // List all users and check for admin claims
    const listUsersResult = await admin.auth().listUsers();
    const adminUsers = listUsersResult.users.filter(user => 
      user.customClaims && user.customClaims.admin === true
    );

    if (adminUsers.length === 0) {
      console.log('No admin users found');
    } else {
      adminUsers.forEach(user => {
        console.log(`- ${user.email} (${user.uid})`);
      });
    }
  } catch (error) {
    console.error('Error listing admin users:', error.message);
  }
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Usage: node scripts/setup-admin.js <user-email>');
    console.log('       node scripts/setup-admin.js --list');
    process.exit(1);
  }

  if (args[0] === '--list') {
    await listAdmins();
    process.exit(0);
  }

  const email = args[0];
  
  if (!email.includes('@')) {
    console.error('❌ Invalid email address');
    process.exit(1);
  }

  console.log(`🔧 Setting up admin privileges for: ${email}`);
  
  const success = await setAdminClaim(email);
  
  if (success) {
    console.log('\n✅ Admin setup completed successfully!');
    console.log('\nNext steps:');
    console.log('1. The user should sign out and sign back in to refresh their token');
    console.log('2. Admin privileges will be available immediately after re-authentication');
    console.log('3. Use --list to verify admin users');
  } else {
    console.log('\n❌ Admin setup failed');
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down...');
  process.exit(0);
});

main().catch(error => {
  console.error('Unexpected error:', error);
  process.exit(1);
});
