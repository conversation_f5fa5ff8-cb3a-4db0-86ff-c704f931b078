/**
 * Optimized Logger System - SERVER SIDE ONLY
 * Provides environment-aware logging with performance optimization
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LoggerConfig {
  isDevelopment: boolean;
  enableDebug: boolean;
  enablePerformanceLogs: boolean;
}

class ServerLogger {
  private static config: LoggerConfig = {
    isDevelopment: process.env.NODE_ENV === 'development',
    enableDebug: process.env.NODE_ENV === 'development',
    enablePerformanceLogs: process.env.NODE_ENV === 'development'
  };

  /**
   * Debug logs - only in development
   */
  static debug(message: string, ...args: any[]): void {
    if (!this.config.enableDebug) return;
    console.log(`🔍 ${message}`, ...args);
  }

  /**
   * Info logs - always enabled
   */
  static info(message: string, ...args: any[]): void {
    console.log(`ℹ️ ${message}`, ...args);
  }

  /**
   * Warning logs - always enabled
   */
  static warn(message: string, ...args: any[]): void {
    console.warn(`⚠️ ${message}`, ...args);
  }

  /**
   * Error logs - always enabled
   */
  static error(message: string, ...args: any[]): void {
    console.error(`❌ ${message}`, ...args);
  }

  /**
   * Performance logs - only in development
   */
  static perf(message: string, startTime?: number): void {
    if (!this.config.enablePerformanceLogs) return;
    
    if (startTime) {
      const duration = Date.now() - startTime;
      console.log(`⚡ ${message}: ${duration}ms`);
    } else {
      console.log(`⚡ ${message}`);
    }
  }

  /**
   * API logs - optimized for production
   */
  static api(method: string, endpoint: string, status?: number, duration?: number): void {
    if (this.config.isDevelopment) {
      const statusEmoji = status ? (status < 400 ? '✅' : '❌') : '📡';
      const durationText = duration ? ` (${duration}ms)` : '';
      console.log(`${statusEmoji} ${method} ${endpoint}${durationText}`);
    }
  }

  /**
   * Voice generation logs - optimized
   */
  static voice(provider: string, action: string, details?: any): void {
    if (this.config.isDevelopment) {
      const emoji = provider === 'elevenlabs' ? '🎵' : provider === 'minimax' ? '🎙️' : '🔊';
      if (details && typeof details === 'object') {
        // Avoid heavy JSON.stringify in production
        console.log(`${emoji} ${provider}: ${action}`, details);
      } else {
        console.log(`${emoji} ${provider}: ${action}`, details || '');
      }
    }
  }

  /**
   * AI generation logs - optimized
   */
  static ai(provider: string, action: string, details?: any): void {
    if (this.config.isDevelopment) {
      const emoji = '🤖';
      if (details && typeof details === 'object') {
        // Avoid heavy JSON.stringify in production
        console.log(`${emoji} ${provider}: ${action}`, details);
      } else {
        console.log(`${emoji} ${provider}: ${action}`, details || '');
      }
    }
  }

  /**
   * Chunk processing logs - lightweight
   */
  static chunk(type: 'audio' | 'text', index: number, size?: number): void {
    if (!this.config.enableDebug) return;
    
    const emoji = type === 'audio' ? '📦' : '📝';
    const sizeText = size ? ` (${size} bytes)` : '';
    console.log(`${emoji} ${type} chunk ${index}${sizeText}`);
  }

  /**
   * Configure logger settings
   */
  static configure(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Get current configuration
   */
  static getConfig(): LoggerConfig {
    return { ...this.config };
  }

  /**
   * Disable all debug logs (for production optimization)
   */
  static disableDebug(): void {
    this.config.enableDebug = false;
    this.config.enablePerformanceLogs = false;
  }

  /**
   * Enable all logs (for development)
   */
  static enableAll(): void {
    this.config.enableDebug = true;
    this.config.enablePerformanceLogs = true;
  }
}

export default ServerLogger;
