{"functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}], "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "emulators": {"auth": {"port": 9098}, "functions": {"port": 5001}, "firestore": {"port": 8082}, "storage": {"port": 9198}, "ui": {"enabled": true, "port": 4001}, "singleProjectMode": true, "apphosting": {"port": 5002, "rootDirectory": "./", "startCommand": "npm run dev"}}}