rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is admin
    function isAdmin() {
      return request.auth != null && request.auth.token.admin == true;
    }

    // Users can only read/write their own user document
    match /users/{userId} {
      allow read: if request.auth != null && request.auth.uid == userId;

      // Allow user document creation for new users
      allow create: if request.auth != null
        && request.auth.uid == userId
        && request.resource.data.keys().hasAll(['email', 'displayName', 'points', 'tier', 'createdAt', 'updatedAt', 'preferences']);

      // Allow point updates for legitimate operations
      allow update: if request.auth != null
        && request.auth.uid == userId;
    }

    // Characters - secured access control
    match /characters/{characterId} {
      // Admins can access all characters
      allow read: if isAdmin();
      // Regular users can only access active characters
      allow read: if request.auth != null && resource.data.isActive == true;
      // Only admins can modify characters
      allow write: if isAdmin();
    }

    // Character prompts - admin only access
    match /characterPrompts/{characterId} {
      allow read, write: if isAdmin();
    }

    // Messages can only be read by the user who sent them
    match /messages/{messageId} {
      allow read: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
      allow update, delete: if false; // Messages are immutable
    }

    // Chat sessions can only be accessed by the user
    match /chatSessions/{sessionId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }

    // Point transactions are read-only for users
    match /pointTransactions/{transactionId} {
      allow read: if request.auth != null && request.auth.uid == resource.data.userId;
      allow write: if false; // Only server can create transactions
    }

    // User memory (for paid users) - read-only for users
    match /userMemory/{memoryId} {
      allow read: if request.auth != null && request.auth.uid == resource.data.userId;
      allow write: if false; // Only server can manage memory
    }

    // Video triggers are read-only for all authenticated users
    match /videoTriggers/{triggerId} {
      allow read: if request.auth != null;
      allow write: if false; // Only admin can modify triggers
    }

    // Analytics are read-only for the user
    match /userAnalytics/{userId} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if false; // Only server can update analytics
    }

    // GDPR compliance documents
    match /dataExportRequests/{requestId} {
      allow read, create: if request.auth != null && request.auth.uid == resource.data.userId;
      allow update: if false; // Only server can update status
    }

    match /dataDeletionRequests/{requestId} {
      allow read, create: if request.auth != null && request.auth.uid == resource.data.userId;
      allow update: if false; // Only server can update status
    }

    // Admin-only collections
    match /admin/{document=**} {
      allow read, write: if false; // Only server-side access
    }

    // Default deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
