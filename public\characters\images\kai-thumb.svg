<svg width="300" height="400" viewBox="0 0 300 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="300" height="400" fill="url(#kaiGradient)"/>
  <circle cx="150" cy="150" r="50" fill="white" opacity="0.3"/>
  <path d="M120 130C120 118.954 128.954 110 140 110H160C171.046 110 180 118.954 180 130V150C180 161.046 171.046 170 160 170H140C128.954 170 120 161.046 120 150V130Z" fill="white" opacity="0.5"/>
  <circle cx="145" cy="135" r="4" fill="#228B22"/>
  <circle cx="155" cy="135" r="4" fill="#228B22"/>
  <path d="M145 155C145 158.314 147.686 161 151 161H149C152.314 161 155 158.314 155 155" stroke="#228B22" stroke-width="2" stroke-linecap="round"/>
  <text x="150" y="280" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="20" opacity="0.9">Kai</text>
  <text x="150" y="310" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" opacity="0.7">Adventure Guide</text>
  <defs>
    <linearGradient id="kaiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#228B22;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#006400;stop-opacity:0.9" />
    </linearGradient>
  </defs>
</svg>
