"use client";

import { useRouter } from "next/navigation";
import { useI18n } from "@/contexts/I18nProvider";

export default function PurchaseCancelPage() {
  const router = useRouter();
  const { t, language } = useI18n();

  const handleRetry = () => {
    router.back();
  };

  const handleGoHome = () => {
    const homeUrl = language === 'ja' ? '/ja' : '/';
    router.push(homeUrl);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
        {/* Cancel Icon */}
        <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg className="w-8 h-8 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </div>

        {/* Cancel Message */}
        <h1 className="text-2xl font-bold text-gray-800 mb-4">
          {language === 'ja' ? '購入がキャンセルされました' : 'Purchase Cancelled'}
        </h1>
        
        <p className="text-gray-600 mb-6">
          {language === 'ja' 
            ? 'ポイントの購入がキャンセルされました。いつでも再度お試しいただけます。'
            : 'Your point purchase was cancelled. You can try again anytime.'
          }
        </p>

        {/* Action Buttons */}
        <div className="space-y-3">
          <button
            onClick={handleRetry}
            className="w-full bg-primary text-white py-3 rounded-lg font-medium hover:bg-blue-600 transition-colors"
          >
            {language === 'ja' ? '再度購入する' : 'Try Again'}
          </button>
          
          <button
            onClick={handleGoHome}
            className="w-full bg-gray-200 text-gray-800 py-3 rounded-lg font-medium hover:bg-gray-300 transition-colors"
          >
            {language === 'ja' ? 'ホームに戻る' : 'Go Home'}
          </button>
        </div>

        {/* Additional Info */}
        <p className="text-sm text-gray-500 mt-6">
          {language === 'ja' 
            ? 'お支払いは処理されていません。'
            : 'No payment has been processed.'
          }
        </p>
      </div>
    </div>
  );
}
