/**
 * 有料ユーザー限定 - 記憶検索付きチャット応答生成Cloud Function
 * 長期記憶（Supabase）+ 短期記憶（Firestore）を組み合わせたハイブリッド記憶システム
 */

import { onCall, HttpsError } from 'firebase-functions/v2/https';
import { logger } from 'firebase-functions';
import { initializeApp } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { createClient } from '@supabase/supabase-js';
import { VertexAI } from '@google-cloud/vertexai';

// Firebase Admin初期化
if (!getApps().length) {
  initializeApp();
}
const db = getFirestore();

// Supabase初期化
const supabaseUrl = process.env.SUPABASE_PROJECT_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

// Vertex AI初期化
const vertexAI = new VertexAI({
  project: process.env.GOOGLE_CLOUD_PROJECT!,
  location: 'us-central1'
});

interface ChatRequest {
  userId: string;
  characterId: string;
  message: string;
  language: 'ja' | 'en';
}

interface LongTermMemory {
  id: string;
  content: string;
  importance_score: number;
  emotion_context?: string;
  conversation_context?: any;
  tags?: string[];
  similarity: number;
  created_at: string;
}

interface ShortTermMessage {
  content: string;
  sender: 'user' | 'ai';
  createdAt: any;
}

/**
 * 課金状態確認
 */
async function checkUserSubscription(userId: string): Promise<boolean> {
  try {
    const userDoc = await db.collection('users').doc(userId).get();
    
    if (!userDoc.exists) {
      return false;
    }
    
    const userData = userDoc.data();
    const subscriptionTier = userData?.subscriptionTier || 'free';
    
    return subscriptionTier !== 'free';
  } catch (error) {
    logger.error('Error checking user subscription:', error);
    return false;
  }
}

/**
 * テキストをベクトル化
 */
async function generateEmbedding(text: string): Promise<number[]> {
  try {
    const model = vertexAI.getGenerativeModel({
      model: 'text-multilingual-embedding-002'
    });
    
    const result = await model.embedContent(text);
    
    if (!result.embedding?.values) {
      throw new Error('No embedding values returned');
    }
    
    return result.embedding.values;
  } catch (error) {
    logger.error('Error generating embedding:', error);
    throw error;
  }
}

/**
 * 長期記憶検索（有料ユーザー限定）
 */
async function searchLongTermMemories(
  userId: string,
  characterId: string,
  queryEmbedding: number[]
): Promise<LongTermMemory[]> {
  try {
    const { data, error } = await supabase.rpc('search_similar_memories', {
      query_embedding: queryEmbedding,
      target_user_id: userId,
      target_character_id: characterId,
      similarity_threshold: 0.7,
      max_results: 5
    });
    
    if (error) {
      throw error;
    }
    
    return data || [];
  } catch (error) {
    logger.error('Error searching long-term memories:', error);
    return [];
  }
}

/**
 * 短期記憶取得（全ユーザー共通）
 */
async function getShortTermMemories(
  userId: string,
  characterId: string
): Promise<ShortTermMessage[]> {
  try {
    const messagesRef = db.collection('messages')
      .where('userId', '==', userId)
      .where('characterId', '==', characterId)
      .orderBy('createdAt', 'desc')
      .limit(10);
    
    const snapshot = await messagesRef.get();
    
    return snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        content: data.content,
        sender: data.sender,
        createdAt: data.createdAt
      };
    }).reverse(); // 時系列順に並び替え
  } catch (error) {
    logger.error('Error getting short-term memories:', error);
    return [];
  }
}

/**
 * キャラクター情報取得
 */
async function getCharacterInfo(characterId: string) {
  try {
    // CharacterServiceから取得（既存の実装を利用）
    const response = await fetch(`${process.env.FUNCTIONS_URL}/api/characters/${characterId}`);
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(`Character not found: ${characterId}`);
    }
    
    return data.character;
  } catch (error) {
    logger.error('Error getting character info:', error);
    throw error;
  }
}

/**
 * 動的プロンプト生成
 */
function buildPrompt(
  character: any,
  shortTermMemories: ShortTermMessage[],
  longTermMemories: LongTermMemory[],
  currentMessage: string,
  language: 'ja' | 'en',
  isPaidUser: boolean
): string {
  const systemPrompt = character.systemPrompt?.[language] || character.systemPrompt?.en || '';
  
  let prompt = `${systemPrompt}\n\n`;
  
  // 有料ユーザーの場合のみ長期記憶を追加
  if (isPaidUser && longTermMemories.length > 0) {
    prompt += `=== 長期記憶 (Long-term Memory) ===\n`;
    prompt += `以下は過去の重要な会話や出来事です：\n\n`;
    
    longTermMemories.forEach((memory, index) => {
      prompt += `記憶${index + 1} (重要度: ${memory.importance_score.toFixed(2)}, 類似度: ${memory.similarity.toFixed(2)}):\n`;
      prompt += `${memory.content}\n`;
      if (memory.emotion_context) {
        prompt += `感情的文脈: ${memory.emotion_context}\n`;
      }
      prompt += `\n`;
    });
    
    prompt += `=== 長期記憶終了 ===\n\n`;
  }
  
  // 短期記憶（全ユーザー共通）
  if (shortTermMemories.length > 0) {
    prompt += `=== 短期記憶 (Recent Conversation) ===\n`;
    
    shortTermMemories.forEach(msg => {
      const speaker = msg.sender === 'user' ? 'ユーザー' : character.name?.[language] || 'AI';
      prompt += `${speaker}: ${msg.content}\n`;
    });
    
    prompt += `=== 短期記憶終了 ===\n\n`;
  }
  
  // 現在のメッセージ
  prompt += `現在のユーザーメッセージ: ${currentMessage}\n\n`;
  
  // 応答指示
  if (isPaidUser) {
    prompt += `上記の長期記憶と短期記憶を参考に、自然で一貫性のある応答をしてください。`;
  } else {
    prompt += `上記の短期記憶を参考に、自然で一貫性のある応答をしてください。`;
  }
  
  return prompt;
}

/**
 * AI応答生成
 */
async function generateAIResponse(prompt: string): Promise<string> {
  try {
    const model = vertexAI.getGenerativeModel({
      model: 'gemini-1.5-pro',
      generationConfig: {
        temperature: 0.8,
        maxOutputTokens: 1000,
      }
    });
    
    const result = await model.generateContent(prompt);
    const response = result.response;
    
    return response.text() || 'すみません、応答を生成できませんでした。';
  } catch (error) {
    logger.error('Error generating AI response:', error);
    throw error;
  }
}

/**
 * メイン処理：記憶検索付きチャット
 */
export const chatWithMemory = onCall(
  { cors: true },
  async (request) => {
    const { userId, characterId, message, language = 'ja' } = request.data as ChatRequest;
    
    // 入力検証
    if (!userId || !characterId || !message) {
      throw new HttpsError('invalid-argument', 'Missing required parameters');
    }
    
    logger.info('Chat with memory request:', {
      userId,
      characterId,
      messageLength: message.length,
      language
    });
    
    try {
      // 1. 課金状態確認
      const isPaidUser = await checkUserSubscription(userId);
      
      logger.info(`User ${userId} subscription status:`, {
        isPaid: isPaidUser
      });
      
      // 2. キャラクター情報取得
      const character = await getCharacterInfo(characterId);
      
      // 3. 短期記憶取得（全ユーザー共通）
      const shortTermMemories = await getShortTermMemories(userId, characterId);
      
      let longTermMemories: LongTermMemory[] = [];
      
      // 4. 長期記憶検索（有料ユーザー限定）
      if (isPaidUser) {
        logger.info('Searching long-term memories for paid user');
        
        const queryEmbedding = await generateEmbedding(message);
        longTermMemories = await searchLongTermMemories(userId, characterId, queryEmbedding);
        
        logger.info(`Found ${longTermMemories.length} relevant long-term memories`);
      } else {
        logger.info('Skipping long-term memory search for free user');
      }
      
      // 5. 動的プロンプト生成
      const prompt = buildPrompt(
        character,
        shortTermMemories,
        longTermMemories,
        message,
        language,
        isPaidUser
      );
      
      // 6. AI応答生成
      const aiResponse = await generateAIResponse(prompt);
      
      // 7. 応答返却
      return {
        success: true,
        response: aiResponse,
        memoryStats: {
          shortTermCount: shortTermMemories.length,
          longTermCount: longTermMemories.length,
          isPaidUser
        }
      };
      
    } catch (error) {
      logger.error('Error in chat with memory:', error);
      throw new HttpsError('internal', 'Failed to generate response');
    }
  }
);
