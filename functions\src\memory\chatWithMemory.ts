/**
 * 有料ユーザー限定 - 記憶検索付きチャット応答生成Cloud Function
 * 長期記憶（Supabase）+ 短期記憶（Firestore）を組み合わせたハイブリッド記憶システム
 */

import * as functions from 'firebase-functions';
import { createClient } from '@supabase/supabase-js';

// Supabase初期化（Firebase Functions Config使用）
const supabaseUrl = process.env.SUPABASE_PROJECT_URL || functions.config().supabase?.project_url;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || functions.config().supabase?.service_role_key;

let supabase: any = null;
if (supabaseUrl && supabaseKey) {
  supabase = createClient(supabaseUrl, supabaseKey);
  functions.logger.info('Supabase client initialized successfully');
} else {
  functions.logger.warn('Supabase credentials not configured. Memory system will be disabled.');
}

// Google AI API用の設定
const GOOGLE_AI_API_KEY = process.env.GOOGLE_AI_API_KEY || functions.config().gemini?.api_key;

interface ChatRequest {
  userId: string;
  characterId: string;
  message: string;
  language: 'ja' | 'en';
}

interface LongTermMemory {
  id: string;
  content: string;
  importance_score: number;
  emotion_context?: string;
  conversation_context?: any;
  tags?: string[];
  similarity: number;
  created_at: string;
}

interface ShortTermMessage {
  content: string;
  sender: 'user' | 'ai';
  createdAt: any;
}

/**
 * 課金状態確認
 */
async function checkUserSubscription(userId: string): Promise<boolean> {
  try {
    // Firebase Admin SDKを使用してユーザー情報を取得
    const admin = require('firebase-admin');
    const db = admin.firestore();

    const userDoc = await db.collection('users').doc(userId).get();

    if (!userDoc.exists) {
      return false;
    }

    const userData = userDoc.data();
    const subscriptionTier = userData?.subscriptionTier || 'free';

    return subscriptionTier !== 'free';
  } catch (error) {
    functions.logger.error('Error checking user subscription:', error);
    return false;
  }
}

/**
 * テキストをベクトル化（Google AI API使用）
 */
async function generateEmbedding(text: string): Promise<number[]> {
  try {
    if (!GOOGLE_AI_API_KEY) {
      throw new Error('Google AI API key not configured');
    }

    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:embedContent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-goog-api-key': GOOGLE_AI_API_KEY
      },
      body: JSON.stringify({
        content: {
          parts: [{ text: text }]
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Embedding API error: ${response.status}`);
    }

    const data = await response.json();

    if (!data.embedding?.values) {
      throw new Error('No embedding values returned');
    }

    return data.embedding.values;
  } catch (error) {
    functions.logger.error('Error generating embedding:', error);
    throw error;
  }
}

/**
 * 長期記憶検索（有料ユーザー限定）
 */
async function searchLongTermMemories(
  userId: string,
  characterId: string,
  queryEmbedding: number[]
): Promise<LongTermMemory[]> {
  try {
    if (!supabase) {
      functions.logger.warn('Supabase not configured, skipping memory search');
      return [];
    }

    const { data, error } = await supabase.rpc('search_similar_memories', {
      query_embedding: queryEmbedding,
      target_user_id: userId,
      target_character_id: characterId,
      similarity_threshold: 0.7,
      max_results: 5
    });

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    functions.logger.error('Error searching long-term memories:', error);
    return [];
  }
}

/**
 * 短期記憶取得（全ユーザー共通）
 */
async function getShortTermMemories(
  userId: string,
  characterId: string
): Promise<ShortTermMessage[]> {
  try {
    // Firebase Admin SDKを使用してメッセージを取得
    const admin = require('firebase-admin');
    const db = admin.firestore();

    const messagesRef = db.collection('messages')
      .where('userId', '==', userId)
      .where('characterId', '==', characterId)
      .orderBy('createdAt', 'desc')
      .limit(10);

    const snapshot = await messagesRef.get();

    return snapshot.docs.map((doc: any) => {
      const data = doc.data();
      return {
        content: data.content,
        sender: data.sender,
        createdAt: data.createdAt
      };
    }).reverse(); // 時系列順に並び替え
  } catch (error) {
    functions.logger.error('Error getting short-term memories:', error);
    return [];
  }
}

/**
 * キャラクター情報取得
 */
async function getCharacterInfo(characterId: string) {
  try {
    // CharacterServiceから取得（既存の実装を利用）
    const response = await fetch(`${process.env.FUNCTIONS_URL}/api/characters/${characterId}`);
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(`Character not found: ${characterId}`);
    }
    
    return data.character;
  } catch (error) {
    functions.logger.error('Error getting character info:', error);
    throw error;
  }
}

/**
 * 動的プロンプト生成
 */
function buildPrompt(
  character: any,
  shortTermMemories: ShortTermMessage[],
  longTermMemories: LongTermMemory[],
  currentMessage: string,
  language: 'ja' | 'en',
  isPaidUser: boolean
): string {
  const systemPrompt = character.systemPrompt?.[language] || character.systemPrompt?.en || '';
  
  let prompt = `${systemPrompt}\n\n`;
  
  // 有料ユーザーの場合のみ長期記憶を追加
  if (isPaidUser && longTermMemories.length > 0) {
    prompt += `=== 長期記憶 (Long-term Memory) ===\n`;
    prompt += `以下は過去の重要な会話や出来事です：\n\n`;
    
    longTermMemories.forEach((memory, index) => {
      prompt += `記憶${index + 1} (重要度: ${memory.importance_score.toFixed(2)}, 類似度: ${memory.similarity.toFixed(2)}):\n`;
      prompt += `${memory.content}\n`;
      if (memory.emotion_context) {
        prompt += `感情的文脈: ${memory.emotion_context}\n`;
      }
      prompt += `\n`;
    });
    
    prompt += `=== 長期記憶終了 ===\n\n`;
  }
  
  // 短期記憶（全ユーザー共通）
  if (shortTermMemories.length > 0) {
    prompt += `=== 短期記憶 (Recent Conversation) ===\n`;
    
    shortTermMemories.forEach(msg => {
      const speaker = msg.sender === 'user' ? 'ユーザー' : character.name?.[language] || 'AI';
      prompt += `${speaker}: ${msg.content}\n`;
    });
    
    prompt += `=== 短期記憶終了 ===\n\n`;
  }
  
  // 現在のメッセージ
  prompt += `現在のユーザーメッセージ: ${currentMessage}\n\n`;
  
  // 応答指示
  if (isPaidUser) {
    prompt += `上記の長期記憶と短期記憶を参考に、自然で一貫性のある応答をしてください。`;
  } else {
    prompt += `上記の短期記憶を参考に、自然で一貫性のある応答をしてください。`;
  }
  
  return prompt;
}

/**
 * AI応答生成（Google AI API使用）
 */
async function generateAIResponse(prompt: string): Promise<string> {
  try {
    if (!GOOGLE_AI_API_KEY) {
      throw new Error('Google AI API key not configured');
    }

    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-goog-api-key': GOOGLE_AI_API_KEY
      },
      body: JSON.stringify({
        contents: [{
          parts: [{ text: prompt }]
        }],
        generationConfig: {
          temperature: 0.8,
          maxOutputTokens: 1000,
        }
      })
    });

    if (!response.ok) {
      throw new Error(`AI API error: ${response.status}`);
    }

    const data = await response.json();

    if (!data.candidates?.[0]?.content?.parts?.[0]?.text) {
      throw new Error('No response text generated');
    }

    return data.candidates[0].content.parts[0].text;
  } catch (error) {
    functions.logger.error('Error generating AI response:', error);
    return 'すみません、応答を生成できませんでした。';
  }
}

/**
 * メイン処理：記憶検索付きチャット（1st Gen）
 */
export const chatWithMemory = functions.https.onCall(async (data, context) => {
  const { userId, characterId, message, language = 'ja' } = data as ChatRequest;

  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  // 入力検証
  if (!userId || !characterId || !message) {
    throw new functions.https.HttpsError('invalid-argument', 'Missing required parameters');
  }

    functions.logger.info('Chat with memory request:', {
      userId,
      characterId,
      messageLength: message.length,
      language
    });

    try {
      // 1. 課金状態確認
      const isPaidUser = await checkUserSubscription(userId);

      functions.logger.info(`User ${userId} subscription status:`, {
        isPaid: isPaidUser
      });
      
      // 2. キャラクター情報取得
      const character = await getCharacterInfo(characterId);
      
      // 3. 短期記憶取得（全ユーザー共通）
      const shortTermMemories = await getShortTermMemories(userId, characterId);
      
      let longTermMemories: LongTermMemory[] = [];
      
      // 4. 長期記憶検索（有料ユーザー限定）
      if (isPaidUser) {
        functions.logger.info('Searching long-term memories for paid user');

        const queryEmbedding = await generateEmbedding(message);
        longTermMemories = await searchLongTermMemories(userId, characterId, queryEmbedding);

        functions.logger.info(`Found ${longTermMemories.length} relevant long-term memories`);
      } else {
        functions.logger.info('Skipping long-term memory search for free user');
      }
      
      // 5. 動的プロンプト生成
      const prompt = buildPrompt(
        character,
        shortTermMemories,
        longTermMemories,
        message,
        language,
        isPaidUser
      );
      
      // 6. AI応答生成
      const aiResponse = await generateAIResponse(prompt);
      
      // 7. 応答返却
      return {
        success: true,
        response: aiResponse,
        memoryStats: {
          shortTermCount: shortTermMemories.length,
          longTermCount: longTermMemories.length,
          isPaidUser
        }
      };

    } catch (error) {
      functions.logger.error('Error in chat with memory:', error);
      throw new functions.https.HttpsError('internal', 'Failed to generate response');
    }
  });
