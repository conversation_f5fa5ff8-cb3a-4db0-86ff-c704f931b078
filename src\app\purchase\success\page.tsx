"use client";

import { useEffect, useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { useI18n } from "@/contexts/I18nProvider";

function PurchaseSuccessContent() {
  const [loading, setLoading] = useState(true);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const { t, language } = useI18n();

  useEffect(() => {
    const sessionIdParam = searchParams.get('session_id');
    if (sessionIdParam) {
      setSessionId(sessionIdParam);
    }
    setLoading(false);
  }, [searchParams]);

  const handleContinue = () => {
    const homeUrl = language === 'ja' ? '/ja' : '/';
    router.push(homeUrl);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 to-secondary/10 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
        {/* Success Icon */}
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>

        {/* Success Message */}
        <h1 className="text-2xl font-bold text-gray-800 mb-4">
          {language === 'ja' ? '購入完了！' : 'Purchase Successful!'}
        </h1>

        <p className="text-gray-600 mb-6">
          {language === 'ja'
            ? 'ポイントの購入が正常に完了しました。ポイントがアカウントに追加されました。'
            : 'Your point purchase has been completed successfully. Points have been added to your account.'
          }
        </p>

        {sessionId && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <p className="text-sm text-gray-600">
              {language === 'ja' ? '取引ID:' : 'Transaction ID:'}
            </p>
            <p className="text-xs text-gray-500 font-mono break-all">
              {sessionId}
            </p>
          </div>
        )}

        {/* Continue Button */}
        <button
          onClick={handleContinue}
          className="w-full bg-primary text-white py-3 rounded-lg font-medium hover:bg-blue-600 transition-colors"
        >
          {language === 'ja' ? 'チャットを続ける' : 'Continue Chatting'}
        </button>

        {/* Additional Info */}
        <p className="text-sm text-gray-500 mt-4">
          {language === 'ja'
            ? 'ポイントは即座にアカウントに反映されます。'
            : 'Points are reflected in your account immediately.'
          }
        </p>
      </div>
    </div>
  );
}

export default function PurchaseSuccessPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    }>
      <PurchaseSuccessContent />
    </Suspense>
  );
}
