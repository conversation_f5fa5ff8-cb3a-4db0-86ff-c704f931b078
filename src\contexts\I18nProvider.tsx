"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { usePathname } from 'next/navigation';
import '@/lib/i18n'; // Initialize i18n

interface I18nContextType {
  language: 'en' | 'ja';
  changeLanguage: (lang: 'en' | 'ja') => void;
  t: (key: string, options?: any) => string;
  isLoading: boolean;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

export function useI18n() {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
}

export function I18nProvider({ children }: { children: React.ReactNode }) {
  const [isLoading, setIsLoading] = useState(true);
  const [mounted, setMounted] = useState(false);
  const [language, setLanguage] = useState<'en' | 'ja'>('en');

  // Safe hooks usage with error handling
  let t: (key: string, options?: any) => string;
  let i18n: any;
  let pathname = '/';

  try {
    const translation = useTranslation();
    t = translation.t;
    i18n = translation.i18n;
  } catch (error) {
    console.warn('useTranslation hook failed, using fallback:', error);
    t = (key: string) => key; // Fallback function
    i18n = null;
  }

  try {
    pathname = usePathname();
  } catch (error) {
    console.warn('usePathname hook failed, using fallback:', error);
    pathname = '/';
  }

  // Determine language from pathname for SSR compatibility
  const getLanguageFromPath = (path: string): 'en' | 'ja' => {
    return path.startsWith('/ja') ? 'ja' : 'en';
  };

  // Handle mounting to avoid SSR mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    const initializeLanguage = async () => {
      try {
        // Get language from current pathname
        const urlLang = getLanguageFromPath(pathname);

        // Use URL language as priority, fallback to localStorage
        const savedLang = typeof window !== 'undefined'
          ? localStorage.getItem('language') as 'en' | 'ja' | null
          : null;

        const initialLang = urlLang || savedLang || 'en';

        // Only call i18n if it's available
        if (i18n && typeof i18n.changeLanguage === 'function') {
          await i18n.changeLanguage(initialLang);
        }

        setLanguage(initialLang);

        // Update HTML lang attribute
        if (typeof window !== 'undefined') {
          document.documentElement.lang = initialLang;
        }

        console.log('Language initialized:', initialLang, 'from URL:', urlLang);
      } catch (error) {
        console.error('Error initializing language:', error);
        // Set default language on error
        setLanguage('en');
      } finally {
        setIsLoading(false);
      }
    };

    initializeLanguage();
  }, [i18n, pathname, mounted]);

  const changeLanguage = async (lang: 'en' | 'ja') => {
    if (!mounted) return;

    try {
      setIsLoading(true);

      // Only call i18n if it's available
      if (i18n && typeof i18n.changeLanguage === 'function') {
        await i18n.changeLanguage(lang);
      }

      setLanguage(lang);

      // Save to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('language', lang);

        // Update HTML lang attribute
        document.documentElement.lang = lang;
      }
    } catch (error) {
      console.error('Error changing language:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const value: I18nContextType = {
    language,
    changeLanguage,
    t,
    isLoading,
  };

  return (
    <I18nContext.Provider value={value}>
      {children}
    </I18nContext.Provider>
  );
}
