// Unified Character Template - All characters must follow this exact structure
// No character-specific files or configurations allowed

export interface UnifiedCharacterData {
  // Core identification (required)
  id: string;
  characterId: string;

  // Basic information (required)
  name: {
    en: string;
    ja: string;
  };
  description: {
    en: string;
    ja: string;
  };

  // Character details (required)
  age: number | {
    en: string;
    ja: string;
  };
  occupation: {
    en: string;
    ja: string;
  };
  personality: {
    en: string[];
    ja: string[];
  };
  interests: {
    en: string[];
    ja: string[];
  };
  background: {
    en: string;
    ja: string;
  };
  welcomeMessage: {
    en: string;
    ja: string;
  };

  // Media (required)
  media: {
    profileImage: string;
    thumbnailImage: string;
    idleVideo: string;
    speakingVideo: string;
    listeningVideo?: string;
    voiceSample?: string;
  };

  // AI Configuration (required)
  aiProvider: string;
  aiModel: string;
  aiConfig: {
    provider: string;
    model: string;
    temperature: number;
    maxTokens: number;
  };

  // Voice Configuration (required - MUST be consistent)
  voiceProvider: 'elevenlabs' | 'minimax';
  voiceConfig: {
    provider: 'elevenlabs' | 'minimax';
    voiceId: string;
  };

  // Provider-specific voice settings (optional)
  elevenLabsVoiceId?: string;
  minimaxVoiceId?: string;
  minimaxModel?: string;
  customVoiceId?: string;

  // Video sets (required)
  videoSets: Array<{
    id: string;
    normalVideo: object;
    lipSyncVideo: object;
    uploaded: boolean;
    normalVideoPath: string;
    lipSyncVideoPath: string;
  }>;

  // System prompts (required)
  systemPrompt: {
    en: string;
    ja: string;
  };

  // Status and metadata (required)
  isActive: boolean;
  isPremium: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

// Default template for new characters
export const createCharacterTemplate = (id: string): UnifiedCharacterData => ({
  id,
  characterId: id,
  
  name: {
    en: '',
    ja: ''
  },
  description: {
    en: '',
    ja: ''
  },
  
  age: 20,
  occupation: {
    en: '',
    ja: ''
  },
  personality: {
    en: [],
    ja: []
  },
  interests: {
    en: [],
    ja: []
  },
  background: {
    en: '',
    ja: ''
  },
  welcomeMessage: {
    en: '',
    ja: ''
  },
  
  media: {
    profileImage: `/characters/images/${id}-profile.jpg`,
    thumbnailImage: `/characters/images/${id}-thumb.jpg`,
    idleVideo: `/characters/videos/${id}-idle.mp4`,
    speakingVideo: `/characters/videos/${id}-speaking.mp4`
  },
  
  aiProvider: 'openrouter.ai',
  aiModel: 'google/gemini-2.0-flash-001',
  aiConfig: {
    provider: 'openrouter.ai',
    model: 'google/gemini-2.0-flash-001',
    temperature: 0.7,
    maxTokens: 500
  },
  
  voiceProvider: 'elevenlabs',
  voiceConfig: {
    provider: 'elevenlabs',
    voiceId: ''
  },
  
  elevenLabsVoiceId: '',
  
  videoSets: [{
    id: '1',
    normalVideo: {},
    lipSyncVideo: {},
    uploaded: true,
    normalVideoPath: `/characters/video-sets/${id}/${id}-set1-normal.mp4`,
    lipSyncVideoPath: `/characters/video-sets/${id}/${id}-set1-lipsync.mp4`
  }],
  
  systemPrompt: {
    en: '',
    ja: ''
  },
  
  isActive: true,
  isPremium: false,
  sortOrder: 1,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
});

// Strict validation function
export const validateCharacterData = (character: any): string[] => {
  const errors: string[] = [];

  // Required fields
  if (!character.id) errors.push('Missing id');
  if (!character.name?.en) errors.push('Missing name.en');
  if (!character.name?.ja) errors.push('Missing name.ja');
  if (!character.voiceProvider) errors.push('Missing voiceProvider');
  if (!character.voiceConfig?.provider) errors.push('Missing voiceConfig.provider');
  if (!character.voiceConfig?.voiceId) errors.push('Missing voiceConfig.voiceId');
  if (!character.aiProvider) errors.push('Missing aiProvider');
  if (!character.aiConfig?.provider) errors.push('Missing aiConfig.provider');
  if (!character.systemPrompt?.en) errors.push('Missing systemPrompt.en');
  if (!character.systemPrompt?.ja) errors.push('Missing systemPrompt.ja');

  // Voice provider consistency check
  if (character.voiceProvider !== character.voiceConfig?.provider) {
    errors.push('voiceProvider and voiceConfig.provider must match');
  }

  // Age must be number
  if (typeof character.age !== 'number') {
    errors.push('age must be a number');
  }

  return errors;
};

// Normalize existing character to unified format
export const normalizeCharacterData = (character: any): UnifiedCharacterData => {
  const template = createCharacterTemplate(character.id);

  // Convert age to number if it's an object
  let normalizedAge = 20;
  if (typeof character.age === 'number') {
    normalizedAge = character.age;
  } else if (typeof character.age === 'object' && character.age?.en) {
    normalizedAge = parseInt(character.age.en) || 20;
  }

  // Determine voice provider and ensure consistency
  const voiceProvider = character.voiceProvider ||
    character.voiceConfig?.provider ||
    (character.elevenLabsVoiceId ? 'elevenlabs' : 'minimax');

  const voiceId = character.voiceConfig?.voiceId ||
    character.elevenLabsVoiceId ||
    character.minimaxVoiceId ||
    character.customVoiceId || '';

  return {
    ...template,
    ...character,
    characterId: character.id,

    // Normalize age to number
    age: normalizedAge,

    // Ensure arrays exist
    personality: {
      en: Array.isArray(character.personality?.en) ? character.personality.en : [],
      ja: Array.isArray(character.personality?.ja) ? character.personality.ja : []
    },
    interests: {
      en: Array.isArray(character.interests?.en) ? character.interests.en : [],
      ja: Array.isArray(character.interests?.ja) ? character.interests.ja : []
    },

    // Ensure voice configuration is complete and consistent
    voiceProvider: voiceProvider as 'elevenlabs' | 'minimax',
    voiceConfig: {
      provider: voiceProvider as 'elevenlabs' | 'minimax',
      voiceId: voiceId
    },

    // Set provider-specific fields
    elevenLabsVoiceId: voiceProvider === 'elevenlabs' ? voiceId : undefined,
    minimaxVoiceId: voiceProvider === 'minimax' ? voiceId : undefined,
    customVoiceId: voiceProvider === 'minimax' ? voiceId : undefined,

    // Ensure video sets exist
    videoSets: character.videoSets || template.videoSets,

    // Ensure system prompt exists
    systemPrompt: character.systemPrompt || template.systemPrompt,

    // Update timestamp
    updatedAt: new Date().toISOString()
  };
};
