import { NextRequest, NextResponse } from 'next/server';
import { voiceCache as advancedVoiceCache } from '@/lib/voiceCache';
import { getAPIKey as getSecretAPIKey, getSecret } from '@/lib/secretManager';


// Force dynamic rendering - using Node.js runtime for file system access
export const dynamic = 'force-dynamic';
// Note: Edge Runtime disabled due to fs module dependency
// export const runtime = 'edge';
export const preferredRegion = 'auto';

// Get character data from mock data (Edge Runtime compatible) - Currently unused
// async function getCharacterData(characterId: string): Promise<any> {
//   // Find character in mock data
//   const character = mockCharacters.find(char => char.id === characterId);
//   if (character) {
//     return character;
//   }

//   // If not found in mock data, return null
//   return null;
// }

// Admin authentication check for test mode
function isAdminTestMode(request: NextRequest): boolean {
  const adminToken = request.headers.get('x-admin-token');
  const expectedToken = process.env.NEXT_PUBLIC_ADMIN_TOKEN || 'your-secure-admin-token-here-change-this';
  return adminToken === expectedToken;
}

interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

interface ChatRequest {
  message: string;
  characterId: string;
  userId?: string;
  userName?: string; // Add user's actual name
  language?: 'ja' | 'en';
  conversationHistory?: ChatMessage[];
  testMode?: boolean;
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  let userId = 'anonymous';

  try {
    const { message, characterId, userId: _requestUserId, userName, language = 'ja', conversationHistory = [], testMode = false }: ChatRequest = await request.json();

    // Generate or extract user ID for statistics
    userId = testMode ? 'test-user' : request.headers.get('x-user-id') || 'anonymous-' + Date.now();

    console.log('=== Chat API Request ===');
    console.log('Message:', message);
    console.log('Character ID:', characterId);
    console.log('User Name:', userName);
    console.log('Language:', language);
    console.log('Test Mode:', testMode);
    console.log('User ID:', userId);

    // Validate required fields
    if (!message || !characterId) {
      return NextResponse.json(
        { success: false, error: 'Message and character ID are required' },
        { status: 400 }
      );
    }

    // Check admin authentication for test mode
    if (testMode && !isAdminTestMode(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized test mode access' },
        { status: 401 }
      );
    }

   // Get character data with fallback mechanisms
   console.log('Loading character:', characterId);
   let character = null;

   try {
     // Force reload characters to get latest settings from file
     const { getCharacterData, forceReloadCharacters } = await import('@/lib/characterManager');

     // Force reload to ensure we have the latest character settings
     const reloadSuccess = forceReloadCharacters();
     if (reloadSuccess) {
       console.log('Characters force reloaded successfully');
     } else {
       console.warn('Characters force reload failed, using cached data');
     }

     character = getCharacterData(characterId);

     if (character) {
       console.log('Character loaded successfully from character manager');
       console.log('Character AI Provider:', character.aiProvider);
       console.log('Character AI Model:', character.aiModel);
       console.log('Character AI Config:', character.aiConfig);
     } else {
       console.log('Character not found in character manager');
     }
   } catch (error: any) {
     console.error('=== CHARACTER MANAGER ERROR ===');
     console.error('Error Type:', error?.name || 'Unknown type');
     console.error('Error Message:', error?.message || 'No message');
     console.error('Character ID:', characterId);
     console.error('User ID:', userId);
     console.error('Stack Trace:', error?.stack || 'No stack trace');
     
     try {
       // Fallback: Try loading from server file manager
       console.log('Falling back to server file manager');
       const { loadCharactersFromServer } = await import('@/lib/serverFileManager');
       const serverCharacters = loadCharactersFromServer();
       character = serverCharacters[characterId];
       
       if (character) {
         console.log('Character loaded successfully from server files');
         console.log('Fallback character AI settings:', {
           aiProvider: character.aiProvider,
           aiModel: character.aiModel,
           aiConfig: character.aiConfig
         });
       } else {
         console.log('Character not found in server files');
       }
     } catch (fallbackError: any) {
       console.error('=== SERVER FILE MANAGER ERROR ===');
       console.error('Error:', fallbackError);
       console.log('All loading attempts failed, using mock data');
     }
   }

    // If character not found, create fallback without AI config
    if (!character) {
      console.log('Character not found, creating fallback...');
      character = {
        id: characterId,
        name: { en: 'AI Character', ja: 'AIキャラクター' },
        description: { en: 'A friendly AI character', ja: 'フレンドリーなAIキャラクター' },
        systemPrompt: {
          en: 'You are a friendly and helpful AI assistant.',
          ja: 'あなたはフレンドリーで親切なAIアシスタントです。'
        },
        // No AI config in fallback - will use defaults
        voiceConfig: {
          voiceId: '1LfCuIldvlGQ7B987Q6L'
        }
      };
      console.log('Using fallback character data');
    }

    if (!character) {
      console.error('Character not found:', characterId);
      return NextResponse.json(
        { success: false, error: 'Character not found' },
        { status: 404 }
      );
    }

    // Final fallback: Use mock data if all else fails
   if (!character) {
     console.log('Using mock character data as final fallback');
     try {
       const { mockCharacters } = await import('@/lib/mockData');
       character = mockCharacters.find(char => char.id === characterId);
     } catch (mockError) {
       console.error('Failed to load mock data:', mockError);
     }
     
     // Ultimate fallback: Create basic character
     if (!character) {
       console.log('Creating basic fallback character');
       character = {
         id: characterId,
         name: { en: 'AI Character', ja: 'AIキャラクター' },
         description: { en: 'A friendly AI character', ja: 'フレンドリーなAIキャラクター' },
         systemPrompt: {
           en: 'You are a friendly and helpful AI assistant.',
           ja: 'あなたはフレンドリーで親切なAIアシスタントです。'
         },
         aiConfig: {
           model: 'gpt-3.5-turbo',
           temperature: 0.7,
           maxTokens: 500
         },
         voiceConfig: {
           voiceId: '1LfCuIldvlGQ7B987Q6L'
         }
       };
     }
   }

    // Build AI configuration from character settings
    console.log('=== CHARACTER AI SETTINGS DEBUG ===');
    console.log('Character ID:', characterId);
    console.log('Character aiProvider:', (character as any).aiProvider);
    console.log('Character aiModel:', (character as any).aiModel);
    console.log('Character aiConfig:', character.aiConfig);
    console.log('Character elevenLabsVoiceId:', (character as any).elevenLabsVoiceId);

    // Use character-specific AI configuration from admin settings
    const aiConfig = {
      provider: (character as any).aiProvider || 'openrouter.ai',
      model: (character as any).aiModel || 'gpt-3.5-turbo',
      temperature: character.aiConfig?.temperature || 0.7,
      maxTokens: character.aiConfig?.maxTokens || 150
    };

    // Ensure voice configuration is properly set
    if (!(character as any).elevenLabsVoiceId && !character.voiceConfig?.voiceId) {
      console.warn(`Character ${characterId} has no voice configuration`);
    }

    console.log('Character loaded:', character.name);
    console.log('AI Provider:', aiConfig.provider);
    console.log('AI Model:', aiConfig.model);
    console.log('AI Config:', aiConfig);

    // Build system prompt with global and character prompts
    console.log('Loading prompts for character:', characterId, 'language:', language);

    // Use secure prompt template engine to prevent injection
    console.log('Building secure messages using template engine');

    let messages: ChatMessage[];

    try {
      // Import secure prompt manager
      const { getSecureMessagesForCharacter } = await import('@/lib/promptManager');

      // Get character name for context
      const characterName = typeof character.name === 'string'
        ? character.name
        : character.name?.[language] || character.name?.en || characterId;

      // Build secure messages array (enforces global + character prompt requirement)
      messages = getSecureMessagesForCharacter(
        characterId,
        conversationHistory,
        message,
        {
          language: language as 'ja' | 'en',
          userName: userName || undefined,
          characterName
        }
      );

      console.log(`✅ Secure messages built for ${characterId} with ${messages.length} messages`);

    } catch (promptError: any) {
      console.error('=== SECURE PROMPT BUILDING ERROR ===');
      console.error('Error Type:', promptError?.name || 'Unknown type');
      console.error('Error Message:', promptError?.message || 'No message');
      console.error('Character ID:', characterId);
      console.error('Language:', language);
      console.error('User ID:', userId);
      console.error('Stack Trace:', promptError?.stack || 'No stack trace');

      // REMOVE FALLBACK: Let the error bubble up to identify the root cause
      throw new Error(`Failed to build secure prompts for character ${characterId}: ${promptError.message}`);
    }

    let aiResponse = '';
    let audioUrl: string | null = null;

    // Call AI API based on character settings - STRICTLY use character's configured AI only
    try {
      let aiPromise: Promise<string>;
      let voicePromise: Promise<string | null> = Promise.resolve(null);

      // Debug AI configuration
      console.log('=== AI CONFIGURATION DEBUG ===');
      console.log('Character ID:', characterId);
      console.log('Character object keys:', Object.keys(character));
      console.log('AI Config loaded:', JSON.stringify(aiConfig, null, 2));
      console.log('Character aiConfig:', JSON.stringify(character.aiConfig, null, 2));
      console.log('Character ai:', JSON.stringify(character.ai, null, 2));

      if (aiConfig && aiConfig.model) {
        console.log(`✅ Using AI provider: ${aiConfig.provider} with model: ${aiConfig.model}`);

        // Check API key availability
        const apiKey = await getSecretAPIKey(aiConfig.provider);
        console.log(`API Key for ${aiConfig.provider}:`, apiKey ? 'Available' : 'NOT FOUND');

        console.log(`=== AI USAGE ===`);
        console.log(`Character: ${characterId}`);
        console.log(`Configured AI Model: ${aiConfig.model}`);
        console.log(`Temperature: ${aiConfig.temperature}`);
        console.log(`Max Tokens: ${aiConfig.maxTokens}`);
        console.log(`AI Provider: ${aiConfig.provider}`);

        // Call appropriate AI provider
        console.log('🚀 Calling AI provider...');
        aiPromise = callAIProvider(aiConfig.provider, aiConfig.model, messages, aiConfig);
      } else {
        console.error(`❌ Character ${characterId} has NO AI configuration`);
        console.log('Character data structure:', JSON.stringify(character, null, 2));
        throw new Error(`Character ${characterId} has no AI configuration. Please configure AI provider and model in admin settings.`);
      }

      // Wait for AI response first
      aiResponse = await aiPromise;

      // Generate voice immediately after AI response
      const voiceProvider = character.voiceProvider || character.voiceConfig?.provider || 'elevenlabs';
      let voiceId: string;

      // Get voice ID based on provider with admin settings priority
      switch (voiceProvider) {
        case 'minimax':
          // Priority: 1) voiceConfig.voiceId (admin UI), 2) customVoiceId, 3) minimaxVoiceId, 4) elevenLabsVoiceId
          voiceId = character.voiceConfig?.voiceId ||
                   (character as any).customVoiceId ||
                   character.minimaxVoiceId ||
                   (character as any).elevenLabsVoiceId;
          break;
        case 'elevenlabs':
        default:
          // Priority: 1) voiceConfig.voiceId (admin UI), 2) elevenLabsVoiceId
          voiceId = character.voiceConfig?.voiceId || (character as any).elevenLabsVoiceId;
          break;
      }
      // Start voice generation in parallel with AI response
      const isMiniMaxCharacter = character?.voiceProvider === 'minimax' ||
                               character?.voiceConfig?.provider === 'minimax';

      if (voiceId && !isMiniMaxCharacter) {
        console.log('🎵 Starting parallel voice generation for non-MiniMax character');
        const voiceStartTime = Date.now();

        voicePromise = generateVoice(voiceId, aiResponse, characterId, language, voiceProvider)
          .then(audioUrl => {
            const voiceTime = Date.now() - voiceStartTime;
            console.log(`Parallel voice generation completed in ${voiceTime}ms`);
            return audioUrl;
          })
          .catch(voiceError => {
            console.warn('Parallel voice generation failed:', voiceError);
            return null;
          });
      } else if (isMiniMaxCharacter) {
        console.log('🎵 MiniMax character detected - will use client-side streaming');
        voicePromise = Promise.resolve(null);
      }

      // Wait for voice generation with reasonable timeout (only for non-MiniMax)
      if (voicePromise && !isMiniMaxCharacter) {
        try {
          audioUrl = await Promise.race([
            voicePromise,
            new Promise<null>((resolve) => setTimeout(() => {
              console.log('Voice generation timeout (5s), sending response without audio');
              resolve(null);
            }, 5000)) // Increased to 5 seconds for MiniMax streaming
          ]);
        } catch (error: any) {
          console.error('=== VOICE GENERATION TIMEOUT ERROR ===');
          console.error('Error Type:', error?.name || 'Unknown type');
          console.error('Error Message:', error?.message || 'No message');
          console.error('Character ID:', characterId);
          console.error('Voice ID:', voiceId);
          console.error('Language:', language);
          console.error('AI Response Length:', aiResponse?.length || 0);
          console.error('User ID:', userId);
          console.error('Stack Trace:', error?.stack || 'No stack trace');
          console.error('Original Error Object:', error);
          console.warn('Voice generation error:', error);
          audioUrl = null;
        }

        // If voice generation is still in progress, continue in background
        if (!audioUrl && voicePromise) {
          console.log('Voice generation continuing in background...');
          voicePromise.then(backgroundAudio => {
            if (backgroundAudio) {
              console.log('Background voice generation completed, but response already sent');
              // Could potentially cache this for next request or send via WebSocket
            }
          }).catch(err => {
            console.warn('Background voice generation failed:', err);
          });
        }
      }

    } catch (aiError: any) {
      console.error(`=== AI API CALL ERROR for ${characterId} ===`);
      console.error('Error Type:', aiError?.name || 'Unknown type');
      console.error('Error Message:', aiError?.message || 'No message');
      console.error('Character ID:', characterId);
      console.error('User ID:', userId);
      console.error('User Name:', userName || 'Not provided');
      console.error('Language:', language);
      console.error('Configured Model:', aiConfig?.model || 'No model configured');
      console.error('AI Config:', aiConfig);
      console.error('Message Length:', message?.length || 0);
      console.error('Stack Trace:', aiError?.stack || 'No stack trace');
      console.error('Original Error Object:', aiError);
      console.error('Error details:', aiError);

      // Don't use fallback - let the error bubble up
      throw aiError;
    }

    // Record successful message statistics (removed for production)
    const responseTime = Date.now() - startTime;
    console.log(`Message processed successfully in ${responseTime}ms`);

    // Final performance logging
    const totalResponseTime = Date.now() - startTime;
    console.log(`=== ULTRA FAST Response Complete ===`);
    console.log(`Total response time: ${totalResponseTime}ms`);
    console.log(`Audio included: ${audioUrl ? 'YES' : 'NO'}`);
    console.log(`Character: ${characterId}`);

    // Check if character uses MiniMax for streaming info
    const isMiniMaxCharacter = character?.voiceProvider === 'minimax' ||
                             character?.voiceConfig?.provider === 'minimax';

    return NextResponse.json({
      success: true,
      response: aiResponse,
      audioUrl: audioUrl || null,
      streamingUrl: isMiniMaxCharacter ? `/api/voice/stream` : null, // Add streaming endpoint for MiniMax
      useStreaming: isMiniMaxCharacter, // Flag for client to use streaming
      characterId: characterId,
      responseTime: totalResponseTime // Include timing info for client
    });

  } catch (error: any) {
    console.error('=== CHAT API MAIN PROCESS CRITICAL ERROR ===');
    console.error('Error Type:', error?.name || 'Unknown type');
    console.error('Error Message:', error?.message || 'No message');
    console.error('User ID:', userId);
    console.error('Request URL:', request.url);
    console.error('Request Method:', request.method);
    console.error('User Agent:', request.headers.get('user-agent') || 'Unknown');
    console.error('Stack Trace:', error?.stack || 'No stack trace');
    console.error('Original Error Object:', error);
    console.error('Chat API error:', error);

    // Record error statistics (removed for production)
    const responseTime = Date.now() - startTime;
    console.log(`Error occurred after ${responseTime}ms: ${error instanceof Error ? error.message : 'Unknown error'}`);

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Call AI provider based on character settings
async function callAIProvider(provider: string, model: string, messages: ChatMessage[], aiConfig?: any): Promise<string> {
  console.log(`=== AI Provider Call ===`);
  console.log(`Provider: ${provider}`);
  console.log(`Model: ${model}`);
  console.log(`Messages count: ${messages.length}`);

  const apiKey = await getSecretAPIKey(provider);

  if (!apiKey) {
    console.error(`API key not found for provider: ${provider}`);
    throw new Error(`API key not found for provider: ${provider}. Please check your environment variables.`);
  }

  // Route to appropriate provider function
  switch (provider) {
    case 'openrouter.ai':
      return await callOpenRouter(apiKey, model, messages, aiConfig);

    case 'venice.ai':
      return await callVenice(apiKey, model, messages, aiConfig);

    case 'openai':
      return await callOpenAI(apiKey, model, messages, aiConfig);

    case 'anthropic':
      return await callAnthropic(apiKey, model, messages, aiConfig);

    case 'google':
      return await callGoogle(apiKey, model, messages, aiConfig);

    default:
      console.error(`Unsupported AI provider: ${provider}`);
      throw new Error(`Unsupported AI provider: ${provider}. Supported providers: openrouter.ai, venice.ai, openai, anthropic, google`);
  }
}

// OpenRouter API call
async function callOpenRouter(apiKey: string, model: string, messages: ChatMessage[], aiConfig?: any): Promise<string> {
  console.log('=== OpenRouter API Call ===');
  console.log('Model:', model);
  console.log('AI Config:', aiConfig);

  try {
    const requestBody = {
      model: model,
      messages: messages,
      max_tokens: aiConfig?.maxTokens || 500,
      temperature: aiConfig?.temperature || 0.7
    };

    console.log('Request body:', JSON.stringify(requestBody, null, 2));

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
        'X-Title': 'AiLuvChat'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('OpenRouter API error:', response.status, errorText);
      throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('OpenRouter response data:', JSON.stringify(data, null, 2));

    const content = data.choices[0]?.message?.content;
    const finishReason = data.choices[0]?.finish_reason;

    console.log('Extracted content:', content);
    console.log('Finish reason:', finishReason);

    if (!content || content.trim() === '') {
      console.warn('Empty response from OpenRouter API, finish_reason:', finishReason);
      throw new Error('Empty response from OpenRouter API');
    }

    console.log('Returning content:', content);
    return content;

  } catch (error) {
    console.error('OpenRouter API call failed:', error);
    throw error;
  }
}

// Venice AI API call
async function callVenice(apiKey: string, model: string, messages: ChatMessage[], aiConfig?: any): Promise<string> {
  console.log('=== Venice AI API Call ===');
  console.log('Model:', model);
  console.log('AI Config:', aiConfig);

  try {
    const requestBody = {
      model: model,
      messages: messages,
      max_tokens: aiConfig?.maxTokens || 500,
      temperature: aiConfig?.temperature || 0.7
    };

    const response = await fetch('https://api.venice.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Venice AI API error:', response.status, errorText);
      throw new Error(`Venice AI API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    const content = data.choices[0]?.message?.content;

    if (!content || content.trim() === '') {
      throw new Error('Empty response from Venice AI');
    }

    return content;
  } catch (error) {
    console.error('Venice AI API call failed:', error);
    throw error;
  }
}

//   try {
//     const requestBody = {
//       model: model,
//       messages: messages,
//       max_tokens: 500,
//       temperature: 0.7
//     };

//     console.log('Request body:', JSON.stringify(requestBody, null, 2));

//     const response = await fetch('https://api.venice.ai/api/v1/chat/completions', {
//       method: 'POST',
//       headers: {
//         'Authorization': `Bearer ${apiKey}`,
//         'Content-Type': 'application/json'
//       },
//       body: JSON.stringify(requestBody)
//     });

//     console.log('Venice response status:', response.status);
//     console.log('Venice response headers:', Object.fromEntries(response.headers.entries()));

//     if (!response.ok) {
//       const errorText = await response.text();
//       console.error('Venice AI API error:', response.status, errorText);
//       throw new Error(`Venice AI API error: ${response.status} - ${errorText}`);
//     }

//     const data = await response.json();
//     console.log('Venice AI response data:', JSON.stringify(data, null, 2));

//     const content = data.choices[0]?.message?.content;
//     const finishReason = data.choices[0]?.finish_reason;

//     console.log('Venice extracted content:', content);
//     console.log('Venice finish reason:', finishReason);

//     if (!content || content.trim() === '') {
//       console.warn('Empty response from Venice AI API, finish_reason:', finishReason);
//       throw new Error('Empty response from AI provider');
//     }

//     console.log('Returning Venice content:', content);
//     return content;

//   } catch (error) {
//     console.error('Venice AI API call failed:', error);
//     throw error;
//   }
// }

// OpenAI API call
async function callOpenAI(apiKey: string, model: string, messages: ChatMessage[], aiConfig?: any): Promise<string> {
  console.log('=== OpenAI API Call ===');
  console.log('Model:', model);
  console.log('AI Config:', aiConfig);

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: model,
        messages: messages,
        max_tokens: aiConfig?.maxTokens || 500,
        temperature: aiConfig?.temperature || 0.7
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('OpenAI API error:', response.status, errorText);
      throw new Error(`OpenAI API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    const content = data.choices[0]?.message?.content;

    if (!content || content.trim() === '') {
      throw new Error('Empty response from OpenAI');
    }

    return content;
  } catch (error) {
    console.error('OpenAI API call failed:', error);
    throw error;
  }
}

// Anthropic API call - Currently unused but kept for future use
async function callAnthropic(_apiKey: string, _model: string, _messages: ChatMessage[], _aiConfig?: any): Promise<string> {
  throw new Error('Anthropic API not implemented yet');
}
//   console.log('=== Anthropic API Call ===');
//   console.log('Model:', model);

//   try {
//     const systemMessage = messages.find(m => m.role === 'system');
//     const userMessages = messages.filter(m => m.role !== 'system');

//     const response = await fetch('https://api.anthropic.com/v1/messages', {
//       method: 'POST',
//       headers: {
//         'x-api-key': apiKey,
//         'Content-Type': 'application/json',
//         'anthropic-version': '2023-06-01'
//       },
//       body: JSON.stringify({
//         model: model,
//         system: systemMessage?.content || '',
//         messages: userMessages.map(m => ({
//           role: m.role === 'assistant' ? 'assistant' : 'user',
//           content: m.content
//         })),
//         max_tokens: 500
//       })
//     });

//     if (!response.ok) {
//       const errorText = await response.text();
//       console.error('Anthropic API error:', response.status, errorText);
//       throw new Error(`Anthropic API error: ${response.status} - ${errorText}`);
//     }

//     const data = await response.json();
//     const content = data.content[0]?.text;

//     if (!content || content.trim() === '') {
//       throw new Error('Empty response from Anthropic');
//     }

//     return content;
//   } catch (error) {
//     console.error('Anthropic API call failed:', error);
//     throw error;
//   }
// }

// Google AI API call - Currently unused but kept for future use
async function callGoogle(_apiKey: string, _model: string, _messages: ChatMessage[], _aiConfig?: any): Promise<string> {
  throw new Error('Google AI API not implemented yet');
}
//   console.log('=== Google AI API Call ===');
//   console.log('Model:', model);

//   try {
//     const systemMessage = messages.find(m => m.role === 'system');
//     const userMessages = messages.filter(m => m.role !== 'system');

//     const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`, {
//       method: 'POST',
//       headers: {
//         'Content-Type': 'application/json'
//       },
//       body: JSON.stringify({
//         contents: userMessages.map(m => ({
//           role: m.role === 'assistant' ? 'model' : 'user',
//           parts: [{ text: m.content }]
//         })),
//         systemInstruction: systemMessage ? {
//           parts: [{ text: systemMessage.content }]
//         } : undefined,
//         generationConfig: {
//           temperature: 0.7,
//           maxOutputTokens: 500
//         }
//       })
//     });

//     if (!response.ok) {
//       const errorText = await response.text();
//       console.error('Google AI API error:', response.status, errorText);
//       throw new Error(`Google AI API error: ${response.status} - ${errorText}`);
//     }

//     const data = await response.json();
//     const content = data.candidates[0]?.content?.parts[0]?.text;

//     if (!content || content.trim() === '') {
//       throw new Error('Empty response from Google AI');
//     }

//     return content;
//   } catch (error) {
//     console.error('Google AI API call failed:', error);
//     throw error;
//   }
// }

// Mock response generator removed - all responses should use real AI with admin prompts



// Generate voice using specified provider with advanced caching and warmup optimization
async function generateVoice(voiceId: string, text: string, characterId: string, language: 'ja' | 'en' = 'ja', provider: string = 'elevenlabs'): Promise<string> {
  console.log(`=== ULTRA FAST Voice Generation ===`);
  console.log(`Provider: ${provider}, Character: ${characterId}, Language: ${language}`);
  console.log(`Text length: ${text.length} characters`);

  // Check advanced cache first
  const cachedAudio = advancedVoiceCache.get(text, characterId, voiceId, language);
  if (cachedAudio) {
    console.log('=== ADVANCED CACHE HIT: Instant voice response ===');
    return cachedAudio;
  }

  // Route to appropriate voice provider
  console.log(`=== VOICE PROVIDER ROUTING ===`);
  console.log(`Selected Provider: ${provider}`);
  console.log(`Voice ID: ${voiceId}`);
  console.log(`Character: ${characterId}`);

  switch (provider) {
    case 'minimax':
      console.log(`🎵 Using MiniMax Direct for voice generation`);
      return await generateVoiceMinimax(voiceId, text, characterId, language);
    case 'elevenlabs':
    default:
      console.log(`🎵 Using ElevenLabs for voice generation`);
      return await generateVoiceElevenLabs(voiceId, text, characterId, language);
  }
}

// Generate voice using ElevenLabs (exported for warmup)
export async function generateVoiceElevenLabs(voiceId: string, text: string, characterId: string, language: 'ja' | 'en' = 'ja'): Promise<string> {
  const apiKey = await getSecret('ELEVENLABS_API_KEY');

  if (!apiKey) {
    throw new Error('ElevenLabs API key not configured');
  }

  // Use eleven_turbo_v2_5 for fastest generation
  const modelId = 'eleven_turbo_v2_5';

  console.log(`=== ElevenLabs Voice Generation ===`);
  console.log(`Language: ${language}, Model: ${modelId}, VoiceId: ${voiceId}`);
  console.log(`Text length: ${text.length} characters`);

  const startTime = Date.now();

  // Use streaming endpoint for faster initial response
  const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voiceId}/stream`, {
    method: 'POST',
    headers: {
      'Accept': 'audio/mpeg',
      'Content-Type': 'application/json',
      'xi-api-key': apiKey
    },
    body: JSON.stringify({
      text: text,
      model_id: modelId,
      voice_settings: {
        stability: 0.3,        // Lower for maximum speed
        similarity_boost: 0.3, // Lower for maximum speed
        style: 0.0,
        use_speaker_boost: false
      },
      optimize_streaming_latency: 4, // Maximum optimization
      output_format: "mp3_22050_32", // Fastest format
      apply_text_normalization: "off" // Skip text processing for speed
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error('ElevenLabs streaming API error:', response.status, errorText);

    // Fallback to regular API if streaming fails
    console.log('Falling back to regular API...');
    return generateVoiceFallback(voiceId, text, characterId, language);
  }

  // Stream the audio data as it arrives
  const audioBuffer = await response.arrayBuffer();
  const base64Audio = Buffer.from(audioBuffer).toString('base64');
  const audioDataUrl = `data:audio/mpeg;base64,${base64Audio}`;

  const generationTime = Date.now() - startTime;
  console.log(`=== ElevenLabs Voice Generation Complete ===`);
  console.log(`Streaming generation time: ${generationTime}ms`);
  console.log(`Audio size: ${audioBuffer.byteLength} bytes`);

  // Cache the result in advanced cache system
  advancedVoiceCache.set(text, characterId, voiceId, language, audioDataUrl);
  console.log(`Voice cached in advanced cache system`);

  return audioDataUrl;
}

// Generate voice using MiniMax (exported for warmup)
export async function generateVoiceMinimax(voiceId: string, text: string, characterId: string, language: 'ja' | 'en' = 'ja'): Promise<string> {
  console.log(`=== MiniMax Voice Generation ===`);
  console.log(`VoiceId: ${voiceId}, Language: ${language}`);
  console.log(`Text length: ${text.length} characters`);

  const startTime = Date.now();

  try {
    // Get character data to use correct model
    let character = null;
    try {
      const { loadCharactersFromServer } = await import('@/lib/serverFileManager');
      const serverCharacters = loadCharactersFromServer();
      character = serverCharacters[characterId];
    } catch (error) {
      console.warn('Failed to load character data for MiniMax model:', error);
    }

    // Use character's configured model or fallback
    const model = character?.minimaxModel || 'speech-01-turbo';
    console.log(`Using MiniMax model: ${model} for character: ${characterId}`);

    // Call MiniMax API directly
    const apiKey = await getSecret('MINIMAX_API_KEY');
    const groupId = await getSecret('MINIMAX_GROUP_ID');

    if (!apiKey || !groupId) {
      throw new Error('MiniMax API credentials not configured');
    }

    const apiUrl = `https://api.minimaxi.chat/v1/t2a_v2?GroupId=${groupId}`;
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        text,
        voice_setting: {
          voice_id: voiceId,
          speed: 1.0,
          vol: 1.0,
          pitch: 0,
          audio_sample_rate: 22050,
          bitrate: 128000
        },
        model,
        audio_setting: {
          sample_rate: 22050,
          bitrate: 128000,
          format: 'mp3'
        },
        stream: true
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('MiniMax API error:', response.status, errorText);
      throw new Error(`MiniMax API error: ${response.status} - ${errorText}`);
    }

    // Handle streaming response
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response body from MiniMax API');
    }

    const decoder = new TextDecoder();
    const audioChunks: Uint8Array[] = [];
    let totalLength = 0;
    let buffer = '';

    // Helper function to convert hex to bytes
    function hexToBytes(hex: string): Uint8Array {
      const bytes = new Uint8Array(hex.length / 2);
      for (let i = 0; i < hex.length; i += 2) {
        bytes[i / 2] = parseInt(hex.substring(i, i + 2), 16);
      }
      return bytes;
    }

    while (true) {
      const { done, value } = await reader.read();

      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith('data:')) {
          try {
            const jsonStr = line.substring(5).trim();
            if (jsonStr) {
              const data = JSON.parse(jsonStr);

              if (data.data && data.data.audio && !data.extra_info) {
                const hexAudio = data.data.audio;
                const audioBytes = hexToBytes(hexAudio);
                audioChunks.push(audioBytes);
                totalLength += audioBytes.length;
              }
            }
          } catch (parseError) {
            console.error('Error parsing MiniMax chunk:', parseError);
          }
        }
      }
    }

    if (audioChunks.length === 0) {
      throw new Error('No audio chunks received from MiniMax');
    }

    // Combine all chunks
    const combinedAudio = new Uint8Array(totalLength);
    let offset = 0;
    for (const chunk of audioChunks) {
      combinedAudio.set(chunk, offset);
      offset += chunk.length;
    }

    const finalBase64 = Buffer.from(combinedAudio).toString('base64');
    const audioUrl = `data:audio/mpeg;base64,${finalBase64}`;

    const generationTime = Date.now() - startTime;
    console.log(`=== MiniMax Voice Generation Complete ===`);
    console.log(`Generation time: ${generationTime}ms`);
    console.log(`Audio chunks: ${audioChunks.length}, Total size: ${totalLength} bytes`);

    // Cache the result in advanced cache system
    advancedVoiceCache.set(text, characterId, voiceId, language, audioUrl);
    console.log(`Voice cached in advanced cache system`);

    return audioUrl;

  } catch (error) {
    console.error('MiniMax voice generation failed:', error);
    throw error;
  }
}

// Fallback voice generation function
async function generateVoiceFallback(voiceId: string, text: string, _characterId: string, _language: 'ja' | 'en' = 'ja'): Promise<string> {
  const apiKey = await getSecret('ELEVENLABS_API_KEY');

  if (!apiKey) {
    throw new Error('ElevenLabs API key not configured');
  }
  const modelId = 'eleven_turbo_v2_5';

  console.log('=== Fallback Voice Generation ===');

  const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`, {
    method: 'POST',
    headers: {
      'Accept': 'audio/mpeg',
      'Content-Type': 'application/json',
      'xi-api-key': apiKey
    },
    body: JSON.stringify({
      text: text,
      model_id: modelId,
      voice_settings: {
        stability: 0.3,
        similarity_boost: 0.3,
        style: 0.0,
        use_speaker_boost: false
      },
      optimize_streaming_latency: 4,
      output_format: "mp3_22050_32"
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error('ElevenLabs fallback API error:', response.status, errorText);
    throw new Error(`ElevenLabs API error: ${response.status}`);
  }

  const audioBuffer = await response.arrayBuffer();
  const base64Audio = Buffer.from(audioBuffer).toString('base64');
  return `data:audio/mpeg;base64,${base64Audio}`;
}

// Voice performance recording (optional) - currently disabled
// async function recordVoicePerformance(characterId: string, duration: number, textLength: number) {
//   try {
//     // Firebase integration would go here
//     console.log('Voice performance:', { characterId, duration, textLength });
//   } catch (error) {
//     console.warn('Failed to record voice performance:', error);
//   }
// }

