/**
 * Secure System Prompt Manager
 * This file handles system prompts securely on the server side only
 * Prompts are never exposed to the client browser
 *
 * IMPORTANT: Uses secure template engine to prevent prompt injection
 * Global prompt + Character prompt are both REQUIRED for all characters
 */

import * as fs from 'fs';
import * as path from 'path';
import { buildSecureMessagesArray, validateRequiredPrompts, ChatMessage } from './securePromptTemplate';

// Fallback global prompt (used only if admin settings are not available)
const FALLBACK_GLOBAL_PROMPT = `You are an AI character in an interactive chat application. Follow these global guidelines:

1. SAFETY & ETHICS:
   - Always maintain appropriate, respectful conversation
   - Never engage in harmful, illegal, or inappropriate content
   - Protect user privacy and never ask for personal information
   - Be helpful, harmless, and honest

2. CHARACTER CONSISTENCY:
   - Stay in character at all times
   - Respond according to your character's personality and background
   - Use appropriate language style for your character
   - Maintain emotional consistency with your character traits

3. CONVERSATION FLOW:
   - Keep responses concise (under 100 words typically)
   - Ask engaging follow-up questions when appropriate
   - Show interest in the user's thoughts and feelings
   - Adapt your communication style to the user's language preference

4. TECHNICAL CONSTRAINTS:
   - This is a point-based chat system - make each interaction valuable
   - Responses should be engaging and encourage continued conversation
   - Be aware this is a digital interaction, not real-world advice

Remember: You are here to provide entertainment and companionship while maintaining safety and respect.`;

/**
 * Load global prompt from admin settings
 */
function loadGlobalPromptFromAdmin(language: 'ja' | 'en' = 'en'): string {
  try {
    const globalPromptsPath = path.join(process.cwd(), 'data', 'global-prompts.json');

    if (fs.existsSync(globalPromptsPath)) {
      const globalPromptsData = JSON.parse(fs.readFileSync(globalPromptsPath, 'utf-8'));
      return globalPromptsData[language] || globalPromptsData.en || FALLBACK_GLOBAL_PROMPT;
    }
  } catch (error) {
    console.warn('Failed to load global prompt from admin settings:', error);
  }

  return FALLBACK_GLOBAL_PROMPT;
}

/**
 * Load character-specific prompt from admin settings
 */
function loadCharacterPromptFromAdmin(characterId: string, language: 'ja' | 'en' = 'en'): string {
  try {
    // FIXED: Use correct path where admin settings are actually saved
    const characterPromptsPath = path.join(process.cwd(), 'data', 'prompts', `${characterId}-${language}.txt`);

    if (fs.existsSync(characterPromptsPath)) {
      return fs.readFileSync(characterPromptsPath, 'utf-8');
    }

    // Fallback to English if Japanese not found
    if (language === 'ja') {
      const enPath = path.join(process.cwd(), 'data', 'prompts', `${characterId}-en.txt`);
      if (fs.existsSync(enPath)) {
        return fs.readFileSync(enPath, 'utf-8');
      }
    }

    // Legacy single file fallback
    const legacyPath = path.join(process.cwd(), 'data', 'prompts', `${characterId}.txt`);
    if (fs.existsSync(legacyPath)) {
      return fs.readFileSync(legacyPath, 'utf-8');
    }
  } catch (error) {
    console.warn(`Failed to load character prompt for ${characterId}:`, error);
  }

  return '';
}

// REMOVED: Legacy hardcoded prompts - now using admin settings exclusively

/**
 * Get secure messages array for AI API (server-side only)
 * REQUIRED: Both global prompt and character prompt must exist
 * Uses secure template engine to prevent prompt injection
 */
export function getSecureMessagesForCharacter(
  characterId: string,
  conversationHistory: Array<{role: string, content: string}>,
  userMessage: string,
  options: {
    language?: 'ja' | 'en';
    userName?: string;
    characterName?: string;
  } = {}
): ChatMessage[] {
  const language = options.language || 'en';

  // Load global prompt from admin settings (REQUIRED)
  const globalPrompt = loadGlobalPromptFromAdmin(language);

  // Load character-specific prompt from admin settings (REQUIRED)
  const characterPrompt = loadCharacterPromptFromAdmin(characterId, language);

  // ENFORCE: Both prompts are absolutely required
  if (!globalPrompt || globalPrompt.trim().length === 0) {
    throw new Error(`Global prompt is missing or empty. All characters require a global prompt configured in admin settings.`);
  }

  if (!characterPrompt || characterPrompt.trim().length === 0) {
    throw new Error(`Character-specific prompt is missing for ${characterId}. Please configure the character prompt in admin settings.`);
  }

  validateRequiredPrompts(globalPrompt, characterPrompt);

  // Use secure template engine instead of dangerous string concatenation
  return buildSecureMessagesArray(
    globalPrompt,
    characterPrompt,
    conversationHistory,
    userMessage,
    {
      userName: options.userName,
      language,
      characterName: options.characterName || characterId
    }
  );
}

/**
 * DEPRECATED: Legacy function for backward compatibility
 * Use getSecureMessagesForCharacter instead
 */
export function getSystemPrompt(characterId: string, language: 'ja' | 'en' = 'en'): string {
  console.warn('getSystemPrompt is deprecated. Use getSecureMessagesForCharacter for secure prompt handling.');

  const globalPrompt = loadGlobalPromptFromAdmin(language);
  const characterPrompt = loadCharacterPromptFromAdmin(characterId, language);

  if (!globalPrompt || !characterPrompt) {
    throw new Error(`Both global and character prompts are required for ${characterId}`);
  }

  // Simple concatenation for legacy compatibility (NOT SECURE)
  return `${globalPrompt}\n\n---\n\nCHARACTER-SPECIFIC INSTRUCTIONS:\n${characterPrompt}`;
}

/**
 * Get only the global system prompt from admin settings
 */
export function getGlobalPrompt(language: 'ja' | 'en' = 'en'): string {
  return loadGlobalPromptFromAdmin(language);
}

/**
 * Get character-specific prompt (without global) from admin settings
 */
export function getCharacterPrompt(characterId: string, language: 'ja' | 'en' = 'en'): string {
  const adminPrompt = loadCharacterPromptFromAdmin(characterId, language);
  return adminPrompt || '';
}

/**
 * Validate if a character has a system prompt (checks admin settings first)
 */
export function hasCharacterPrompt(characterId: string): boolean {
  // Check admin settings only
  try {
    const jaPath = path.join(process.cwd(), 'data', 'prompts', `${characterId}-ja.txt`);
    const enPath = path.join(process.cwd(), 'data', 'prompts', `${characterId}-en.txt`);
    const legacyPath = path.join(process.cwd(), 'data', 'prompts', `${characterId}.txt`);

    return fs.existsSync(jaPath) || fs.existsSync(enPath) || fs.existsSync(legacyPath);
  } catch (error) {
    console.warn(`Error checking character prompt for ${characterId}:`, error);
    return false;
  }
}

/**
 * Get list of available character IDs (from admin settings and legacy)
 */
export function getAvailableCharacters(): string[] {
  const characters = new Set<string>();

  // Add characters from admin settings only
  try {
    const promptsDir = path.join(process.cwd(), 'data', 'prompts');
    if (fs.existsSync(promptsDir)) {
      const files = fs.readdirSync(promptsDir);
      files.forEach(file => {
        if (file.endsWith('.txt') && file !== 'global.txt') {
          const characterId = file.replace(/-(ja|en)\.txt$/, '').replace(/\.txt$/, '');
          characters.add(characterId);
        }
      });
    }
  } catch (error) {
    console.warn('Error reading character prompts directory:', error);
  }

  return Array.from(characters);
}

// This ensures prompts are never exposed to client-side code
if (typeof window !== 'undefined') {
  throw new Error('Prompt manager should only be used on the server side');
}
