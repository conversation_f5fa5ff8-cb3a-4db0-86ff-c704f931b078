import { NextRequest, NextResponse } from 'next/server';
import { collection, addDoc, updateDoc, doc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { createTimestamp } from '@/lib/firebaseService';
import { CharacterService } from '@/lib/characterService';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// AI response generator with system prompt integration
function generateAIResponse(message: string, characterId: string, language: string): string {
  // Use unified character service for secure messages
  try {
    const secureMessages = CharacterService.getSecureMessages(
      characterId,
      [], // No conversation history for mock
      message,
      {
        language: language as 'ja' | 'en',
        userName: 'User'
      }
    );
    console.log(`✅ Secure messages built for ${characterId} with ${secureMessages.length} messages`);
  } catch (error) {
    console.error(`Failed to generate secure response for ${characterId}:`, error);
    throw new Error(`Character ${characterId} requires both global and character prompts to be configured`);
  }

  // TODO: Integrate with actual AI service (OpenAI, <PERSON>, etc.)
  // For now, return character-appropriate responses based on system prompt
  const responses = language === 'ja' ? {
    sakura: [
      "それは本当に興味深いですね！もっと詳しく教えてください。",
      "あなたの気持ちがわかります。私はここで聞いています。",
      "それは素晴らしいですね！もっと聞きたいです。",
      "あなたはとても思いやりがありますね。シェアしてくれてありがとうございます。",
      "それは良い質問ですね！ちょっと考えさせてください..."
    ],
    alex: [
      "クールですね！そのゲームについてもっと教えてください。",
      "僕もそのゲーム好きです！一緒にプレイしませんか？",
      "面白そうですね！どんなストラテジーを使いますか？",
      "それは挑戦的ですね！頑張って！",
      "ゲームの世界は無限の可能性がありますね。"
    ],
    luna: [
      "それは魔法のように美しいですね！",
      "星々があなたに微笑んでいるようです。",
      "あなたの心の中に素晴らしい光を感じます。",
      "神秘的な力があなたを導いているようですね。",
      "この瞬間は特別な意味があるかもしれません。"
    ],
    kai: [
      "自然の力を感じますね！",
      "地球があなたに語りかけているようです。",
      "バランスが大切ですね。心と体の調和を保ちましょう。",
      "あなたの内なる平和が美しく輝いています。",
      "今この瞬間を大切にしましょう。"
    ]
  } : {
    sakura: [
      "That's really interesting! Tell me more about that.",
      "I understand how you feel. I'm here to listen.",
      "That sounds wonderful! I'd love to hear more.",
      "You're so thoughtful. I appreciate you sharing that with me.",
      "That's a great question! Let me think about that..."
    ],
    alex: [
      "Cool! Tell me more about that game.",
      "I love that game too! Want to play together sometime?",
      "Sounds interesting! What strategy do you use?",
      "That's challenging! Good luck with that!",
      "Gaming worlds have infinite possibilities, don't they?"
    ],
    luna: [
      "That's magically beautiful!",
      "The stars seem to be smiling upon you.",
      "I sense a wonderful light within your heart.",
      "It seems like mystical forces are guiding you.",
      "This moment might have special meaning."
    ],
    kai: [
      "I can feel the power of nature!",
      "The earth seems to be speaking to you.",
      "Balance is important. Let's maintain harmony between mind and body.",
      "Your inner peace shines beautifully.",
      "Let's cherish this moment."
    ]
  };

  const characterResponses = responses[characterId as keyof typeof responses] || responses.sakura;
  return characterResponses[Math.floor(Math.random() * characterResponses.length)];
}

export async function POST(request: NextRequest) {
  try {
    // Check if Firebase is initialized
    if (!db) {
      console.error('Firebase not initialized in sendMessage API');
      return NextResponse.json(
        { success: false, error: 'Firebase not initialized' },
        { status: 500 }
      );
    }

    const { message, characterId, userId, language = 'en' } = await request.json();

    console.log('SendMessage API called with:', {
      messageLength: message?.length,
      characterId,
      userId,
      language
    });

    // Validate input
    if (!message || !characterId || !userId) {
      console.error('Missing required fields:', { message: !!message, characterId: !!characterId, userId: !!userId });
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate message length
    if (message.length > 500) {
      console.error('Message too long:', message.length);
      return NextResponse.json(
        { success: false, error: 'Message too long' },
        { status: 400 }
      );
    }

    // Save user message to Firebase
    const userMessageRef = await addDoc(collection(db, 'messages'), {
      content: message,
      sender: 'user',
      userId,
      characterId,
      createdAt: createTimestamp(),
      language
    });

    // Generate AI response
    const aiResponse = generateAIResponse(message, characterId, language);

    // Save AI response to Firebase
    const aiMessageRef = await addDoc(collection(db, 'messages'), {
      content: aiResponse,
      sender: 'ai',
      userId,
      characterId,
      createdAt: createTimestamp(),
      language,
      parentMessageId: userMessageRef.id
    });

    return NextResponse.json({
      success: true,
      response: aiResponse,
      messageId: aiMessageRef.id,
      userMessageId: userMessageRef.id
    });

  } catch (error) {
    console.error('Error in sendMessage API:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
