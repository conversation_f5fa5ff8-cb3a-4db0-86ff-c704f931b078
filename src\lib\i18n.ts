import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Translation resources
const resources = {
  en: {
    translation: {
      // Navigation
      home: "Home",
      characters: "Characters",
      about: "About",
      contact: "Contact",
      signIn: "Sign In",
      
      // Home page
      title: "AiLuvChat",
      subtitle: "Experience the future of AI companionship. Chat with your perfect virtual partner.",
      chooseCompanion: "Choose Your AI Companion",
      startChat: "Start Chat",
      
      // Character tags
      cheerful: "Cheerful",
      gamer: "Gamer",
      animeLover: "Anime Lover",
      sophisticated: "Sophisticated",
      artistic: "Artistic",
      intellectual: "Intellectual",
      mysterious: "Mysterious",
      musical: "Musical",
      enchanting: "Enchanting",
      adventurous: "Adventurous",
      athletic: "Athletic",
      outdoorsy: "Outdoorsy",
      
      // Chat interface
      online: "Online",
      points: "Points",
      typeMessage: "Type your message...",
      listening: "🎤 Listening... Speak now",
      outOfPoints: "You're out of points!",
      buyPoints: "Buy Points",
      characterNotFound: "Character not found",
      goBackHome: "Go Back Home",
      messageTooLong: "Message must be 50 characters or less!",
      needPointsToSend: "You need points to send messages...",
      
      // Character descriptions
      sakuraDesc: "A cheerful and energetic AI companion who loves anime and gaming.",
      alexDesc: "A sophisticated AI partner with a passion for literature and art.",
      lunaDesc: "A mysterious and enchanting AI companion with a love for music.",
      kaiDesc: "An adventurous AI partner who enjoys outdoor activities and sports.",
      
      // AI responses
      aiResponse1: "That's really interesting! Tell me more about that.",
      aiResponse2: "I understand how you feel. I'm here to listen.",
      aiResponse3: "That sounds wonderful! I'd love to hear more.",
      aiResponse4: "You're so thoughtful. I appreciate you sharing that with me.",
      aiResponse5: "That's a great question! Let me think about that...",
      
      // Loading
      loading: "Loading",
      loadingCharacter: "Loading {{name}}...",
    }
  },
  ja: {
    translation: {
      // Navigation
      home: "ホーム",
      characters: "キャラクター",
      about: "について",
      contact: "お問い合わせ",
      signIn: "サインイン",
      
      // Home page
      title: "AiLuvChat",
      subtitle: "AI コンパニオンシップの未来を体験してください。完璧なバーチャルパートナーとチャットしましょう。",
      chooseCompanion: "AIコンパニオンを選択",
      startChat: "チャット開始",
      
      // Character tags
      cheerful: "明るい",
      gamer: "ゲーマー",
      animeLover: "アニメ好き",
      sophisticated: "洗練された",
      artistic: "芸術的",
      intellectual: "知的",
      mysterious: "神秘的",
      musical: "音楽的",
      enchanting: "魅惑的",
      adventurous: "冒険好き",
      athletic: "運動好き",
      outdoorsy: "アウトドア好き",
      
      // Chat interface
      online: "オンライン",
      points: "ポイント",
      typeMessage: "メッセージを入力...",
      listening: "🎤 聞いています... 今話してください",
      outOfPoints: "ポイントが足りません！",
      buyPoints: "ポイント購入",
      characterNotFound: "キャラクターが見つかりません",
      goBackHome: "ホームに戻る",
      messageTooLong: "メッセージは50文字以下にしてください！",
      needPointsToSend: "メッセージを送信するにはポイントが必要です...",
      
      // Character descriptions
      sakuraDesc: "アニメとゲームが大好きな明るく元気なAIコンパニオンです。",
      alexDesc: "文学と芸術に情熱を持つ洗練されたAIパートナーです。",
      lunaDesc: "音楽を愛する神秘的で魅惑的なAIコンパニオンです。",
      kaiDesc: "アウトドア活動とスポーツを楽しむ冒険好きなAIパートナーです。",
      
      // AI responses
      aiResponse1: "それは本当に興味深いですね！もっと詳しく教えてください。",
      aiResponse2: "あなたの気持ちがわかります。私はここで聞いています。",
      aiResponse3: "それは素晴らしいですね！もっと聞きたいです。",
      aiResponse4: "あなたはとても思いやりがありますね。シェアしてくれてありがとうございます。",
      aiResponse5: "それは良い質問ですね！ちょっと考えさせてください...",
      
      // Loading
      loading: "読み込み中",
      loadingCharacter: "{{name}}を読み込み中...",
    }
  }
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',
    
    interpolation: {
      escapeValue: false,
    },
    
    detection: {
      order: ['path', 'localStorage', 'navigator'],
      caches: ['localStorage'],
    }
  });

export default i18n;
