import { NextRequest, NextResponse } from 'next/server';
import { getCharacterData, getAllActiveCharacters } from '@/lib/characterManager';

// GET: Get specific character by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { characterId: string } }
) {
  try {
    const { characterId } = params;

    if (!characterId) {
      return NextResponse.json(
        { success: false, error: 'Character ID is required' },
        { status: 400 }
      );
    }

    console.log('Loading character:', characterId);

    // Debug: Check available characters
    const allCharacters = getAllActiveCharacters();
    console.log('Available characters:', Object.keys(allCharacters));
    console.log('Total characters:', Object.keys(allCharacters).length);

    // Get character data from character manager
    const character = getCharacterData(characterId);

    if (!character) {
      console.log('Character not found:', characterId);
      console.log('Available character IDs:', Object.keys(allCharacters));
      return NextResponse.json(
        { success: false, error: `Character '${characterId}' not found. Available characters: ${Object.keys(allCharacters).join(', ')}` },
        { status: 404 }
      );
    }

    console.log('Character loaded successfully:', character.name);

    return NextResponse.json({
      success: true,
      character
    });

  } catch (error) {
    console.error('Error loading character:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
