import { NextRequest, NextResponse } from 'next/server';
import { CharacterService } from '@/lib/characterService';

// GET: Get specific character by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { characterId: string } }
) {
  try {
    const { characterId } = params;

    if (!characterId) {
      return NextResponse.json(
        { success: false, error: 'Character ID is required' },
        { status: 400 }
      );
    }

    console.log('Loading character:', characterId);

    // Debug: Check available characters
    const allCharacters = CharacterService.getAllActiveCharacters();
    console.log('Available characters:', allCharacters.map(c => c.id));
    console.log('Total characters:', allCharacters.length);

    // Get character data from unified service
    const character = CharacterService.getCharacter(characterId);

    if (!character) {
      console.log('Character not found:', characterId);
      console.log('Available character IDs:', allCharacters.map(c => c.id));
      return NextResponse.json(
        { success: false, error: `Character '${characterId}' not found. Available characters: ${allCharacters.map(c => c.id).join(', ')}` },
        { status: 404 }
      );
    }

    console.log('Character loaded successfully:', character.name);

    return NextResponse.json({
      success: true,
      character
    });

  } catch (error) {
    console.error('Error loading character:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
