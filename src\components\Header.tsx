"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { useI18n } from "@/contexts/I18nProvider";
import AuthModal from "./AuthModal";

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [mounted, setMounted] = useState(false);
  const router = useRouter();
  const { user, userData, logout } = useAuth();
  const { t, language, changeLanguage } = useI18n();

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleSignOut = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const handleLanguageChange = async (newLanguage: 'en' | 'ja') => {
    if (!mounted) return;

    try {
      await changeLanguage(newLanguage);

      // Redirect to appropriate language URL
      if (typeof window !== 'undefined') {
        const currentPath = window.location.pathname;
        let newPath = currentPath;

        // Special handling for Japanese-only pages
        const japaneseOnlyPages = ['/tokusho'];
        const isJapaneseOnlyPage = japaneseOnlyPages.some(page =>
          currentPath === page || currentPath === `/ja${page}`
        );

        if (isJapaneseOnlyPage) {
          if (newLanguage === 'ja') {
            // Stay on the same page for Japanese
            newPath = currentPath.startsWith('/ja') ? currentPath : currentPath;
          } else {
            // Redirect to home for English
            newPath = '/';
          }
        } else {
          // Normal language switching logic for other pages
          if (newLanguage === 'ja') {
            if (!currentPath.startsWith('/ja')) {
              newPath = `/ja${currentPath === '/' ? '' : currentPath}`;
            }
          } else {
            if (currentPath.startsWith('/ja')) {
              newPath = currentPath.replace('/ja', '') || '/';
            }
          }
        }

        if (newPath !== currentPath) {
          router.push(newPath);
        }
      }
    } catch (error) {
      console.error('Language change error:', error);
    }
  };

  // Show loading state during SSR
  if (!mounted) {
    return (
      <header className="bg-white shadow-md">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center py-4">
            <Link href="/" className="text-2xl font-bold text-primary">
              AiLuvChat
            </Link>
            <div className="hidden md:flex items-center space-x-6">
              <span className="text-gray-600">Home</span>
            </div>
            <div className="hidden md:flex items-center space-x-4">
              <select className="border border-gray-300 rounded-md px-3 py-1 text-sm">
                <option>English</option>
              </select>
              <button className="bg-primary text-white px-4 py-2 rounded-md">
                Sign In
              </button>
            </div>
          </div>
        </div>
      </header>
    );
  }

  return (
    <header className="bg-white shadow-md">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <Link href={language === 'ja' ? '/ja' : '/'} className="text-2xl font-bold text-primary">
            AiLuvChat
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <Link href={language === 'ja' ? '/ja' : '/'} className="text-gray-600 hover:text-primary transition-colors">
              {t('home')}
            </Link>
          </nav>

          {/* Language Selector & Auth */}
          <div className="hidden md:flex items-center space-x-4">
            <select
              value={language}
              onChange={(e) => handleLanguageChange(e.target.value as 'en' | 'ja')}
              className="border border-gray-300 rounded-md px-3 py-1 text-sm"
            >
              <option value="en">English</option>
              <option value="ja">日本語</option>
            </select>

            {user ? (
              <div className="flex items-center space-x-4">
                {/* Points Display */}
                {userData && (
                  <div className="text-sm">
                    <span className="text-gray-600">{t('points')}: </span>
                    <span className="font-semibold text-primary">{userData.points}</span>
                  </div>
                )}

                {/* User Menu */}
                <div className="relative group">
                  <button className="flex items-center space-x-2 text-gray-600 hover:text-primary transition-colors">
                    <span>{user.displayName || 'User'}</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  {/* Dropdown Menu */}
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                    <div className="py-1">
                      <Link href="/profile" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        {language === 'ja' ? 'マイページ' : 'My Profile'}
                      </Link>
                      <Link href="/history" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        {language === 'ja' ? 'チャット履歴' : 'Chat History'}
                      </Link>
                      <Link href="/points" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        {language === 'ja' ? 'ポイント履歴' : 'Point History'}
                      </Link>
                      <hr className="my-1" />
                      <button
                        onClick={handleSignOut}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        {language === 'ja' ? 'サインアウト' : 'Sign Out'}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <button
                onClick={() => setShowAuthModal(true)}
                className="bg-primary text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors"
              >
                {t('signIn')}
              </button>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t">
            <nav className="flex flex-col space-y-4">
              <Link href={language === 'ja' ? '/ja' : '/'} className="text-gray-600 hover:text-primary transition-colors">
                {t('home')}
              </Link>
              <div className="flex flex-col space-y-4 pt-4">
                <select
                  value={language}
                  onChange={(e) => handleLanguageChange(e.target.value as 'en' | 'ja')}
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm"
                >
                  <option value="en">English</option>
                  <option value="ja">日本語</option>
                </select>

                {user ? (
                  <div className="space-y-2">
                    {userData && (
                      <div className="text-sm">
                        <span className="text-gray-600">{t('points')}: </span>
                        <span className="font-semibold text-primary">{userData.points}</span>
                      </div>
                    )}
                    <div className="text-sm text-gray-600">
                      {language === 'ja' ? 'ようこそ' : 'Welcome'}, {user.displayName || 'User'}
                    </div>
                    <Link
                      href="/profile"
                      className="block bg-primary text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors text-center"
                    >
                      {language === 'ja' ? 'マイページ' : 'My Profile'}
                    </Link>
                    <button
                      onClick={handleSignOut}
                      className="bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600 transition-colors w-full"
                    >
                      {language === 'ja' ? 'サインアウト' : 'Sign Out'}
                    </button>
                  </div>
                ) : (
                  <button
                    onClick={() => setShowAuthModal(true)}
                    className="bg-primary text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors"
                  >
                    {t('signIn')}
                  </button>
                )}
              </div>
            </nav>
          </div>
        )}
      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
    </header>
  );
}
