#!/usr/bin/env node

/**
 * Production Data Sync Script
 * 
 * This script copies local character data and media files to the production build.
 * Run this before deploying to ensure all locally created characters are included.
 * 
 * Usage:
 *   node scripts/sync-production-data.js
 */

const fs = require('fs');
const path = require('path');

const DATA_DIR = path.join(__dirname, '..', 'data');
const PUBLIC_CHARACTERS_DIR = path.join(__dirname, '..', 'public', 'characters');
const PRODUCTION_DATA_DIR = path.join(__dirname, '..', 'production-data');
const PRODUCTION_PUBLIC_DIR = path.join(__dirname, '..', 'production-public', 'characters');

function ensureDirectoryExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`Created directory: ${dir}`);
  }
}

function copyFileSync(src, dest) {
  try {
    const destDir = path.dirname(dest);
    ensureDirectoryExists(destDir);
    fs.copyFileSync(src, dest);
    console.log(`Copied: ${src} -> ${dest}`);
  } catch (error) {
    console.error(`Failed to copy ${src} to ${dest}:`, error.message);
  }
}

function copyDirectorySync(src, dest) {
  if (!fs.existsSync(src)) {
    console.log(`Source directory does not exist: ${src}`);
    return;
  }

  ensureDirectoryExists(dest);

  const items = fs.readdirSync(src);
  for (const item of items) {
    const srcPath = path.join(src, item);
    const destPath = path.join(dest, item);

    const stat = fs.statSync(srcPath);
    if (stat.isDirectory()) {
      copyDirectorySync(srcPath, destPath);
    } else {
      copyFileSync(srcPath, destPath);
    }
  }
}

function syncProductionData() {
  console.log('🚀 Starting production data sync...\n');

  // 1. Copy character data files
  console.log('📁 Syncing character data files...');
  if (fs.existsSync(DATA_DIR)) {
    copyDirectorySync(DATA_DIR, PRODUCTION_DATA_DIR);
  } else {
    console.log('No local data directory found. Creating empty production data structure...');
    ensureDirectoryExists(path.join(PRODUCTION_DATA_DIR, 'prompts'));
    
    // Create empty characters.json
    const emptyCharacters = {};
    fs.writeFileSync(
      path.join(PRODUCTION_DATA_DIR, 'characters.json'),
      JSON.stringify(emptyCharacters, null, 2)
    );
    
    // Create default global prompt
    const defaultGlobalPrompt = `You are an AI character in a chat application. You should:

1. Stay in character at all times
2. Respond naturally and conversationally
3. Be helpful and engaging
4. Keep responses concise but meaningful
5. Show personality through your responses

Remember to use the character-specific information provided to shape your responses.`;
    
    fs.writeFileSync(
      path.join(PRODUCTION_DATA_DIR, 'prompts', 'global.txt'),
      defaultGlobalPrompt
    );
  }

  // 2. Copy character media files
  console.log('\n🎬 Syncing character media files...');
  if (fs.existsSync(PUBLIC_CHARACTERS_DIR)) {
    copyDirectorySync(PUBLIC_CHARACTERS_DIR, PRODUCTION_PUBLIC_DIR);
  } else {
    console.log('No local character media directory found.');
    ensureDirectoryExists(PRODUCTION_PUBLIC_DIR);
  }

  // 3. Generate deployment instructions
  console.log('\n📋 Generating deployment instructions...');
  const instructions = `# Production Deployment Instructions

## Character Data Sync Complete

### Files Synced:
- Character data: ${PRODUCTION_DATA_DIR}
- Media files: ${PRODUCTION_PUBLIC_DIR}

### Deployment Steps:

1. **Copy data files to production server:**
   \`\`\`bash
   # Copy character data
   cp -r production-data/* /path/to/production/data/
   
   # Copy media files
   cp -r production-public/characters/* /path/to/production/public/characters/
   \`\`\`

2. **Set proper permissions:**
   \`\`\`bash
   chmod -R 644 /path/to/production/data/
   chmod -R 644 /path/to/production/public/characters/
   \`\`\`

3. **Environment variables:**
   Ensure all required API keys are set in production:
   - OPENROUTER_API_KEY
   - VENICE_API_KEY
   - ELEVENLABS_API_KEY
   - NEXT_PUBLIC_ADMIN_TOKEN

4. **Restart the application:**
   \`\`\`bash
   pm2 restart your-app
   # or
   systemctl restart your-app
   \`\`\`

### Verification:
- Check that characters appear in the admin panel
- Test chat functionality with created characters
- Verify voice synthesis works if ElevenLabs is configured

Generated on: ${new Date().toISOString()}
`;

  fs.writeFileSync(
    path.join(__dirname, '..', 'DEPLOYMENT_INSTRUCTIONS.md'),
    instructions
  );

  console.log('\n✅ Production data sync complete!');
  console.log('\n📖 Next steps:');
  console.log('1. Review the generated DEPLOYMENT_INSTRUCTIONS.md file');
  console.log('2. Copy the production-data and production-public directories to your server');
  console.log('3. Follow the deployment instructions');
  console.log('\n🎉 Your locally created characters will be available in production!');
}

// Run the sync
if (require.main === module) {
  syncProductionData();
}

module.exports = { syncProductionData };
