import fs from 'fs';
import path from 'path';
import { FullCharacterData } from '@/types/firebase';

// Server-side file paths
const DATA_DIR = path.join(process.cwd(), 'data');
const CHARACTERS_FILE = path.join(DATA_DIR, 'characters.json');
const PROMPTS_DIR = path.join(DATA_DIR, 'prompts');
const MEDIA_DIR = path.join(process.cwd(), 'public', 'characters');

// Ensure directories exist
export function ensureDirectoriesExist() {
  if (!fs.existsSync(DATA_DIR)) {
    fs.mkdirSync(DATA_DIR, { recursive: true });
  }
  if (!fs.existsSync(PROMPTS_DIR)) {
    fs.mkdirSync(PROMPTS_DIR, { recursive: true });
  }
  if (!fs.existsSync(MEDIA_DIR)) {
    fs.mkdirSync(MEDIA_DIR, { recursive: true });
  }
}

// Character data management
export function loadCharactersFromServer(): Record<string, FullCharacterData> {
  try {
    ensureDirectoriesExist();

    if (!fs.existsSync(CHARACTERS_FILE)) {
      // Create empty characters file if it doesn't exist
      const emptyData = {};
      fs.writeFileSync(CHARACTERS_FILE, JSON.stringify(emptyData, null, 2));
      return emptyData;
    }

    const data = fs.readFileSync(CHARACTERS_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error loading characters from server:', error);
    return {};
  }
}

export function saveCharactersToServer(characters: Record<string, FullCharacterData>): boolean {
  try {
    ensureDirectoriesExist();
    fs.writeFileSync(CHARACTERS_FILE, JSON.stringify(characters, null, 2));
    return true;
  } catch (error) {
    console.error('Error saving characters to server:', error);
    return false;
  }
}

export function saveCharacterToServer(character: FullCharacterData): boolean {
  try {
    const characters = loadCharactersFromServer();
    characters[character.id] = {
      ...character,
      updatedAt: new Date().toISOString()
    };
    return saveCharactersToServer(characters);
  } catch (error) {
    console.error('Error saving character to server:', error);
    return false;
  }
}

export function deleteCharacterFromServer(characterId: string): boolean {
  try {
    const characters = loadCharactersFromServer();
    if (characters[characterId]) {
      delete characters[characterId];

      // Also delete associated prompt file
      const promptFile = path.join(PROMPTS_DIR, `${characterId}.txt`);
      if (fs.existsSync(promptFile)) {
        fs.unlinkSync(promptFile);
      }

      return saveCharactersToServer(characters);
    }
    return false;
  } catch (error) {
    console.error('Error deleting character from server:', error);
    return false;
  }
}

// Prompt management - Multi-language support
export function loadPromptFromServer(characterId: string, language?: 'ja' | 'en'): string | { ja: string; en: string } {
  try {
    ensureDirectoriesExist();

    if (language) {
      // Load specific language prompt
      const promptFile = path.join(PROMPTS_DIR, `${characterId}-${language}.txt`);
      if (fs.existsSync(promptFile)) {
        return fs.readFileSync(promptFile, 'utf-8');
      }

      // Fallback to legacy single file
      const legacyFile = path.join(PROMPTS_DIR, `${characterId}.txt`);
      if (fs.existsSync(legacyFile)) {
        return fs.readFileSync(legacyFile, 'utf-8');
      }

      return '';
    } else {
      // Load both languages
      const jaFile = path.join(PROMPTS_DIR, `${characterId}-ja.txt`);
      const enFile = path.join(PROMPTS_DIR, `${characterId}-en.txt`);
      const legacyFile = path.join(PROMPTS_DIR, `${characterId}.txt`);

      let jaPrompt = '';
      let enPrompt = '';

      if (fs.existsSync(jaFile)) {
        jaPrompt = fs.readFileSync(jaFile, 'utf-8');
      }

      if (fs.existsSync(enFile)) {
        enPrompt = fs.readFileSync(enFile, 'utf-8');
      }

      // Fallback to legacy file for both languages if no language-specific files exist
      if (!jaPrompt && !enPrompt && fs.existsSync(legacyFile)) {
        const legacyPrompt = fs.readFileSync(legacyFile, 'utf-8');
        jaPrompt = legacyPrompt;
        enPrompt = legacyPrompt;
      }

      return { ja: jaPrompt, en: enPrompt };
    }
  } catch (error) {
    console.error('Error loading prompt from server:', error);
    return language ? '' : { ja: '', en: '' };
  }
}

export function savePromptToServer(characterId: string, prompt: string | { ja: string; en: string }, language?: 'ja' | 'en'): boolean {
  try {
    ensureDirectoriesExist();

    if (typeof prompt === 'string' && language) {
      // Save specific language prompt
      const promptFile = path.join(PROMPTS_DIR, `${characterId}-${language}.txt`);
      fs.writeFileSync(promptFile, prompt, 'utf-8');
      return true;
    } else if (typeof prompt === 'object') {
      // Save both languages
      const jaFile = path.join(PROMPTS_DIR, `${characterId}-ja.txt`);
      const enFile = path.join(PROMPTS_DIR, `${characterId}-en.txt`);

      if (prompt.ja) {
        fs.writeFileSync(jaFile, prompt.ja, 'utf-8');
      }

      if (prompt.en) {
        fs.writeFileSync(enFile, prompt.en, 'utf-8');
      }

      return true;
    } else {
      // Legacy: save to single file
      const promptFile = path.join(PROMPTS_DIR, `${characterId}.txt`);
      fs.writeFileSync(promptFile, prompt as string, 'utf-8');
      return true;
    }
  } catch (error) {
    console.error('Error saving prompt to server:', error);
    return false;
  }
}

export function loadGlobalPromptFromServer(): string {
  try {
    ensureDirectoriesExist();
    const globalPromptFile = path.join(PROMPTS_DIR, 'global.txt');

    if (fs.existsSync(globalPromptFile)) {
      return fs.readFileSync(globalPromptFile, 'utf-8');
    }

    // Create default global prompt if it doesn't exist
    const defaultGlobalPrompt = `You are an AI character in a chat application. You should:

1. Stay in character at all times
2. Respond naturally and conversationally
3. Be helpful and engaging
4. Keep responses concise but meaningful
5. Show personality through your responses

Remember to use the character-specific information provided to shape your responses.`;

    fs.writeFileSync(globalPromptFile, defaultGlobalPrompt, 'utf-8');
    return defaultGlobalPrompt;
  } catch (error) {
    console.error('Error loading global prompt from server:', error);
    return '';
  }
}

export function saveGlobalPromptToServer(prompt: string): boolean {
  try {
    ensureDirectoriesExist();
    const globalPromptFile = path.join(PROMPTS_DIR, 'global.txt');
    fs.writeFileSync(globalPromptFile, prompt, 'utf-8');
    return true;
  } catch (error) {
    console.error('Error saving global prompt to server:', error);
    return false;
  }
}

// Media file management
export function saveMediaFileToServer(characterId: string, fileName: string, buffer: Buffer): string {
  try {
    ensureDirectoriesExist();

    // Create character-specific directory
    const characterDir = path.join(MEDIA_DIR, characterId);
    if (!fs.existsSync(characterDir)) {
      fs.mkdirSync(characterDir, { recursive: true });
    }

    const filePath = path.join(characterDir, fileName);
    fs.writeFileSync(filePath, buffer);

    // Return public URL path
    return `/characters/${characterId}/${fileName}`;
  } catch (error) {
    console.error('Error saving media file to server:', error);
    throw error;
  }
}

export function deleteMediaFileFromServer(filePath: string): boolean {
  try {
    const fullPath = path.join(process.cwd(), 'public', filePath);
    if (fs.existsSync(fullPath)) {
      fs.unlinkSync(fullPath);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error deleting media file from server:', error);
    return false;
  }
}

// Video sets management
export function saveVideoSetToServer(characterId: string, setId: string, normalVideo: Buffer, lipSyncVideo: Buffer): { normalPath: string; lipSyncPath: string } {
  try {
    ensureDirectoriesExist();

    // Create video sets directory
    const videoSetsDir = path.join(MEDIA_DIR, 'video-sets', characterId);
    if (!fs.existsSync(videoSetsDir)) {
      fs.mkdirSync(videoSetsDir, { recursive: true });
    }

    const normalFileName = `${characterId}-${setId}-normal.mp4`;
    const lipSyncFileName = `${characterId}-${setId}-lipsync.mp4`;

    const normalPath = path.join(videoSetsDir, normalFileName);
    const lipSyncPath = path.join(videoSetsDir, lipSyncFileName);

    fs.writeFileSync(normalPath, normalVideo);
    fs.writeFileSync(lipSyncPath, lipSyncVideo);

    return {
      normalPath: `/characters/video-sets/${characterId}/${normalFileName}`,
      lipSyncPath: `/characters/video-sets/${characterId}/${lipSyncFileName}`
    };
  } catch (error) {
    console.error('Error saving video set to server:', error);
    throw error;
  }
}

// Utility functions
export function getCharacterMediaDirectory(characterId: string): string {
  return path.join(MEDIA_DIR, characterId);
}

export function listCharacterFiles(characterId: string): string[] {
  try {
    const characterDir = getCharacterMediaDirectory(characterId);
    if (fs.existsSync(characterDir)) {
      return fs.readdirSync(characterDir);
    }
    return [];
  } catch (error) {
    console.error('Error listing character files:', error);
    return [];
  }
}

export function getFileStats(filePath: string): { size: number; modified: Date } | null {
  try {
    const fullPath = path.join(process.cwd(), 'public', filePath);
    if (fs.existsSync(fullPath)) {
      const stats = fs.statSync(fullPath);
      return {
        size: stats.size,
        modified: stats.mtime
      };
    }
    return null;
  } catch (error) {
    console.error('Error getting file stats:', error);
    return null;
  }
}
