# AiLuvChat - AI Character Chat Website

An interactive, SEO-friendly, multilingual AI character chat website built with Next.js and modern web technologies.

## 🌟 Features

### Core Features
- **Multilingual Support (i18n)**: English and Japanese with easy expansion for more languages
- **User Authentication**: Google Sign-In and Email/Password via Firebase Authentication
- **Point System**: Server-side managed point system (100 initial points, 10 points per message)
- **Message Constraints**: 50-character limit with server-side validation
- **GDPR Compliance**: Privacy policy, cookie consent, data management

### AI & Chat Features
- **AI Character Chat**: Interactive chat with virtual AI companions
- **Video Background**: Character videos with idle/speaking state switching
- **Voice Input**: Web Speech API integration for voice-to-text
- **Real-time Chat**: LINE-like chat interface with message bubbles
- **Character Selection**: Grid of AI characters with preview videos

### Technical Features
- **Next.js 15**: Modern React framework with App Router
- **TypeScript**: Full type safety
- **Tailwind CSS**: Utility-first styling with custom theme
- **HLS Video Streaming**: Optimized video delivery
- **Responsive Design**: Mobile-first approach

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Firebase project (for backend services)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd AiLuvChat
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Copy the example file and configure your API keys:
   ```bash
   cp .env.local.example .env.local
   ```

   Edit `.env.local` with your API keys:
   ```env
   # Firebase Configuration
   NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

   # AI Provider API Keys (choose the ones you want to use)
   OPENROUTER_API_KEY=your_openrouter_api_key_here
   VENICE_API_KEY=your_venice_api_key_here
   OPENAI_API_KEY=your_openai_api_key_here
   ANTHROPIC_API_KEY=your_anthropic_api_key_here
   GOOGLE_AI_API_KEY=your_google_ai_api_key_here
   ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

   # Admin Configuration
   NEXT_PUBLIC_ADMIN_TOKEN=your-secure-admin-token-here-change-this
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── chat/[id]/         # Dynamic chat pages
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── ChatInput.tsx      # Chat input with voice support
│   ├── ChatInterface.tsx  # Main chat interface
│   ├── CharacterGrid.tsx  # Character selection grid
│   ├── Header.tsx         # Navigation header
│   ├── MessageBubble.tsx  # Chat message bubbles
│   └── VideoBackground.tsx # Character video player
├── lib/                   # Utility libraries
│   └── i18n.ts           # Internationalization config
├── types/                 # TypeScript type definitions
├── hooks/                 # Custom React hooks
└── utils/                 # Utility functions

public/
├── characters/            # Character images
└── videos/               # Character videos
```

## 🎨 Design System

### Colors
- **Primary**: #29ABE2 (Vibrant Blue)
- **Secondary**: #FFA500 (Orange)
- **Background**: #F0F0F0 (Light Gray)

### Typography
- **Font**: Inter (Google Fonts)
- **Responsive**: Mobile-first approach

### Components
- **Chat Bubbles**: Semi-transparent with backdrop blur
- **Video Background**: 50% top, chat overlay 50% bottom
- **Animations**: Subtle transitions and heartbeat effects

## 🔧 Configuration

### AI Provider API Keys

The application supports multiple AI providers. You only need to configure the ones you plan to use:

#### 🌟 Recommended: OpenRouter.ai
- **Why**: Access to multiple AI models through a single API
- **Sign up**: [https://openrouter.ai/](https://openrouter.ai/)
- **Get API key**: [https://openrouter.ai/keys](https://openrouter.ai/keys)
- **Popular models**:
  - `x-ai/grok-3-mini-beta` (Grok latest)
  - `deepseek/deepseek-r1-0528-qwen3-8b:free` (Free DeepSeek)
  - `anthropic/claude-3.5-sonnet` (Claude)
  - `openai/gpt-4o-mini` (GPT-4o mini)

#### Other Providers
- **Venice.ai**: Privacy-focused AI platform
- **OpenAI**: Direct access to GPT models
- **Anthropic**: Claude models
- **Google AI**: Gemini models
- **Cohere**: Command models
- **Mistral AI**: Mistral models
- **Together AI**: Open source models
- **Replicate**: Various models
- **Hugging Face**: HF Inference models

#### Voice Synthesis
- **ElevenLabs**: For character voice generation
- **Sign up**: [https://elevenlabs.io/](https://elevenlabs.io/)
- **Get API key**: Profile settings → API keys

### Character Management

The application includes a local admin interface for managing AI characters:

1. **Access**: `http://localhost:3000/admin/characters`
2. **Features**:
   - Create/edit characters with multilingual support
   - Configure AI model per character
   - Upload and crop profile images
   - Manage video sets (normal + lip-sync pairs)
   - Set ElevenLabs Voice IDs
   - System prompt configuration

### Internationalization
The app supports multiple languages through react-i18next:
- English (default): `/`
- Japanese: `/ja/`

### Video System
- **Idle Videos**: Loop continuously when character is not speaking
- **Speaking Videos**: Play during AI responses
- **HLS Streaming**: Optimized delivery with signed URLs
- **Fallback**: Graceful degradation with character images

### Point System
- **Initial Points**: 100 points on account creation
- **Cost**: 10 points per message
- **Validation**: Server-side enforcement
- **UI**: Real-time point display

## 🚀 Deployment

### Google Cloud App Hosting
1. Build the application: `npm run build`
2. Deploy to Google Cloud App Hosting
3. Configure Firebase Functions for backend
4. Set up Firebase Storage for videos
5. Configure domain and SSL

### Environment Setup
- **Development**: Local development with Firebase emulators
- **Staging**: Testing environment with real Firebase
- **Production**: Full deployment with CDN and monitoring

## 📱 Mobile Optimization

- **Responsive Design**: Tailwind CSS breakpoints
- **Touch Interactions**: Optimized for mobile devices
- **Video Performance**: Single video playback on mobile
- **Voice Input**: Web Speech API support
- **PWA Ready**: Service worker and manifest support

## 🔒 Security & Privacy

### GDPR Compliance
- **Privacy Policy**: Comprehensive data handling disclosure
- **Cookie Consent**: Granular consent management
- **Data Access**: User data download and correction
- **Right to Erasure**: Account deletion process
- **Data Portability**: JSON export functionality

### Security Measures
- **API Keys**: Secure storage in Firebase Functions
- **Input Validation**: Server-side message validation
- **Rate Limiting**: Point system prevents abuse
- **Signed URLs**: Secure video access

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- **Documentation**: Check this README and code comments
- **Issues**: Create a GitHub issue
- **Contact**: Use the contact form on the website

---

Built with ❤️ using Next.js, TypeScript, and modern web technologies.
