/**
 * AI Provider Configuration and API Clients
 * Handles different AI providers and their API configurations
 */

export interface AIProviderConfig {
  name: string;
  apiKeyEnvVar: string;
  baseUrl: string;
  headers: (apiKey: string) => Record<string, string>;
  requestFormat: 'openai' | 'anthropic' | 'google' | 'custom';
}

// AI Provider configurations
export const AI_PROVIDERS: Record<string, AIProviderConfig> = {
  'openrouter.ai': {
    name: 'OpenRouter.ai',
    apiKeyEnvVar: 'OPENROUTER_API_KEY',
    baseUrl: 'https://openrouter.ai/api/v1',
    headers: (apiKey: string) => ({
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
      'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
      'X-Title': 'AiLuvChat'
    }),
    requestFormat: 'openai'
  },
  
  'venice.ai': {
    name: 'Venice.ai',
    apiKeyEnvVar: 'VENICE_API_KEY',
    baseUrl: 'https://api.venice.ai/api/v1',
    headers: (apiKey: string) => ({
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }),
    requestFormat: 'openai'
  },
  
  'openai': {
    name: 'OpenAI',
    apiKeyEnvVar: 'OPENAI_API_KEY',
    baseUrl: 'https://api.openai.com/v1',
    headers: (apiKey: string) => ({
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }),
    requestFormat: 'openai'
  },
  
  'anthropic': {
    name: 'Anthropic',
    apiKeyEnvVar: 'ANTHROPIC_API_KEY',
    baseUrl: 'https://api.anthropic.com',
    headers: (apiKey: string) => ({
      'x-api-key': apiKey,
      'Content-Type': 'application/json',
      'anthropic-version': '2023-06-01'
    }),
    requestFormat: 'anthropic'
  },
  
  'google': {
    name: 'Google AI',
    apiKeyEnvVar: 'GOOGLE_AI_API_KEY',
    baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
    headers: (apiKey: string) => ({
      'Content-Type': 'application/json'
    }),
    requestFormat: 'google'
  },
  
  'cohere': {
    name: 'Cohere',
    apiKeyEnvVar: 'COHERE_API_KEY',
    baseUrl: 'https://api.cohere.ai/v1',
    headers: (apiKey: string) => ({
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }),
    requestFormat: 'custom'
  },
  
  'mistral': {
    name: 'Mistral AI',
    apiKeyEnvVar: 'MISTRAL_API_KEY',
    baseUrl: 'https://api.mistral.ai/v1',
    headers: (apiKey: string) => ({
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }),
    requestFormat: 'openai'
  },
  
  'together': {
    name: 'Together AI',
    apiKeyEnvVar: 'TOGETHER_API_KEY',
    baseUrl: 'https://api.together.xyz/v1',
    headers: (apiKey: string) => ({
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }),
    requestFormat: 'openai'
  },
  
  'replicate': {
    name: 'Replicate',
    apiKeyEnvVar: 'REPLICATE_API_TOKEN',
    baseUrl: 'https://api.replicate.com/v1',
    headers: (apiKey: string) => ({
      'Authorization': `Token ${apiKey}`,
      'Content-Type': 'application/json'
    }),
    requestFormat: 'custom'
  },
  
  'huggingface': {
    name: 'Hugging Face',
    apiKeyEnvVar: 'HUGGINGFACE_API_KEY',
    baseUrl: 'https://api-inference.huggingface.co',
    headers: (apiKey: string) => ({
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }),
    requestFormat: 'custom'
  }
};

/**
 * Get API key for a specific provider
 */
export function getProviderApiKey(provider: string): string | null {
  const config = AI_PROVIDERS[provider];
  if (!config) return null;
  
  return process.env[config.apiKeyEnvVar] || null;
}

/**
 * Get provider configuration
 */
export function getProviderConfig(provider: string): AIProviderConfig | null {
  return AI_PROVIDERS[provider] || null;
}

/**
 * Check if provider is configured (has API key)
 */
export function isProviderConfigured(provider: string): boolean {
  const apiKey = getProviderApiKey(provider);
  return !!apiKey;
}

/**
 * Get all configured providers
 */
export function getConfiguredProviders(): string[] {
  return Object.keys(AI_PROVIDERS).filter(provider => isProviderConfigured(provider));
}

/**
 * Format request for different providers
 */
export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ChatRequest {
  model: string;
  messages: ChatMessage[];
  temperature?: number;
  max_tokens?: number;
  stream?: boolean;
}

/**
 * Format request based on provider type
 */
export function formatProviderRequest(
  provider: string,
  model: string,
  messages: ChatMessage[],
  options: {
    temperature?: number;
    max_tokens?: number;
    stream?: boolean;
  } = {}
): any {
  const config = getProviderConfig(provider);
  if (!config) throw new Error(`Unknown provider: ${provider}`);

  const { temperature = 0.7, max_tokens = 1000, stream = false } = options;

  switch (config.requestFormat) {
    case 'openai':
      return {
        model,
        messages,
        temperature,
        max_tokens,
        stream
      };

    case 'anthropic':
      const systemMessage = messages.find(m => m.role === 'system');
      const userMessages = messages.filter(m => m.role !== 'system');
      
      return {
        model,
        system: systemMessage?.content || '',
        messages: userMessages.map(m => ({
          role: m.role === 'assistant' ? 'assistant' : 'user',
          content: m.content
        })),
        max_tokens,
        temperature,
        stream
      };

    case 'google':
      return {
        contents: messages.filter(m => m.role !== 'system').map(m => ({
          role: m.role === 'assistant' ? 'model' : 'user',
          parts: [{ text: m.content }]
        })),
        systemInstruction: {
          parts: [{ text: messages.find(m => m.role === 'system')?.content || '' }]
        },
        generationConfig: {
          temperature,
          maxOutputTokens: max_tokens
        }
      };

    case 'custom':
      // Each custom provider needs specific handling
      return {
        model,
        messages,
        temperature,
        max_tokens,
        stream
      };

    default:
      throw new Error(`Unsupported request format: ${config.requestFormat}`);
  }
}

/**
 * Get endpoint URL for provider
 */
export function getProviderEndpoint(provider: string, model?: string): string {
  const config = getProviderConfig(provider);
  if (!config) throw new Error(`Unknown provider: ${provider}`);

  switch (config.requestFormat) {
    case 'openai':
      return `${config.baseUrl}/chat/completions`;
    
    case 'anthropic':
      return `${config.baseUrl}/messages`;
    
    case 'google':
      return `${config.baseUrl}/models/${model}:generateContent`;
    
    default:
      return `${config.baseUrl}/chat/completions`;
  }
}
