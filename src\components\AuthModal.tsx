"use client";

import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useI18n } from "@/contexts/I18nProvider";

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode?: 'signin' | 'signup';
}

export default function AuthModal({ isOpen, onClose, mode = 'signin' }: AuthModalProps) {
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>(mode);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [displayName, setDisplayName] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const { signIn, signUp, signInWithGoogle } = useAuth();
  const { t, language } = useI18n();

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      if (authMode === 'signup') {
        if (!displayName.trim()) {
          setError(language === 'ja' ? '表示名を入力してください' : 'Please enter a display name');
          return;
        }
        await signUp(email, password, displayName);
      } else {
        await signIn(email, password);
      }
      onClose();
    } catch (err: any) {
      console.error('Auth error:', err);

      // Firebase error handling
      let errorMessage = '';
      switch (err.code) {
        case 'auth/user-not-found':
          errorMessage = language === 'ja' ? 'ユーザーが見つかりません' : 'User not found';
          break;
        case 'auth/wrong-password':
          errorMessage = language === 'ja' ? 'パスワードが間違っています' : 'Wrong password';
          break;
        case 'auth/email-already-in-use':
          errorMessage = language === 'ja' ? 'このメールアドレスは既に使用されています' : 'Email already in use';
          break;
        case 'auth/weak-password':
          errorMessage = language === 'ja' ? 'パスワードが弱すぎます' : 'Password is too weak';
          break;
        case 'auth/invalid-email':
          errorMessage = language === 'ja' ? '無効なメールアドレスです' : 'Invalid email address';
          break;
        default:
          errorMessage = language === 'ja' ? '認証エラーが発生しました' : 'Authentication error occurred';
      }
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    // Prevent multiple simultaneous requests
    if (loading) return;

    setLoading(true);
    setError("");

    try {
      await signInWithGoogle();
      onClose();
    } catch (err: any) {
      console.error('Google sign in error:', err);

      // Show user-friendly error messages
      if (err.message && err.message.includes('cancelled')) {
        setError(language === 'ja' ? 'サインインがキャンセルされました。もう一度お試しください。' : 'Sign in was cancelled. Please try again.');
      } else if (err.message && err.message.includes('popup-blocked')) {
        setError(language === 'ja' ? 'ポップアップがブロックされました。ポップアップを許可してもう一度お試しください。' : 'Popup was blocked. Please allow popups and try again.');
      } else {
        setError(language === 'ja' ? 'Googleサインインに失敗しました。もう一度お試しください。' : 'Google sign in failed. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const switchMode = () => {
    setAuthMode(authMode === 'signin' ? 'signup' : 'signin');
    setError("");
    setEmail("");
    setPassword("");
    setDisplayName("");
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-md mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-2xl font-semibold text-gray-800">
            {authMode === 'signin'
              ? (language === 'ja' ? 'サインイン' : 'Sign In')
              : (language === 'ja' ? '新規登録' : 'Sign Up')
            }
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Google Sign In */}
          <button
            onClick={handleGoogleSignIn}
            disabled={loading}
            className="w-full flex items-center justify-center gap-3 bg-white border border-gray-300 rounded-lg px-4 py-3 text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed mb-4"
          >
            <svg className="w-5 h-5" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            {language === 'ja' ? 'Googleでサインイン' : 'Sign in with Google'}
          </button>

          {/* Divider */}
          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">
                {language === 'ja' ? 'または' : 'or'}
              </span>
            </div>
          </div>

          {/* Email/Password Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {authMode === 'signup' && (
              <div>
                <label htmlFor="displayName" className="block text-sm font-medium text-gray-700 mb-1">
                  {language === 'ja' ? '表示名' : 'Display Name'}
                </label>
                <input
                  id="displayName"
                  type="text"
                  value={displayName}
                  onChange={(e) => setDisplayName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder={language === 'ja' ? '表示名を入力' : 'Enter display name'}
                  required
                />
              </div>
            )}

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                {language === 'ja' ? 'メールアドレス' : 'Email Address'}
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder={language === 'ja' ? 'メールアドレスを入力' : 'Enter email address'}
                required
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                {language === 'ja' ? 'パスワード' : 'Password'}
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder={language === 'ja' ? 'パスワードを入力' : 'Enter password'}
                required
                minLength={6}
                maxLength={15}
              />
              {authMode === 'signup' && (
                <p className="text-xs text-gray-500 mt-1">
                  {language === 'ja'
                    ? '6文字以上15文字以内、最低1文字以上の数字を含む'
                    : '6-15 characters, must include at least 1 number'
                  }
                </p>
              )}
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-primary text-white py-3 rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium"
            >
              {loading
                ? (language === 'ja' ? '処理中...' : 'Processing...')
                : authMode === 'signin'
                  ? (language === 'ja' ? 'サインイン' : 'Sign In')
                  : (language === 'ja' ? '新規登録' : 'Sign Up')
              }
            </button>
          </form>

          {/* Google Sign In */}
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">
                  {language === 'ja' ? 'または' : 'Or'}
                </span>
              </div>
            </div>

            <div className="mt-6">
              <button
                onClick={handleGoogleSignIn}
                disabled={loading}
                className={`w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 transition-colors ${
                  loading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                }`}
              >
                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                {loading
                  ? (language === 'ja' ? '処理中...' : 'Processing...')
                  : (language === 'ja' ? 'Googleでサインイン' : 'Sign in with Google')
                }
              </button>
            </div>
          </div>

          {/* Switch Mode */}
          <div className="mt-6 text-center">
            <p className="text-gray-600 text-sm">
              {authMode === 'signin'
                ? (language === 'ja' ? 'アカウントをお持ちでない方は' : "Don't have an account?")
                : (language === 'ja' ? '既にアカウントをお持ちの方は' : 'Already have an account?')
              }
              {' '}
              <button
                onClick={switchMode}
                className="text-primary hover:text-blue-600 font-medium"
              >
                {authMode === 'signin'
                  ? (language === 'ja' ? '新規登録' : 'Sign up')
                  : (language === 'ja' ? 'サインイン' : 'Sign in')
                }
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
