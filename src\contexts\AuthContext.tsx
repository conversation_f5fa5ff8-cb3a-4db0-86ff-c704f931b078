"use client";

console.log('🔥 AuthContext: Starting imports...');

import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  User as FirebaseUser,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  signInWithRedirect,
  GoogleAuthProvider,
  signOut,
  onAuthStateChanged,
  updateProfile,
  sendEmailVerification
} from 'firebase/auth';
import { doc, getDoc, setDoc, updateDoc, Timestamp } from 'firebase/firestore';

console.log('🔥 AuthContext: About to import Firebase...');
import { auth, db } from '@/lib/firebase';
console.log('🔥 AuthContext: Firebase imported successfully', { auth: !!auth, db: !!db });

import { User } from '@/types/firebase';

interface AuthContextType {
  user: FirebaseUser | null;
  userData: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, displayName: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signInWithGoogleRedirect: () => Promise<void>;
  logout: () => Promise<void>;
  updateUserProfile: (data: Partial<User>) => Promise<void>;
  refreshUserData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<FirebaseUser | null>(null);
  const [userData, setUserData] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const createUserDocument = async (user: FirebaseUser, displayName: string) => {
    // Cloud Functions (createUserProfile) が自動でユーザードキュメントを作成するため
    // フロントエンドでの手動作成は無効化
    console.log('User document creation handled by Cloud Functions (createUserProfile)');
    return;
  };

  const fetchUserData = async (uid: string) => {
    try {
      if (!db) {
        console.log('Firebase not initialized, skipping user data fetch');
        return;
      }

      const userDoc = await getDoc(doc(db, 'users', uid));
      if (userDoc.exists()) {
        setUserData(userDoc.data() as User);
      }
    } catch (error: any) {
      console.error('Error fetching user data:', error);
    }
  };

  const refreshUserData = async () => {
    if (user) {
      await fetchUserData(user.uid);
    }
  };

  useEffect(() => {
    if (!auth) {
      setLoading(false);
      return;
    }

    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        // Reload user to get latest emailVerified status
        try {
          await firebaseUser.reload();
          console.log('User emailVerified status:', firebaseUser.emailVerified);

          // Update user state with reloaded data
          setUser(firebaseUser);
        } catch (error) {
          console.error('Error reloading user:', error);
          setUser(firebaseUser);
        }

        await fetchUserData(firebaseUser.uid);
      } else {
        setUser(null);
        setUserData(null);
      }

      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      if (!auth) throw new Error('Firebase not initialized');
      await signInWithEmailAndPassword(auth, email, password);
    } catch (error: any) {
      console.error('Sign in error:', error);
      throw error;
    }
  };

  const signUp = async (email: string, password: string, displayName: string) => {
    try {
      if (!auth || !db) throw new Error('Firebase not initialized');

      // Validate password requirements
      if (password.length < 6 || password.length > 15) {
        throw new Error('Password must be between 6 and 15 characters');
      }

      if (!/\d/.test(password)) {
        throw new Error('Password must contain at least one number');
      }

      const result = await createUserWithEmailAndPassword(auth, email, password);

      // Update profile with display name
      await updateProfile(result.user, { displayName });

      // Send email verification
      try {
        await sendEmailVerification(result.user);
        console.log('Email verification sent successfully');
      } catch (emailError) {
        console.error('Failed to send verification email:', emailError);
        // Don't throw error here - user account is still created
      }

      // Cloud Functions (createUserProfile) が自動でユーザードキュメントを作成
      // フロントエンドでの手動作成は不要
      console.log('User created, Cloud Functions will handle profile creation');

      // 少し待ってからユーザーデータを取得（Cloud Functions実行待ち）
      setTimeout(async () => {
        await fetchUserData(result.user.uid);
      }, 3000); // 3秒に延長

    } catch (error: any) {
      console.error('Sign up error:', error);
      throw error;
    }
  };

  const signInWithGoogle = async () => {
    try {
      console.log('Google sign in attempt, auth status:', !!auth);
      if (!auth) {
        console.error('Firebase auth not initialized');
        throw new Error('Firebase not initialized');
      }

      const provider = new GoogleAuthProvider();
      provider.addScope('email');
      provider.addScope('profile');

      // Add custom parameters to avoid popup issues
      provider.setCustomParameters({
        prompt: 'select_account'
      });

      console.log('Starting Google popup...');
      console.log('Auth domain:', auth.config.authDomain);
      console.log('Project ID:', auth.app.options.projectId);

      const result = await signInWithPopup(auth, provider);
      console.log('Google sign in successful:', result.user.uid);

      // Cloud Functions (createUserProfile) が自動でユーザードキュメントを作成
      // Google認証でも手動作成は不要
    } catch (error: any) {
      console.error('Google sign in error:', error);

      // Handle specific error cases
      if (error?.code === 'auth/cancelled-popup-request') {
        throw new Error('Sign in was cancelled. Please try again.');
      } else if (error?.code === 'auth/popup-blocked') {
        throw new Error('Popup was blocked by your browser. Please allow popups and try again.');
      } else if (error?.code === 'auth/popup-closed-by-user') {
        throw new Error('Sign in was cancelled. Please try again.');
      }

      throw error;
    }
  };

  // Alternative: Sign in with redirect (for mobile or popup issues)
  const signInWithGoogleRedirect = async () => {
    if (!auth) {
      throw new Error('Firebase Auth not initialized');
    }

    try {
      const provider = new GoogleAuthProvider();
      provider.addScope('email');
      provider.addScope('profile');

      // Add custom parameters
      provider.setCustomParameters({
        prompt: 'select_account'
      });

      await signInWithRedirect(auth, provider);
    } catch (error: any) {
      console.error('Google redirect sign in error:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      if (!auth) throw new Error('Firebase not initialized');

      // Clear email verification session flags
      if (user) {
        const sessionKey = `emailVerificationShown_${user.uid}`;
        const completedKey = `emailVerificationCompleted_${user.uid}`;
        sessionStorage.removeItem(sessionKey);
        sessionStorage.removeItem(completedKey);
      }

      await signOut(auth);
    } catch (error: any) {
      console.error('Logout error:', error);
      throw error;
    }
  };

  const updateUserProfile = async (data: Partial<User>) => {
    if (!user) throw new Error('No user logged in');
    if (!db) throw new Error('Firebase not initialized');

    try {
      const userRef = doc(db, 'users', user.uid);

      // Prepare update data with proper type handling
      const updateData: any = {
        ...data,
        updatedAt: Timestamp.now(),
      };

      // Handle undefined values by removing them
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      await updateDoc(userRef, updateData);

      // Refresh user data
      await fetchUserData(user.uid);
    } catch (error: any) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    userData,
    loading,
    signIn,
    signUp,
    signInWithGoogle,
    signInWithGoogleRedirect,
    logout,
    updateUserProfile,
    refreshUserData,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
