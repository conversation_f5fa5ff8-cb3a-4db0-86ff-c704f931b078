"use client";

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useI18n } from '@/contexts/I18nProvider';
import { useRouter } from 'next/navigation';

export default function PointsPage() {
  const { user, userData } = useAuth();
  const { language } = useI18n();
  const router = useRouter();

  // Redirect if not logged in
  useEffect(() => {
    if (!user) {
      const homeUrl = language === 'ja' ? '/ja' : '/';
      router.push(homeUrl);
    }
  }, [user, router, language]);

  if (!user || !userData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">{language === 'ja' ? '読み込み中...' : 'Loading...'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold text-gray-900">
              {language === 'ja' ? 'ポイント履歴' : 'Points History'}
            </h1>
            <button
              onClick={() => router.push('/profile')}
              className="text-gray-500 hover:text-gray-700"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Current Points Card */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {language === 'ja' ? '現在のポイント' : 'Current Points'}
              </h3>
              <div className="text-center">
                <div className="text-4xl font-bold text-primary mb-2">
                  {userData.points}
                </div>
                <p className="text-sm text-gray-600">
                  {language === 'ja' ? 'ポイント' : 'Points'}
                </p>
                {userData.tier === 'free' && (
                  <p className="text-xs text-gray-500 mt-2">
                    {language === 'ja'
                      ? '無料会員：毎日最大50ポイントまで補充'
                      : 'Free tier: Daily refresh up to 50 points'
                    }
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Transaction History */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {language === 'ja' ? '取引履歴' : 'Transaction History'}
              </h3>

              <div className="text-center py-8">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <p className="mt-2 text-gray-600">
                  {language === 'ja' ? '取引履歴機能は開発中です' : 'Transaction history feature is under development'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
