/**
 * Secret Manager Cache System - SERVER SIDE ONLY
 * Caches API keys at startup to avoid repeated Secret Manager calls
 * This file should NEVER be imported in client-side code
 */

import { getSecret } from '../secretManager';

interface CachedSecrets {
  MINIMAX_API_KEY?: string;
  MINIMAX_GROUP_ID?: string;
  ELEVENLABS_API_KEY?: string;
  OPENAI_API_KEY?: string;
  ANTHROPIC_API_KEY?: string;
  GOOGLE_AI_API_KEY?: string;
  [key: string]: string | undefined;
}

class SecretCache {
  private static cache: CachedSecrets = {};
  private static initialized = false;
  private static initPromise: Promise<void> | null = null;

  /**
   * Initialize the secret cache with all required API keys
   * This should be called once at application startup
   */
  static async initialize(): Promise<void> {
    // Prevent multiple simultaneous initializations
    if (this.initPromise) {
      return this.initPromise;
    }

    if (this.initialized) {
      return;
    }

    this.initPromise = this.performInitialization();
    await this.initPromise;
  }

  private static async performInitialization(): Promise<void> {
    console.log('🔑 Initializing Secret Manager cache...');
    const startTime = Date.now();

    // List of secrets to cache
    const secretKeys = [
      'MINIMAX_API_KEY',
      'MINIMAX_GROUP_ID',
      'ELEVENLABS_API_KEY',
      'OPENAI_API_KEY',
      'ANTHROPIC_API_KEY',
      'GOOGLE_AI_API_KEY'
    ];

    try {
      // Load all secrets in parallel for faster initialization
      const secretPromises = secretKeys.map(async (key) => {
        try {
          const value = await getSecret(key);
          return { key, value };
        } catch (error) {
          console.warn(`⚠️ Failed to load secret ${key}:`, error);
          return { key, value: undefined };
        }
      });

      const results = await Promise.all(secretPromises);
      
      // Store results in cache
      for (const { key, value } of results) {
        this.cache[key] = value;
      }

      this.initialized = true;
      const duration = Date.now() - startTime;
      console.log(`✅ Secret cache initialized in ${duration}ms`);
      console.log(`🔑 Cached secrets: ${Object.keys(this.cache).filter(k => this.cache[k]).join(', ')}`);

    } catch (error) {
      console.error('❌ Failed to initialize secret cache:', error);
      throw error;
    }
  }

  /**
   * Get a cached secret value
   * @param key Secret key to retrieve
   * @returns Cached secret value or empty string if not found
   */
  static get(key: string): string {
    if (!this.initialized) {
      console.warn(`⚠️ Secret cache not initialized, attempting to get ${key}`);
      return '';
    }

    const value = this.cache[key];
    if (!value) {
      console.warn(`⚠️ Secret ${key} not found in cache`);
      return '';
    }

    return value;
  }

  /**
   * Check if a secret exists in the cache
   * @param key Secret key to check
   * @returns True if secret exists and has a value
   */
  static has(key: string): boolean {
    return this.initialized && !!this.cache[key];
  }

  /**
   * Get cache status for debugging
   */
  static getStatus(): { initialized: boolean; cachedKeys: string[] } {
    return {
      initialized: this.initialized,
      cachedKeys: Object.keys(this.cache).filter(k => this.cache[k])
    };
  }

  /**
   * Force refresh a specific secret (for development/testing)
   * @param key Secret key to refresh
   */
  static async refresh(key: string): Promise<void> {
    try {
      const value = await getSecret(key);
      this.cache[key] = value;
      console.log(`🔄 Refreshed secret: ${key}`);
    } catch (error) {
      console.error(`❌ Failed to refresh secret ${key}:`, error);
    }
  }
}

export default SecretCache;
