import {
  collection,
  doc,
  getDocs,
  getDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';
import { db, functions } from './firebase';
import { Character, Message, PointTransaction } from '@/types/firebase';
import { getMock<PERSON>haracters, get<PERSON>ock<PERSON><PERSON>cter } from './mockData';

// Public character interface for secure API
export interface PublicCharacter {
  id: string;
  name: {
    en: string;
    ja: string;
  };
  description: {
    en: string;
    ja: string;
  };
  personality: {
    en: string[];
    ja: string[];
  };
  interests: {
    en: string[];
    ja: string[];
  };
  age?: number;
  occupation?: {
    en: string;
    ja: string;
  };
  background?: {
    en: string;
    ja: string;
  };
  isPremium: boolean;
  sortOrder: number;
  media: {
    profileImage?: string;
    thumbnailImage?: string;
  };
}

// Check if we're in development mode with demo Firebase config
const isDemoMode = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID === 'demo-project';

// Character services
export const getCharacters = async (): Promise<Character[]> => {
  try {
    console.log('Attempting to fetch characters from local API...');

    // First try to fetch from local API endpoint
    const response = await fetch('/api/characters');
    if (response.ok) {
      const data = await response.json();
      if (data.success && data.characters && data.characters.length > 0) {
        console.log(`Fetched ${data.characters.length} characters from local API`);
        return data.characters;
      }
    }

    console.log('Local API failed, trying Firebase...');

    // Fallback to Firebase if local API fails
    if (!isDemoMode && db) {
      // Set a timeout for Firebase operations
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Firebase timeout')), 5000);
      });

      const fetchPromise = (async () => {
        const charactersRef = collection(db, 'characters');
        const q = query(charactersRef, where('isActive', '==', true));
        const snapshot = await getDocs(q);

        const characters = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Character[];

        console.log(`Fetched ${characters.length} characters from Firebase`);

        if (characters.length > 0) {
          return characters;
        }

        throw new Error('No characters found in Firebase');
      })();

      try {
        return await Promise.race([fetchPromise, timeoutPromise]);
      } catch (firebaseError) {
        console.error('Firebase fetch failed:', firebaseError);
      }
    }

    // Final fallback to mock data
    console.log('All sources failed, falling back to mock data');
    return getMockCharacters();

  } catch (error) {
    console.error('Error fetching characters:', error);
    // Always fallback to mock data if everything fails
    console.log('Falling back to mock data due to error');
    return getMockCharacters();
  }
};

export const getCharacter = async (characterId: string): Promise<Character | null> => {
  console.log('getCharacter: Fetching character with ID:', characterId);

  // TEMPORARY FIX: Use mock data directly to bypass JSON parse error
  console.log('getCharacter: Using mock data directly (temporary fix)');
  return getMockCharacter(characterId);
};

// UI Message type for components
export interface UIMessage {
  id: string;
  content: string;
  sender: "user" | "ai";
  timestamp: Date;
  characterId?: string;
}

// Message services
export const getChatHistory = async (
  userId: string,
  characterId: string,
  limitCount: number = 50
): Promise<UIMessage[]> => {
  try {
    if (!db) {
      console.log('Firebase not initialized, returning empty chat history');
      return [];
    }

    const messagesRef = collection(db, 'messages');
    const q = query(
      messagesRef,
      where('userId', '==', userId),
      where('characterId', '==', characterId),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(q);

    return snapshot.docs
      .map(doc => {
        const data = doc.data() as Message;
        return {
          id: doc.id,
          content: data.content,
          sender: data.sender,
          timestamp: data.createdAt.toDate(),
          characterId: data.characterId
        } as UIMessage;
      })
      .reverse(); // Reverse to get chronological order
  } catch (error) {
    console.error('Error fetching chat history:', error);
    throw error;
  }
};

// Point services
export const getPointTransactions = async (
  userId: string,
  limitCount: number = 20
): Promise<PointTransaction[]> => {
  try {
    if (!db) {
      console.log('Firebase not initialized, returning empty transactions');
      return [];
    }

    const transactionsRef = collection(db, 'pointTransactions');
    const q = query(
      transactionsRef,
      where('userId', '==', userId),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    const snapshot = await getDocs(q);

    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as PointTransaction[];
  } catch (error) {
    console.error('Error fetching point transactions:', error);
    throw error;
  }
};

// Firebase Functions calls
export const validateMessage = functions ? httpsCallable(functions, 'validateMessage') : null;
export const deductPoints = functions ? httpsCallable(functions, 'deductPoints') : null;
export const synthesizeVoice = functions ? httpsCallable(functions, 'synthesizeVoice') : null;

// Secure Firebase Functions calls
export const getSecureVideoUrlFunc = functions ? httpsCallable(functions, 'getSecureVideoUrl') : null;
export const processSecureChatFunc = functions ? httpsCallable(functions, 'processSecureChat') : null;

// クライアント側のFirebase関連サービス

// キャラクター一覧取得 - Cloud Functions経由
export const getPublicCharacters = async (): Promise<PublicCharacter[]> => {
  if (!functions) {
    console.log('Firebase not initialized, returning empty characters list');
    return [];
  }

  try {
    const getPublicCharactersFunc = httpsCallable(functions, 'getPublicCharacters');
    const result = await getPublicCharactersFunc();
    return result.data as PublicCharacter[];
  } catch (error) {
    console.error('Error fetching public characters:', error);
    return [];
  }
};

// セキュアな動画URL取得 - 署名付きURL
export const getSecureVideoUrl = async (
  characterId: string,
  videoType: 'idle' | 'speaking' | 'listening' | 'normal' | 'lipsync',
  videoSetId?: string
): Promise<string> => {
  if (!getSecureVideoUrlFunc) {
    console.log('Firebase not initialized, returning empty URL');
    return '';
  }

  try {
    const result = await getSecureVideoUrlFunc({ characterId, videoType, videoSetId });
    const data = result.data as { url: string; expiresIn: number };
    return data.url;
  } catch (error) {
    console.error('Error fetching secure video URL:', error);
    return '';
  }
};

// セキュアなチャットメッセージ送信 - Cloud Functions経由
export const sendSecureChatMessage = async (
  message: string,
  characterId: string,
  language: string = 'en'
): Promise<{ success: boolean; response: string; messageId: string }> => {
  if (!processSecureChatFunc) {
    console.log('Firebase not initialized, chat unavailable');
    return { success: false, response: 'Service unavailable', messageId: '' };
  }

  try {
    const result = await processSecureChatFunc({
      message,
      characterId,
      language,
    });

    return result.data as { success: boolean; response: string; messageId: string };
  } catch (error) {
    console.error('Error sending secure chat message:', error);
    throw error;
  }
};

// 音声生成関数 - 一貫性向上版
export const generateVoice = async (
  text: string,
  characterId: string,
  language: string = 'en'
): Promise<{ success: boolean; audioUrl: string; duration: number }> => {
  try {
    if (!functions) {
      throw new Error('Firebase Functions not initialized');
    }

    // Cloud Function経由で一貫した音声生成
    const generateVoiceFunc = httpsCallable(functions, 'generateOptimizedVoice');
    
    // パフォーマンス計測開始
    const startTime = Date.now();
    console.log(`Starting voice generation for character ${characterId}`);
    
    const result = await generateVoiceFunc({
      text,
      characterId,
      language,
    });
    
    const duration = Date.now() - startTime;
    console.log(`Voice generation completed in ${duration}ms`);
    
    return result.data as { success: boolean; audioUrl: string; duration: number };
  } catch (error) {
    console.error('Error generating voice:', error);
    throw error;
  }
};

// Legacy video URL service - deprecated, use getSecureVideoUrl instead
export const getSignedVideoUrl = async (_videoPath: string): Promise<string> => {
  console.warn('getSignedVideoUrl is deprecated, use getSecureVideoUrl instead');
  return '';
};

// Point management
export const spendPoints = async (
  amount: number,
  reason: string,
  metadata?: Record<string, any>
): Promise<{ success: boolean; message: string }> => {
  try {
    if (!deductPoints) {
      throw new Error('Firebase Functions not initialized');
    }

    const result = await deductPoints({
      amount,
      reason,
      metadata,
    });

    return result.data as { success: boolean; message: string };
  } catch (error) {
    console.error('Error spending points:', error);
    throw error;
  }
};

// Message validation
export const validateChatMessage = async (message: string): Promise<{ valid: boolean; message: string }> => {
  try {
    if (!validateMessage) {
      throw new Error('Firebase Functions not initialized');
    }

    const result = await validateMessage({ message });
    return result.data as { valid: boolean; message: string };
  } catch (error) {
    console.error('Error validating message:', error);
    throw error;
  }
};

// Utility functions
export const createTimestamp = () => Timestamp.now();

export const formatTimestamp = (timestamp: Timestamp): string => {
  return timestamp.toDate().toLocaleString();
};


