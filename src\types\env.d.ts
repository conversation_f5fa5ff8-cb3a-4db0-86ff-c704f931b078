/**
 * Environment Variables Type Definitions
 * Provides type safety for environment variables
 */

declare namespace NodeJS {
  interface ProcessEnv {
    // Firebase Configuration
    NEXT_PUBLIC_FIREBASE_API_KEY: string;
    NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: string;
    NEXT_PUBLIC_FIREBASE_PROJECT_ID: string;
    NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: string;
    NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: string;
    NEXT_PUBLIC_FIREBASE_APP_ID: string;

    // AI Provider API Keys
    OPENROUTER_API_KEY?: string;
    VENICE_API_KEY?: string;
    OPENAI_API_KEY?: string;
    ANTHROPIC_API_KEY?: string;
    GOOGLE_AI_API_KEY?: string;
    COHERE_API_KEY?: string;
    MISTRAL_API_KEY?: string;
    TOGETHER_API_KEY?: string;
    REPLICATE_API_TOKEN?: string;
    HUGGINGFACE_API_KEY?: string;

    // Voice Synthesis
    ELEVENLABS_API_KEY?: string;

    // Admin Configuration
    NEXT_PUBLIC_ADMIN_TOKEN?: string;
    ADMIN_TOKEN?: string;

    // Application Configuration
    NODE_ENV: 'development' | 'production' | 'test';
    NEXT_PUBLIC_SITE_URL?: string;
  }
}

export {};
