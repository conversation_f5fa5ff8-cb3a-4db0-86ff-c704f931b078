// Real-time streaming audio player using Media Source Extensions
export class StreamingAudioPlayer {
  private mediaSource: MediaSource | null = null;
  private sourceBuffer: SourceBuffer | null = null;
  private audio: HTMLAudioElement | null = null;
  private isInitialized = false;
  private pendingChunks: Uint8Array[] = [];

  constructor() {
    this.audio = new Audio();
    this.audio.preload = 'none';
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    return new Promise((resolve, reject) => {
      if (!('MediaSource' in window)) {
        reject(new Error('MediaSource not supported'));
        return;
      }

      this.mediaSource = new MediaSource();
      this.audio!.src = URL.createObjectURL(this.mediaSource);

      this.mediaSource.addEventListener('sourceopen', () => {
        try {
          // Use MP3 codec for both ElevenLabs and MiniMax
          this.sourceBuffer = this.mediaSource!.addSourceBuffer('audio/mpeg');

          this.sourceBuffer.addEventListener('error', (e) => {
            console.error('SourceBuffer error:', e);
          });

          this.isInitialized = true;

          // Process any pending chunks
          for (const chunk of this.pendingChunks) {
            this.appendChunk(chunk).catch(error => {
              console.error('Error processing pending chunk:', error);
            });
          }
          this.pendingChunks = [];

          resolve();
        } catch (error) {
          console.error('Failed to initialize SourceBuffer:', error);
          reject(error);
        }
      });

      this.mediaSource.addEventListener('sourceclose', () => {
        console.log('MediaSource closed');
      });

      this.mediaSource.addEventListener('sourceended', () => {
        console.log('MediaSource ended');
      });
    });
  }

  async appendAudioChunk(audioChunk: Uint8Array): Promise<void> {
    if (!this.isInitialized || !this.sourceBuffer) {
      console.warn('Player not initialized, queuing chunk');
      this.pendingChunks.push(audioChunk);
      return;
    }

    // Use the same pattern as the working sample code
    await this.appendChunk(audioChunk);
  }

  private async appendChunk(chunk: Uint8Array): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.sourceBuffer) {
        reject(new Error('SourceBuffer not available'));
        return;
      }

      if (this.sourceBuffer.updating) {
        // Buffer is updating, wait for it to finish
        this.sourceBuffer.addEventListener('updateend', () => resolve(this.appendChunk(chunk)), { once: true });
      } else {
        try {
          this.sourceBuffer.appendBuffer(chunk);
          resolve();
        } catch (error) {
          reject(error);
        }
      }
    });
  }

  play(): void {
    if (!this.audio) return;
    this.audio.play();
  }

  pause(): void {
    if (this.audio) {
      this.audio.pause();
    }
  }

  stop(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }

  async finalize(): Promise<void> {
    if (this.sourceBuffer && this.mediaSource && this.mediaSource.readyState === 'open') {
      try {
        await new Promise<void>(resolve => {
          const onUpdateEnd = () => {
            this.sourceBuffer!.removeEventListener('updateend', onUpdateEnd);
            if (this.mediaSource && this.mediaSource.readyState === 'open') {
              this.mediaSource.endOfStream();
            }
            resolve();
          };
          if (!this.sourceBuffer!.updating) {
            onUpdateEnd();
          } else {
            this.sourceBuffer!.addEventListener('updateend', onUpdateEnd);
          }
        });
      } catch (error) {
        // Silent error handling
      }
    }
  }

  destroy(): void {
    this.stop();
    
    if (this.audio) {
      if (this.audio.src) {
        URL.revokeObjectURL(this.audio.src);
      }
      this.audio = null;
    }

    if (this.mediaSource) {
      if (this.mediaSource.readyState === 'open') {
        try {
          this.mediaSource.endOfStream();
        } catch (error) {
          console.error('Error ending MediaSource:', error);
        }
      }
      this.mediaSource = null;
    }

    this.sourceBuffer = null;
    this.isInitialized = false;
    this.pendingChunks = [];
  }

  get currentTime(): number {
    return this.audio?.currentTime || 0;
  }

  get duration(): number {
    return this.audio?.duration || 0;
  }

  get isPlaying(): boolean {
    return this.audio ? !this.audio.paused : false;
  }
}

// Utility function to start streaming audio
export async function startStreamingAudio(
  text: string,
  voiceId: string,
  language: 'ja' | 'en',
  characterId: string,
  provider?: 'elevenlabs' | 'minimax',
  onAudioStart?: () => void
): Promise<StreamingAudioPlayer> {
  console.log('🎵 Starting streaming audio at', new Date().toISOString(), 'for text:', text.substring(0, 50));

  // Always create new streaming request (no cache to ensure callback works)
  return createStreamingAudio(text, voiceId, language, characterId, provider, onAudioStart);
}

async function createStreamingAudio(
  text: string,
  voiceId: string,
  language: 'ja' | 'en',
  characterId: string,
  provider?: 'elevenlabs' | 'minimax',
  onAudioStart?: () => void
): Promise<StreamingAudioPlayer> {
  const player = new StreamingAudioPlayer();
  await player.initialize();

  // Start streaming request
  const response = await fetch('/api/voice/stream', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      text,
      voiceId,
      language,
      characterId,
      provider
    })
  });

  if (!response.ok) {
    throw new Error(`Streaming request failed: ${response.status}`);
  }

  const reader = response.body?.getReader();
  if (!reader) {
    throw new Error('No response body for streaming');
  }

  const decoder = new TextDecoder();
  let buffer = '';

  // Start playing as soon as we get the first chunk
  let hasStartedPlaying = false;

  // Process streaming data
  const processStream = async (audioStartCallback?: () => void) => {
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.substring(6));
              
              if (data.type === 'audio_chunk') {
                // Decode Base64 to Uint8Array
                const base64Data = data.data;
                const binaryString = atob(base64Data);
                const audioBytes = new Uint8Array(binaryString.length);
                for (let i = 0; i < binaryString.length; i++) {
                  audioBytes[i] = binaryString.charCodeAt(i);
                }

                console.log('🎵 Received audio chunk, size:', audioBytes.length, 'at', new Date().toISOString());
                await player.appendAudioChunk(audioBytes);

                // Start playing immediately after first chunk - same as sample code
                if (!hasStartedPlaying) {
                  hasStartedPlaying = true;
                  console.log('🎵 Starting audio playback with first chunk at', new Date().toISOString());
                  try {
                    player.play();
                    console.log('🎵 Audio playback started successfully');
                    // Trigger text display callback
                    if (audioStartCallback) {
                      audioStartCallback();
                    }
                  } catch (playError) {
                    console.warn('Autoplay blocked, will start on user interaction:', playError);
                    // Store player reference for manual start
                    (window as any).pendingAudioPlayer = player;
                    // Still trigger callback even if autoplay is blocked
                    if (audioStartCallback) {
                      audioStartCallback();
                    }
                  }
                }
              } else if (data.type === 'complete') {
                await player.finalize();
                break;
              }
            } catch (parseError) {
              console.error('Error parsing streaming data:', parseError);
            }
          }
        }
      }
    } catch (error) {
      console.error('Streaming error:', error);
      player.destroy();
      throw error;
    }
  };

  // Start processing in background but ensure player is ready
  processStream(onAudioStart).catch(error => {
    console.error('Stream processing failed:', error);
    player.destroy(); // Clean up on error
  });

  // Return player immediately but it will start processing chunks as they arrive
  return player;
}
