# 実装要件：ローカル管理画面から本番環境への反映

## セキュリティ要件

### キャラクター表示制御
- Active フラグが false のキャラクターは本番サイトに表示しない
- 非アクティブキャラクターのチャット画面へのアクセスをブロック
- URL直接アクセスによる非アクティブキャラクターへのアクセス防止

### システムプロンプト保護
- グローバルシステムプロンプトをクライアント側で確認不可に
- キャラクター固有プロンプトをクライアント側で確認不可に
- ブラウザの開発者ツールからのプロンプト確認を防止
- すべてのプロンプト処理をサーバーサイドのみで実行

### 動画リソース保護
- 動画URLの秘匿化（署名付きURL、一時URL等）
- 動画ファイルへの直接アクセス防止
- リップシンク技術の模倣（実際は単純再生だが、ユーザーにはリップシンクと認識させる）
- 動画ソースコードの難読化

## 実装アプローチ

1. **キャラクター表示フィルタリング**
   - サーバーサイドでアクティブフラグによるフィルタリング
   - クライアントへは必要最小限の情報のみ送信

2. **プロンプト管理強化**
   - すべてのプロンプト処理をサーバーサイド関数に限定
   - API経由でのプロンプト取得時に認証チェック
   - 環境変数やサーバーサイド専用ストレージでプロンプト管理

3. **動画セキュリティ**
   - 署名付き一時URLによる動画配信
   - CDNやストリーミングサービス活用
   - 再生制御をサーバーサイドで管理
   - フロントエンド実装の難読化

4. **アクセス制御**
   - ミドルウェアによるルートガード実装
   - サーバーサイドでのキャラクターID検証
   - 不正アクセス試行の監視とブロック