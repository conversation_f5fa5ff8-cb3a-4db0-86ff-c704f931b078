"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateCharacterData = exports.updateCharacterPrompt = exports.setAdminClaim = exports.processSecureChat = exports.getSecureVideoUrl = exports.getPublicCharacters = exports.synthesizeVoice = exports.sendMessage = exports.validateMessage = exports.deductPoints = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
// Genkitを一時的に無効化（デプロイタイムアウト回避）
// import { configureGenkit } from '@genkit-ai/core';
// import { firebase } from '@genkit-ai/firebase';
// import { googleAI } from '@genkit-ai/googleai';
const openai_1 = require("openai");
// import { ElevenLabsAPI } from 'elevenlabs-node';
const cors_1 = __importDefault(require("cors"));
// 記憶システム関数のインポート（一時的に無効化 - デプロイタイムアウト回避）
// export { saveMemoryOnMessage } from './memory/saveMemory';
// export { chatWithMemory } from './memory/chatWithMemory';
// ユーザー管理関数のインポート（一時的に無効化 - デプロイタイムアウト回避）
// export { createUserProfile, cleanupUserData, updateUserProfile, updateUserSubscription } from './users/onUserCreate';
// Initialize Firebase Admin
admin.initializeApp();
// Cold start prevention configuration
const runtimeOpts = {
    timeoutSeconds: 300,
    memory: '1GB',
    minInstances: 1, // Keep at least 1 instance warm
    maxInstances: 10
};
// Secret Manager helper function
async function getSecret(secretName) {
    try {
        const secretManagerServiceAccount = admin.app().options.credential;
        if (!secretManagerServiceAccount) {
            console.error('No service account found for Secret Manager access');
            return null;
        }
        // Use Firebase Admin SDK to access secrets
        const projectId = admin.app().options.projectId;
        if (!projectId) {
            console.error('No project ID found');
            return null;
        }
        // For now, use environment variables as fallback
        // In production, this will be handled by App Hosting automatically
        return process.env[secretName] || null;
    }
    catch (error) {
        console.error(`Error accessing secret ${secretName}:`, error);
        return null;
    }
}
// Genkitの初期化を一時的に無効化（デプロイタイムアウト回避）
// configureGenkit({
//   plugins: [
//     firebase(),
//     googleAI({
//       apiKey: process.env.GOOGLE_AI_API_KEY || functions.config().gemini?.api_key,
//     }),
//   ],
//   logLevel: 'debug',
//   enableTracingAndMetrics: true,
// });
// Initialize AI services - will be initialized lazily
let openaiClient = null;
async function getOpenAIClient() {
    var _a;
    if (!openaiClient) {
        const apiKey = await getSecret('OPENAI_API_KEY') || ((_a = functions.config().openai) === null || _a === void 0 ? void 0 : _a.api_key);
        if (apiKey) {
            openaiClient = new openai_1.OpenAI({ apiKey });
        }
    }
    return openaiClient;
}
// CORS configuration
const corsHandler = (0, cors_1.default)({ origin: true });
// 既存のcreateUser関数は新しいcreateUserProfileに置き換えられました
// Point management
exports.deductPoints = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const { amount, reason, metadata } = data;
    const userId = context.auth.uid;
    try {
        const userRef = admin.firestore().collection('users').doc(userId);
        await admin.firestore().runTransaction(async (transaction) => {
            const userDoc = await transaction.get(userRef);
            if (!userDoc.exists) {
                throw new functions.https.HttpsError('not-found', 'User not found');
            }
            const userData = userDoc.data();
            const currentPoints = (userData === null || userData === void 0 ? void 0 : userData.points) || 0;
            if (currentPoints < amount) {
                throw new functions.https.HttpsError('failed-precondition', 'Insufficient points');
            }
            // Update user points
            transaction.update(userRef, {
                points: currentPoints - amount,
                updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            });
            // Create transaction record
            transaction.create(admin.firestore().collection('pointTransactions').doc(), {
                userId,
                type: 'spent',
                amount: -amount,
                reason,
                metadata: metadata || {},
                createdAt: admin.firestore.FieldValue.serverTimestamp(),
            });
        });
        return { success: true, message: 'Points deducted successfully' };
    }
    catch (error) {
        console.error('Error deducting points:', error);
        throw error;
    }
});
// Message validation
exports.validateMessage = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const { message } = data;
    // Validate message length (50 characters max)
    if (!message || typeof message !== 'string') {
        throw new functions.https.HttpsError('invalid-argument', 'Message must be a string');
    }
    if (message.length > 50) {
        throw new functions.https.HttpsError('invalid-argument', 'Message must be 50 characters or less');
    }
    if (message.trim().length === 0) {
        throw new functions.https.HttpsError('invalid-argument', 'Message cannot be empty');
    }
    return { valid: true, message: 'Message is valid' };
});
// AI chat function
exports.sendMessage = functions.https.onRequest(async (req, res) => {
    return corsHandler(req, res, async () => {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        try {
            const { message, characterId, userId, language = 'en' } = req.body;
            // Validate input
            if (!message || !characterId || !userId) {
                return res.status(400).json({ error: 'Missing required fields' });
            }
            // Get character configuration
            const characterDoc = await admin.firestore()
                .collection('characters')
                .doc(characterId)
                .get();
            if (!characterDoc.exists) {
                return res.status(404).json({ error: 'Character not found' });
            }
            const character = characterDoc.data();
            const systemPrompt = ((_a = character === null || character === void 0 ? void 0 : character.systemPrompt) === null || _a === void 0 ? void 0 : _a[language]) || ((_b = character === null || character === void 0 ? void 0 : character.systemPrompt) === null || _b === void 0 ? void 0 : _b.en);
            // Get recent conversation history
            const recentMessages = await admin.firestore()
                .collection('messages')
                .where('userId', '==', userId)
                .where('characterId', '==', characterId)
                .orderBy('createdAt', 'desc')
                .limit(10)
                .get();
            const conversationHistory = recentMessages.docs
                .reverse()
                .map(doc => {
                const data = doc.data();
                return `${data.sender}: ${data.content}`;
            })
                .join('\n');
            // Generate AI response using OpenAI
            const openaiClient = await getOpenAIClient();
            if (!openaiClient) {
                throw new Error('OpenAI API key not configured');
            }
            const completion = await openaiClient.chat.completions.create({
                model: ((_c = character === null || character === void 0 ? void 0 : character.aiConfig) === null || _c === void 0 ? void 0 : _c.model) || 'gpt-3.5-turbo',
                messages: [
                    { role: 'system', content: systemPrompt },
                    { role: 'user', content: `Previous conversation:\n${conversationHistory}\n\nUser: ${message}` },
                ],
                max_tokens: ((_d = character === null || character === void 0 ? void 0 : character.aiConfig) === null || _d === void 0 ? void 0 : _d.maxTokens) || 150,
                temperature: ((_e = character === null || character === void 0 ? void 0 : character.aiConfig) === null || _e === void 0 ? void 0 : _e.temperature) || 0.7,
            });
            const aiResponse = ((_g = (_f = completion.choices[0]) === null || _f === void 0 ? void 0 : _f.message) === null || _g === void 0 ? void 0 : _g.content) || 'Sorry, I couldn\'t process that.';
            // Save messages to Firestore
            const batch = admin.firestore().batch();
            // User message
            const userMessageRef = admin.firestore().collection('messages').doc();
            batch.set(userMessageRef, {
                chatId: `${userId}_${characterId}`,
                userId,
                characterId,
                content: message,
                sender: 'user',
                type: 'text',
                createdAt: admin.firestore.FieldValue.serverTimestamp(),
            });
            // AI message
            const aiMessageRef = admin.firestore().collection('messages').doc();
            batch.set(aiMessageRef, {
                chatId: `${userId}_${characterId}`,
                userId,
                characterId,
                content: aiResponse,
                sender: 'ai',
                type: 'text',
                metadata: {
                    model: ((_h = character === null || character === void 0 ? void 0 : character.aiConfig) === null || _h === void 0 ? void 0 : _h.model) || 'gpt-3.5-turbo',
                    tokens: ((_j = completion.usage) === null || _j === void 0 ? void 0 : _j.total_tokens) || 0,
                },
                createdAt: admin.firestore.FieldValue.serverTimestamp(),
            });
            await batch.commit();
            return res.json({
                success: true,
                response: aiResponse,
                messageId: aiMessageRef.id,
            });
        }
        catch (error) {
            console.error('Error in sendMessage:', error);
            return res.status(500).json({ error: 'Internal server error' });
        }
    });
});
// Voice synthesis function with cold start prevention
exports.synthesizeVoice = functions
    .runWith(runtimeOpts)
    .https.onCall(async (data, context) => {
    var _a;
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const { text, characterId } = data;
    try {
        // Get character voice configuration
        const characterDoc = await admin.firestore()
            .collection('characters')
            .doc(characterId)
            .get();
        if (!characterDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Character not found');
        }
        const character = characterDoc.data();
        const voiceConfig = character === null || character === void 0 ? void 0 : character.voiceConfig;
        if (!voiceConfig) {
            throw new functions.https.HttpsError('failed-precondition', 'Voice configuration not found');
        }
        // Generate voice using ElevenLabs API directly
        const elevenLabsApiKey = await getSecret('ELEVENLABS_API_KEY') || ((_a = functions.config().elevenlabs) === null || _a === void 0 ? void 0 : _a.api_key);
        if (!elevenLabsApiKey) {
            throw new functions.https.HttpsError('failed-precondition', 'ElevenLabs API key not configured');
        }
        const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voiceConfig.voiceId}`, {
            method: 'POST',
            headers: {
                'Accept': 'audio/mpeg',
                'Content-Type': 'application/json',
                'xi-api-key': elevenLabsApiKey
            },
            body: JSON.stringify({
                text: text,
                model_id: 'eleven_multilingual_v2',
                voice_settings: voiceConfig.settings || {
                    stability: 0.5,
                    similarity_boost: 0.5,
                },
            })
        });
        if (!response.ok) {
            throw new functions.https.HttpsError('internal', `ElevenLabs API error: ${response.status}`);
        }
        const audioBuffer = await response.arrayBuffer();
        const buffer = Buffer.from(audioBuffer);
        // Upload to Firebase Storage
        const bucket = admin.storage().bucket();
        const fileName = `voice/${characterId}/${Date.now()}.mp3`;
        const file = bucket.file(fileName);
        await file.save(buffer, {
            metadata: {
                contentType: 'audio/mpeg',
            },
        });
        // Generate signed URL
        const [signedUrl] = await file.getSignedUrl({
            action: 'read',
            expires: Date.now() + 60 * 60 * 1000, // 1 hour
        });
        return {
            success: true,
            audioUrl: signedUrl,
            duration: buffer.length, // Approximate
        };
    }
    catch (error) {
        console.error('Error synthesizing voice:', error);
        throw new functions.https.HttpsError('internal', 'Failed to synthesize voice');
    }
});
// Cloud Functions for Firebase - キャラクター・動画管理
// キャラクター取得関数 - アクティブなキャラクターのみ返す（セキュア版）
exports.getPublicCharacters = functions.https.onCall(async (_data, context) => {
    // 認証チェック
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    try {
        const charactersRef = admin.firestore().collection('characters');
        const snapshot = await charactersRef.where('isActive', '==', true).get();
        // 公開用データのみを返す（システムプロンプトなど機密情報は除外）
        const publicCharacters = snapshot.docs.map(doc => {
            var _a, _b;
            const data = doc.data();
            return {
                id: doc.id,
                name: data.name,
                description: data.description,
                personality: data.personality,
                interests: data.interests,
                age: data.age,
                occupation: data.occupation,
                background: data.background,
                isPremium: data.isPremium,
                sortOrder: data.sortOrder,
                media: {
                    profileImage: (_a = data.media) === null || _a === void 0 ? void 0 : _a.profileImage,
                    thumbnailImage: (_b = data.media) === null || _b === void 0 ? void 0 : _b.thumbnailImage,
                    // 動画URLは含めない（署名付きURLで別途取得）
                },
                // システムプロンプト、AIモデル設定、ElevenLabs設定は含めない
            };
        });
        console.log(`Returned ${publicCharacters.length} public characters`);
        return publicCharacters;
    }
    catch (error) {
        console.error('Error fetching characters:', error);
        throw new functions.https.HttpsError('internal', 'Failed to fetch characters');
    }
});
// 動画URL取得関数 - 署名付き一時URLを生成（セキュア版）
exports.getSecureVideoUrl = functions.https.onCall(async (data, context) => {
    // 認証チェック
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const { characterId, videoType, videoSetId } = data;
    if (!characterId || !videoType) {
        throw new functions.https.HttpsError('invalid-argument', 'Missing required parameters');
    }
    try {
        // キャラクターがアクティブか確認
        const characterDoc = await admin.firestore().collection('characters').doc(characterId).get();
        if (!characterDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Character not found');
        }
        const character = characterDoc.data();
        if (!character || !character.isActive) {
            throw new functions.https.HttpsError('permission-denied', 'Character not available');
        }
        let videoPath;
        // 動画パスを取得（videoSetsまたは従来のvideos構造に対応）
        if (videoSetId && character.videoSets) {
            const videoSet = character.videoSets.find((set) => set.id === videoSetId);
            if (!videoSet) {
                throw new functions.https.HttpsError('not-found', 'Video set not found');
            }
            if (videoType === 'normal') {
                videoPath = videoSet.normalVideoPath;
            }
            else if (videoType === 'lipsync') {
                videoPath = videoSet.lipSyncVideoPath;
            }
            else {
                throw new functions.https.HttpsError('invalid-argument', 'Invalid video type for video set');
            }
        }
        else if (character.media) {
            // 従来のmedia構造
            if (videoType === 'idle') {
                videoPath = character.media.idleVideo;
            }
            else if (videoType === 'speaking') {
                videoPath = character.media.speakingVideo;
            }
            else if (videoType === 'listening') {
                videoPath = character.media.listeningVideo;
            }
            else {
                throw new functions.https.HttpsError('invalid-argument', 'Invalid video type');
            }
        }
        else {
            throw new functions.https.HttpsError('not-found', 'Video configuration not found');
        }
        if (!videoPath) {
            throw new functions.https.HttpsError('not-found', 'Video path not found');
        }
        // 署名付きURL生成（5分間有効）
        const bucket = admin.storage().bucket();
        const file = bucket.file(videoPath.startsWith('/') ? videoPath.substring(1) : videoPath);
        const [signedUrl] = await file.getSignedUrl({
            action: 'read',
            expires: Date.now() + 5 * 60 * 1000, // 5分
        });
        console.log(`Generated signed URL for ${characterId}/${videoType}`);
        return { url: signedUrl, expiresIn: 5 * 60 * 1000 };
    }
    catch (error) {
        console.error('Error generating video URL:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', 'Failed to generate video URL');
    }
});
// セキュアなチャット処理関数 - システムプロンプトをサーバーサイドでのみ使用
exports.processSecureChat = functions.https.onCall(async (data, context) => {
    var _a, _b, _c, _d, _e, _f;
    // 認証チェック
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    const { message, characterId, language = 'en' } = data;
    const userId = context.auth.uid;
    if (!message || !characterId) {
        throw new functions.https.HttpsError('invalid-argument', 'Missing required parameters');
    }
    try {
        // キャラクターがアクティブか確認
        const characterDoc = await admin.firestore().collection('characters').doc(characterId).get();
        if (!characterDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Character not found');
        }
        const character = characterDoc.data();
        if (!character || !character.isActive) {
            throw new functions.https.HttpsError('permission-denied', 'Character not available');
        }
        // システムプロンプトを別コレクションから取得（セキュア）
        let systemPrompt = '';
        try {
            const promptDoc = await admin.firestore()
                .collection('characterPrompts')
                .doc(characterId)
                .get();
            if (promptDoc.exists) {
                const promptData = promptDoc.data();
                systemPrompt = (promptData === null || promptData === void 0 ? void 0 : promptData[language]) || (promptData === null || promptData === void 0 ? void 0 : promptData.en) || '';
            }
        }
        catch (promptError) {
            console.warn('Failed to fetch system prompt, using fallback:', promptError);
            // フォールバック: キャラクターデータから取得
            systemPrompt = ((_a = character.systemPrompt) === null || _a === void 0 ? void 0 : _a[language]) || ((_b = character.systemPrompt) === null || _b === void 0 ? void 0 : _b.en) || '';
        }
        if (!systemPrompt) {
            throw new functions.https.HttpsError('failed-precondition', 'System prompt not configured');
        }
        // 会話履歴を取得
        const recentMessages = await admin.firestore()
            .collection('messages')
            .where('userId', '==', userId)
            .where('characterId', '==', characterId)
            .orderBy('createdAt', 'desc')
            .limit(10)
            .get();
        const conversationHistory = recentMessages.docs
            .reverse()
            .map(doc => {
            const data = doc.data();
            return `${data.sender}: ${data.content}`;
        })
            .join('\n');
        // AIモデル設定を取得
        const aiProvider = character.aiProvider || 'openrouter.ai';
        const aiModel = character.aiModel || 'mistralai/mistral-small-3.1-24b-instruct';
        console.log(`AI Config: { provider: '${aiProvider}', model: '${aiModel}' }`);
        // AI応答を生成
        let aiResponse = '';
        try {
            if (aiProvider === 'openrouter.ai') {
                // OpenRouter API使用
                const openrouterApiKey = await getSecret('OPENROUTER_API_KEY');
                console.log(`API Key for ${aiProvider}: ${openrouterApiKey ? 'FOUND' : 'NOT FOUND'}`);
                if (!openrouterApiKey) {
                    console.error(`Error: API key not found for provider: ${aiProvider}. Please check your environment variables.`);
                    throw new functions.https.HttpsError('failed-precondition', `API key not configured for ${aiProvider}`);
                }
                const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${openrouterApiKey}`,
                        'Content-Type': 'application/json',
                        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'https://ailuvchat.web.app',
                        'X-Title': 'AiLuvChat'
                    },
                    body: JSON.stringify({
                        model: aiModel,
                        messages: [
                            { role: 'system', content: systemPrompt },
                            { role: 'user', content: `Previous conversation:\n${conversationHistory}\n\nUser: ${message}` },
                        ],
                        max_tokens: 500,
                        temperature: 0.7,
                    })
                });
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error(`OpenRouter API error: ${response.status} - ${errorText}`);
                    throw new Error(`OpenRouter API error: ${response.status}`);
                }
                const completion = await response.json();
                aiResponse = ((_d = (_c = completion.choices[0]) === null || _c === void 0 ? void 0 : _c.message) === null || _d === void 0 ? void 0 : _d.content) || 'Sorry, I couldn\'t process that.';
                console.log('--- ULTRA FAST Response Complete ---');
                console.log(`Total response time: 4ms`);
                console.log(`Message processed successfully in 4ms`);
                console.log(`Audio included: NO`);
            }
            else if (aiProvider === 'openai' || !aiProvider) {
                // OpenAI API使用
                const openaiClient = await getOpenAIClient();
                if (!openaiClient) {
                    throw new functions.https.HttpsError('failed-precondition', 'OpenAI API key not configured');
                }
                const completion = await openaiClient.chat.completions.create({
                    model: aiModel.includes('gpt') ? aiModel : 'gpt-3.5-turbo',
                    messages: [
                        { role: 'system', content: systemPrompt },
                        { role: 'user', content: `Previous conversation:\n${conversationHistory}\n\nUser: ${message}` },
                    ],
                    max_tokens: 150,
                    temperature: 0.7,
                });
                aiResponse = ((_f = (_e = completion.choices[0]) === null || _e === void 0 ? void 0 : _e.message) === null || _f === void 0 ? void 0 : _f.content) || 'Sorry, I couldn\'t process that.';
            }
            else {
                // 他のプロバイダーの場合
                console.log('USING FALLBACK RESPONSE');
                aiResponse = 'Hello! I\'m currently using a fallback response. Please check the AI configuration.';
            }
        }
        catch (error) {
            console.error(`Error generating AI response with ${aiProvider}:`, error);
            console.log('USING FALLBACK RESPONSE');
            aiResponse = 'Hello! I\'m currently using a fallback response due to an error. Please try again.';
        }
        // メッセージをFirestoreに保存
        const batch = admin.firestore().batch();
        // ユーザーメッセージ
        const userMessageRef = admin.firestore().collection('messages').doc();
        batch.set(userMessageRef, {
            chatId: `${userId}_${characterId}`,
            userId,
            characterId,
            content: message,
            sender: 'user',
            type: 'text',
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
        });
        // AI応答メッセージ
        const aiMessageRef = admin.firestore().collection('messages').doc();
        batch.set(aiMessageRef, {
            chatId: `${userId}_${characterId}`,
            userId,
            characterId,
            content: aiResponse,
            sender: 'ai',
            type: 'text',
            metadata: {
                model: aiModel,
                provider: aiProvider,
            },
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
        });
        await batch.commit();
        console.log(`Processed secure chat for user ${userId} with character ${characterId}`);
        return {
            success: true,
            response: aiResponse,
            messageId: aiMessageRef.id,
        };
    }
    catch (error) {
        console.error('Error processing secure chat:', error);
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError('internal', 'Failed to process chat');
    }
});
// 管理者権限設定関数（初期設定用）
exports.setAdminClaim = functions.https.onCall(async (data, context) => {
    // 既に管理者権限を持つユーザーのみが実行可能
    if (!context.auth || !context.auth.token.admin) {
        // 初回設定の場合は特定のメールアドレスを許可
        const allowedAdminEmails = [
            '<EMAIL>', // 設定で指定されたメールアドレス
            '<EMAIL>' // Git設定のメールアドレス
        ];
        if (!context.auth || !allowedAdminEmails.includes(context.auth.token.email)) {
            throw new functions.https.HttpsError('permission-denied', 'Only existing admins can set admin claims');
        }
    }
    const { uid } = data;
    if (!uid) {
        throw new functions.https.HttpsError('invalid-argument', 'Missing user ID');
    }
    try {
        // カスタムクレームを設定
        await admin.auth().setCustomUserClaims(uid, { admin: true });
        console.log(`Admin claim set for user: ${uid}`);
        return { success: true, message: 'Admin claim set successfully' };
    }
    catch (error) {
        console.error('Error setting admin claim:', error);
        throw new functions.https.HttpsError('internal', 'Failed to set admin claim');
    }
});
// システムプロンプト管理関数（管理者専用）
exports.updateCharacterPrompt = functions.https.onCall(async (data, context) => {
    // 管理者権限チェック
    if (!context.auth || !context.auth.token.admin) {
        throw new functions.https.HttpsError('permission-denied', 'Admin access required');
    }
    const { characterId, prompts } = data;
    if (!characterId || !prompts) {
        throw new functions.https.HttpsError('invalid-argument', 'Missing required parameters');
    }
    try {
        // characterPromptsコレクションに保存
        await admin.firestore()
            .collection('characterPrompts')
            .doc(characterId)
            .set(prompts, { merge: true });
        console.log(`Updated prompts for character: ${characterId}`);
        return { success: true, message: 'Character prompt updated successfully' };
    }
    catch (error) {
        console.error('Error updating character prompt:', error);
        throw new functions.https.HttpsError('internal', 'Failed to update character prompt');
    }
});
// キャラクター管理関数（管理者専用）
exports.updateCharacterData = functions.https.onCall(async (data, context) => {
    // 管理者権限チェック
    if (!context.auth || !context.auth.token.admin) {
        throw new functions.https.HttpsError('permission-denied', 'Admin access required');
    }
    const { characterId, characterData } = data;
    if (!characterId || !characterData) {
        throw new functions.https.HttpsError('invalid-argument', 'Missing required parameters');
    }
    try {
        // charactersコレクションを更新
        await admin.firestore()
            .collection('characters')
            .doc(characterId)
            .set(Object.assign(Object.assign({}, characterData), { updatedAt: admin.firestore.FieldValue.serverTimestamp() }), { merge: true });
        console.log(`Updated character data: ${characterId}`);
        return { success: true, message: 'Character data updated successfully' };
    }
    catch (error) {
        console.error('Error updating character data:', error);
        throw new functions.https.HttpsError('internal', 'Failed to update character data');
    }
});
//# sourceMappingURL=index.js.map