{"functions": {"src/app/api/chat/route.ts": {"maxDuration": 30}, "src/app/api/voice/generate/route.ts": {"maxDuration": 30}, "src/app/api/warmup/route.ts": {"maxDuration": 60}}, "crons": [{"path": "/api/warmup", "schedule": "*/5 * * * *"}, {"path": "/api/warmup", "schedule": "0 */2 * * *"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "/data/(.*)", "headers": [{"key": "X-Robots-Tag", "value": "noindex, nofollow, nosnippet, noarchive"}]}], "rewrites": [{"source": "/data/(.*)", "destination": "/404"}, {"source": "/admin/(.*)", "destination": "/404"}]}