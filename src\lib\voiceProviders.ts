// Voice provider configurations
export interface VoiceProviderConfig {
  name: string;
  apiKeyEnvVar: string;
  baseUrl: string;
  supportedFormats: string[];
  supportedLanguages: string[];
  streamingSupported: boolean;
  models: Record<string, {
    name: string;
    description: string;
    maxTextLength?: number;
  }>;
}

// Voice provider configurations
export const VOICE_PROVIDERS: Record<string, VoiceProviderConfig> = {
  'elevenlabs': {
    name: 'ElevenLabs',
    apiKeyEnvVar: 'ELEVENLABS_API_KEY',
    baseUrl: 'https://api.elevenlabs.io/v1',
    supportedFormats: ['mp3', 'wav'],
    supportedLanguages: ['en', 'ja', 'es', 'fr', 'de', 'it', 'pt', 'pl', 'tr', 'ru', 'nl', 'cs', 'ar', 'zh', 'hu', 'ko'],
    streamingSupported: true,
    models: {
      'eleven_turbo_v2_5': {
        name: 'Eleven Turbo v2.5',
        description: 'Fastest model with good quality',
        maxTextLength: 5000
      },
      'eleven_multilingual_v2': {
        name: 'Eleven Multilingual v2',
        description: 'Best for multilingual content',
        maxTextLength: 5000
      }
    }
  },

  'minimax': {
    name: 'MiniMax',
    apiKeyEnvVar: 'MINIMAX_API_KEY',
    baseUrl: 'https://api.minimax.chat/v1',
    supportedFormats: ['mp3', 'wav', 'pcm'],
    supportedLanguages: ['en', 'ja', 'zh'],
    streamingSupported: true,
    models: {
      'speech-01': {
        name: 'Speech-01',
        description: 'Standard quality voice synthesis',
        maxTextLength: 3000
      },
      'speech-01-turbo': {
        name: 'Speech-01 Turbo',
        description: 'Fast voice synthesis with good quality',
        maxTextLength: 3000
      }
    }
  }
};

// Voice provider utility functions
export const getVoiceProvider = (provider: string): VoiceProviderConfig | null => {
  return VOICE_PROVIDERS[provider] || null;
};

export const getSupportedVoiceProviders = (): string[] => {
  return Object.keys(VOICE_PROVIDERS);
};

export const isVoiceProviderSupported = (provider: string): boolean => {
  return provider in VOICE_PROVIDERS;
};

// MiniMax specific types and utilities
export interface MinimaxVoiceSettings {
  voice_id: string;
  speed?: number; // 0.5 - 2.0
  vol?: number;   // 0.1 - 3.0
  pitch?: number; // -12 - 12
  audio_sample_rate?: number; // 16000, 22050, 24000, 32000, 44100, 48000
  bitrate?: number; // 64000, 128000, 192000, 256000, 320000
}

export interface MinimaxStreamRequest {
  text: string;
  voice_setting: MinimaxVoiceSettings;
  model?: string;
  audio_setting?: {
    sample_rate?: number;
    bitrate?: number;
    format?: 'mp3' | 'wav' | 'pcm';
  };
  pronunciation_dict?: Record<string, string>;
  audio_extra_config?: {
    audio_optimize?: number; // 0 or 1
  };
}

// Default MiniMax voice settings
export const DEFAULT_MINIMAX_SETTINGS: Partial<MinimaxVoiceSettings> = {
  speed: 1.0,
  vol: 1.0,
  pitch: 0,
  audio_sample_rate: 22050,
  bitrate: 128000
};
