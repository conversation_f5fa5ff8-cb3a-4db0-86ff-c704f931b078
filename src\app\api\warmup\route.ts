// Serverless function warmup endpoint
import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';
// Note: Edge Runtime disabled for broader Node.js compatibility
// export const runtime = 'edge';
export const preferredRegion = 'auto';

interface WarmupResult {
  endpoint: string;
  status: 'success' | 'error';
  responseTime: number;
  error?: string;
}

// Critical endpoints to warmup
const CRITICAL_ENDPOINTS = [
  '/api/chat',
  '/api/voice/generate',
  '/api/characters',
  '/api/voice/optimize'
];

// GET: Health check and basic warmup
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    console.log('🔥 Warmup endpoint called');
    
    // Basic warmup operations
    const warmupResults: WarmupResult[] = [];
    
    // Warmup critical endpoints
    for (const endpoint of CRITICAL_ENDPOINTS) {
      const endpointResult = await warmupEndpoint(endpoint, request);
      warmupResults.push(endpointResult);
    }
    
    const totalTime = Date.now() - startTime;
    const successCount = warmupResults.filter(r => r.status === 'success').length;
    
    console.log(`✅ Warmup completed: ${successCount}/${warmupResults.length} endpoints in ${totalTime}ms`);
    
    return NextResponse.json({
      success: true,
      message: 'Warmup completed',
      totalTime,
      results: warmupResults,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Warmup failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      totalTime: Date.now() - startTime,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// POST: Comprehensive warmup with specific targets
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    const body = await request.json();
    const { 
      endpoints = CRITICAL_ENDPOINTS,
      includeVoiceWarmup = true,
      includeCachePreload = true 
    } = body;
    
    console.log('🚀 Comprehensive warmup started');
    console.log('Endpoints:', endpoints);
    console.log('Voice warmup:', includeVoiceWarmup);
    console.log('Cache preload:', includeCachePreload);
    
    const results: any = {
      endpoints: [],
      voiceWarmup: null,
      cachePreload: null
    };
    
    // 1. Warmup endpoints
    for (const endpoint of endpoints) {
      const result = await warmupEndpoint(endpoint, request);
      results.endpoints.push(result);
    }
    
    // 2. Voice system warmup
    if (includeVoiceWarmup) {
      try {
        const voiceResult = await warmupVoiceSystem();
        results.voiceWarmup = voiceResult;
      } catch (error) {
        results.voiceWarmup = {
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }
    
    // 3. Cache preloading
    if (includeCachePreload) {
      try {
        const cacheResult = await preloadCaches();
        results.cachePreload = cacheResult;
      } catch (error) {
        results.cachePreload = {
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }
    
    const totalTime = Date.now() - startTime;
    
    console.log(`✅ Comprehensive warmup completed in ${totalTime}ms`);
    
    return NextResponse.json({
      success: true,
      message: 'Comprehensive warmup completed',
      totalTime,
      results,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Comprehensive warmup failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      totalTime: Date.now() - startTime,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Warmup individual endpoint
async function warmupEndpoint(endpoint: string, request: NextRequest): Promise<WarmupResult> {
  const startTime = Date.now();
  
  try {
    const baseUrl = new URL(request.url).origin;
    const fullUrl = `${baseUrl}${endpoint}`;
    
    // Determine appropriate method and body for each endpoint
    let method = 'GET';
    let body = null;
    let headers: Record<string, string> = {};
    
    if (endpoint === '/api/chat') {
      method = 'POST';
      headers['Content-Type'] = 'application/json';
      body = JSON.stringify({
        message: 'warmup',
        characterId: 'test',
        userId: 'warmup-user',
        language: 'en',
        testMode: true
      });
    } else if (endpoint === '/api/voice/generate') {
      method = 'POST';
      headers['Content-Type'] = 'application/json';
      body = JSON.stringify({
        text: 'warmup',
        voiceId: 'test',
        language: 'en',
        characterId: 'warmup-test',
        preload: true
      });
    }
    
    const response = await fetch(fullUrl, {
      method,
      headers,
      body
    });
    
    const responseTime = Date.now() - startTime;
    
    if (response.ok || response.status === 400) { // 400 is expected for test data
      return {
        endpoint,
        status: 'success',
        responseTime
      };
    } else {
      return {
        endpoint,
        status: 'error',
        responseTime,
        error: `HTTP ${response.status}`
      };
    }
    
  } catch (error) {
    return {
      endpoint,
      status: 'error',
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Warmup voice system
async function warmupVoiceSystem(): Promise<any> {
  const startTime = Date.now();
  
  try {
    // Trigger voice optimization warmup
    const response = await fetch('/api/voice/optimize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'auto-optimize' })
    });
    
    const responseTime = Date.now() - startTime;
    
    if (response.ok) {
      const data = await response.json();
      return {
        status: 'success',
        responseTime,
        message: data.message
      };
    } else {
      return {
        status: 'error',
        responseTime,
        error: `HTTP ${response.status}`
      };
    }
    
  } catch (error) {
    return {
      status: 'error',
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Preload caches
async function preloadCaches(): Promise<any> {
  const startTime = Date.now();
  
  try {
    // Get characters data to populate cache
    const response = await fetch('/api/characters');
    const responseTime = Date.now() - startTime;
    
    if (response.ok) {
      const data = await response.json();
      return {
        status: 'success',
        responseTime,
        charactersLoaded: data.characters?.length || 0
      };
    } else {
      return {
        status: 'error',
        responseTime,
        error: `HTTP ${response.status}`
      };
    }
    
  } catch (error) {
    return {
      status: 'error',
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
