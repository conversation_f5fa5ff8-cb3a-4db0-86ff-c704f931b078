import { NextRequest, NextResponse } from 'next/server';
import Secret<PERSON>ache from '@/lib/server/secretCache';
import ServerLogger from '@/lib/server/logger';
import { MinimaxStreamRequest, DEFAULT_MINIMAX_SETTINGS } from '@/lib/voiceProviders';

export const dynamic = 'force-dynamic';
export const preferredRegion = 'auto';

// Admin token validation
function validateAdminToken(request: NextRequest): boolean {
  const adminToken = request.headers.get('x-admin-token');
  const expectedToken = process.env.NEXT_PUBLIC_ADMIN_TOKEN || 'your-secure-admin-token-here-change-this';
  return adminToken === expectedToken;
}

// GET: Fetch available MiniMax voices
export async function GET(request: NextRequest) {
  try {
    // Validate admin token
    if (!validateAdminToken(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('🎤 Fetching MiniMax voices list');

    // Initialize secret cache if not already done
    await SecretCache.initialize();

    // Get MiniMax API credentials from cache
    const apiKey = SecretCache.get('MINIMAX_API_KEY');
    const groupId = SecretCache.get('MINIMAX_GROUP_ID');
    
    if (!apiKey || !groupId) {
      console.warn('MiniMax API credentials not configured, using fallback voices');

      // Return fallback voices instead of error
      const fallbackVoices = [
        { voice_id: 'Wise_Woman', voice_name: 'Wise Woman', display_name: 'Wise Woman (Female, Adult)' },
        { voice_id: 'Friendly_Person', voice_name: 'Friendly Person', display_name: 'Friendly Person (Neutral, Adult)' },
        { voice_id: 'Inspirational_girl', voice_name: 'Inspirational Girl', display_name: 'Inspirational Girl (Female, Young)' },
        { voice_id: 'Deep_Voice_Man', voice_name: 'Deep Voice Man', display_name: 'Deep Voice Man (Male, Adult)' },
        { voice_id: 'Calm_Woman', voice_name: 'Calm Woman', display_name: 'Calm Woman (Female, Adult)' },
        { voice_id: 'Custom', voice_name: 'Custom', display_name: 'Custom Voice ID' }
      ];

      return NextResponse.json({
        success: true,
        voices: fallbackVoices
      });
    }

    // Try to fetch voices from MiniMax API
    try {
      const response = await fetch(`https://api.minimaxi.chat/v1/voice_list?GroupId=${groupId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Successfully fetched MiniMax voices:', data);
        
        // Transform the response to match our expected format
        const voices = data.voices?.map((voice: any) => ({
          voice_id: voice.voice_id,
          voice_name: voice.voice_name || voice.voice_id,
          display_name: voice.display_name || `${voice.voice_name || voice.voice_id} (${voice.gender || 'Unknown'}, ${voice.age || 'Unknown'})`
        })) || [];

        // Add custom option
        voices.push({
          voice_id: 'Custom',
          voice_name: 'Custom',
          display_name: 'Custom Voice ID'
        });

        return NextResponse.json({
          success: true,
          voices: voices
        });
      } else {
        console.error('MiniMax API error:', response.status, response.statusText);
        throw new Error(`MiniMax API error: ${response.status}`);
      }
    } catch (apiError) {
      console.error('Error calling MiniMax API:', apiError);
      
      // Return fallback voices
      const fallbackVoices = [
        { voice_id: 'Wise_Woman', voice_name: 'Wise Woman', display_name: 'Wise Woman (Female, Adult)' },
        { voice_id: 'Friendly_Person', voice_name: 'Friendly Person', display_name: 'Friendly Person (Neutral, Adult)' },
        { voice_id: 'Inspirational_girl', voice_name: 'Inspirational Girl', display_name: 'Inspirational Girl (Female, Young)' },
        { voice_id: 'Deep_Voice_Man', voice_name: 'Deep Voice Man', display_name: 'Deep Voice Man (Male, Adult)' },
        { voice_id: 'Calm_Woman', voice_name: 'Calm Woman', display_name: 'Calm Woman (Female, Adult)' },
        { voice_id: 'Custom', voice_name: 'Custom', display_name: 'Custom Voice ID' }
      ];

      return NextResponse.json({
        success: true,
        voices: fallbackVoices
      });
    }

  } catch (error) {
    console.error('MiniMax voices API error:', error);

    // Return fallback voices even on error to prevent UI breaking
    const fallbackVoices = [
      { voice_id: 'Wise_Woman', voice_name: 'Wise Woman', display_name: 'Wise Woman (Female, Adult)' },
      { voice_id: 'Friendly_Person', voice_name: 'Friendly Person', display_name: 'Friendly Person (Neutral, Adult)' },
      { voice_id: 'Inspirational_girl', voice_name: 'Inspirational Girl', display_name: 'Inspirational Girl (Female, Young)' },
      { voice_id: 'Deep_Voice_Man', voice_name: 'Deep Voice Man', display_name: 'Deep Voice Man (Male, Adult)' },
      { voice_id: 'Calm_Woman', voice_name: 'Calm Woman', display_name: 'Calm Woman (Female, Adult)' },
      { voice_id: 'Custom', voice_name: 'Custom', display_name: 'Custom Voice ID' }
    ];

    return NextResponse.json({
      success: true,
      voices: fallbackVoices,
      warning: 'Using fallback voices due to API error'
    });
  }
}

// POST: Test voice generation
export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    // Initialize secret cache if not already done
    await SecretCache.initialize();

    // Validate admin token
    if (!validateAdminToken(request)) {
      ServerLogger.warn('Unauthorized access to MiniMax voice test API');
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { voiceId, language, text, model = 'speech-02-turbo' } = await request.json();

    if (!voiceId || !text) {
      ServerLogger.warn('Missing required parameters for MiniMax voice test');
      return NextResponse.json(
        { success: false, error: 'Missing required parameters: voiceId, text' },
        { status: 400 }
      );
    }

    ServerLogger.voice('minimax', `Testing voice: ${voiceId} (${language}), Model: ${model}`);
    ServerLogger.debug(`Text: ${text.substring(0, 50)}...`);

    // Get MiniMax API credentials from cache (optimized)
    const apiKey = SecretCache.get('MINIMAX_API_KEY');
    const groupId = SecretCache.get('MINIMAX_GROUP_ID');

    if (!apiKey || !groupId) {
      ServerLogger.error('MiniMax API credentials not configured or not cached');
      throw new Error('MiniMax API credentials not configured');
    }

    // Prepare MiniMax request payload (based on working sample)
    const minimaxRequest = {
      model,
      text,
      stream: true, // Important: enable streaming
      voice_setting: {
        voice_id: voiceId,
        speed: DEFAULT_MINIMAX_SETTINGS.speed || 1.0,
        vol: DEFAULT_MINIMAX_SETTINGS.vol || 1.0,
        pitch: DEFAULT_MINIMAX_SETTINGS.pitch || 0
      },
      audio_setting: {
        format: 'mp3'
      }
    };

    ServerLogger.debug('Calling MiniMax T2A V2 API directly');
    ServerLogger.debug('Request payload', { text: text.substring(0, 50) + '...', voiceId, language, model });

    // Call MiniMax T2A V2 streaming API directly (based on working sample)
    const apiUrl = `https://api.minimaxi.chat/v1/t2a_v2?GroupId=${groupId}`;
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(minimaxRequest)
    });

    ServerLogger.api('POST', apiUrl, response.status, Date.now() - startTime);

    if (!response.ok) {
      const errorText = await response.text();
      ServerLogger.error(`MiniMax test API error: ${response.status} ${response.statusText}`);
      ServerLogger.debug('Error response body', errorText);
      throw new Error(`MiniMax test API error: ${response.status} - ${errorText}`);
    }

    // Handle streaming response from MiniMax Direct API (same as existing implementation)
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response body from MiniMax Direct API');
    }

    const decoder = new TextDecoder();
    const audioChunks: Uint8Array[] = [];
    let totalLength = 0;
    let chunkCount = 0;

    // Utility function to convert hex string to bytes (same as existing implementation)
    function hexToBytes(hex: string): Uint8Array {
      const bytes = new Uint8Array(hex.length / 2);
      for (let i = 0; i < hex.length; i += 2) {
        bytes[i / 2] = parseInt(hex.substr(i, 2), 16);
      }
      return bytes;
    }

    ServerLogger.debug('Starting to read MiniMax Direct streaming response');

    let buffer = '';

    while (true) {
      const { done, value } = await reader.read();

      if (done) {
        ServerLogger.debug('MiniMax Direct stream completed');
        break;
      }

      // Decode the chunk and handle partial lines (based on working sample)
      const decodedChunk = decoder.decode(value, { stream: true });
      // ServerLogger.debug('Raw chunk received', decodedChunk); // Commented out to avoid binary data in logs
      buffer += decodedChunk;
      const lines = buffer.split('\n');
      buffer = lines.pop() || ''; // Keep the last incomplete line in buffer

      ServerLogger.debug('Processing lines', lines.length);
      for (const line of lines) {
        ServerLogger.debug('Processing line', line);
        if (line.startsWith('data:')) {
          try {
            const jsonStr = line.substring(5).trim(); // Remove 'data:' prefix
            if (jsonStr) {
              const data = JSON.parse(jsonStr);

              // Debug: Log the actual response structure
              ServerLogger.debug('MiniMax response data', JSON.stringify(data, null, 2));

              // Check for audio data (based on working sample format)
              if (data.data && data.data.audio && !data.extra_info) {
                ServerLogger.debug('Found audio data in response');
                const hexAudio = data.data.audio;
                const audioBytes = hexToBytes(hexAudio);

                audioChunks.push(audioBytes);
                chunkCount++;
                totalLength += audioBytes.length;

                ServerLogger.chunk('audio', chunkCount, audioBytes.length);
              } else {
                ServerLogger.debug('No audio data found. Conditions:', {
                  'data.data exists': !!data.data,
                  'data.data.audio exists': !!(data.data && data.data.audio),
                  'data.extra_info exists': !!data.extra_info
                });
              }

              if (data.data && data.data.status === 'finished') {
                ServerLogger.voice('minimax', 'Generation finished');
              }
            }
          } catch (parseError) {
            ServerLogger.error('Error parsing MiniMax Direct chunk', parseError);
            ServerLogger.debug('Problematic line', line);
            // Continue processing other chunks
          }
        }
      }
    }

    // Send completion signal (same as existing implementation)
    if (audioChunks.length > 0) {
      // Combine all chunks for final audio
      const combinedAudio = new Uint8Array(totalLength);
      let offset = 0;
      for (const chunk of audioChunks) {
        combinedAudio.set(chunk, offset);
        offset += chunk.length;
      }

      const finalBase64 = Buffer.from(combinedAudio).toString('base64');
      const audioUrl = `data:audio/mpeg;base64,${finalBase64}`;

      ServerLogger.perf(`MiniMax Direct test complete: ${chunkCount} chunks, ${totalLength} bytes`, startTime);

      return NextResponse.json({
        success: true,
        audioUrl: audioUrl,
        message: 'MiniMax Direct voice test completed successfully',
        totalChunks: chunkCount,
        totalSize: totalLength
      });
    } else {
      throw new Error('No audio chunks received from MiniMax Direct');
    }

  } catch (error) {
    ServerLogger.error('MiniMax voice test error', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle preflight requests
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, x-admin-token',
    },
  });
}
