"use client";

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useI18n } from '@/contexts/I18nProvider';
import { useRouter } from 'next/navigation';
import { Timestamp } from 'firebase/firestore';
import { User } from '@/types/firebase';
import PointsService from '@/lib/pointsService';
import Image from 'next/image';

export default function ProfilePage() {
  const { user, userData, updateUserProfile, logout } = useAuth();
  const { t, language } = useI18n();
  const router = useRouter();

  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Form state
  const [formData, setFormData] = useState({
    displayName: '',
    gender: '' as 'male' | 'female' | 'non-binary' | 'prefer-not-to-say' | '',
    birthDate: '',
    photoURL: ''
  });

  // Redirect if not logged in
  useEffect(() => {
    if (!user) {
      const homeUrl = language === 'ja' ? '/ja' : '/';
      router.push(homeUrl);
    }
  }, [user, router, language]);

  // Initialize form data
  useEffect(() => {
    if (userData) {
      setFormData({
        displayName: userData.displayName || '',
        gender: userData.gender || '',
        birthDate: userData.birthDate ?
          userData.birthDate.toDate().toISOString().split('T')[0] : '',
        photoURL: userData.photoURL || ''
      });
    }
  }, [userData]);

  // Check for daily points refresh on page load
  useEffect(() => {
    if (user && userData?.subscriptionTier === 'free') {
      PointsService.checkAndApplyDailyRefresh(user.uid);
    }
  }, [user, userData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = async () => {
    if (!user) return;

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const updateData: Partial<User> = {
        displayName: formData.displayName,
        gender: formData.gender as any,
        photoURL: formData.photoURL
      };

      // Convert birth date to Timestamp if provided
      if (formData.birthDate) {
        updateData.birthDate = Timestamp.fromDate(new Date(formData.birthDate));
      }

      await updateUserProfile(updateData);
      setSuccess(language === 'ja' ? 'プロフィールを更新しました' : 'Profile updated successfully');
      setIsEditing(false);
    } catch (err: any) {
      setError(err.message || (language === 'ja' ? '更新に失敗しました' : 'Failed to update profile'));
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAccount = async () => {
    if (!confirm(language === 'ja' ? '本当にアカウントを削除しますか？' : 'Are you sure you want to delete your account?')) {
      return;
    }

    try {
      // Here you would implement account deletion
      // For now, just logout
      await logout();
      const homeUrl = language === 'ja' ? '/ja' : '/';
      router.push(homeUrl);
    } catch (err: any) {
      setError(err.message || (language === 'ja' ? 'アカウント削除に失敗しました' : 'Failed to delete account'));
    }
  };

  const getGenderLabel = (gender: string) => {
    const labels = {
      male: language === 'ja' ? '男性' : 'Male',
      female: language === 'ja' ? '女性' : 'Female',
      'non-binary': language === 'ja' ? '中性' : 'Non-binary',
      'prefer-not-to-say': language === 'ja' ? '無回答' : 'Prefer not to say'
    };
    return labels[gender as keyof typeof labels] || '';
  };

  if (!user || !userData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">{language === 'ja' ? '読み込み中...' : 'Loading...'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold text-gray-900">
              {language === 'ja' ? 'マイページ' : 'My Profile'}
            </h1>
            <button
              onClick={() => {
                const homeUrl = language === 'ja' ? '/ja' : '/';
                router.push(homeUrl);
              }}
              className="text-gray-500 hover:text-gray-700"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Profile Information */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  {language === 'ja' ? 'プロフィール情報' : 'Profile Information'}
                </h2>
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className="bg-primary text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors"
                >
                  {isEditing
                    ? (language === 'ja' ? 'キャンセル' : 'Cancel')
                    : (language === 'ja' ? '編集' : 'Edit')
                  }
                </button>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
              )}

              {success && (
                <div className="bg-green-50 border border-green-200 rounded-md p-3 mb-4">
                  <p className="text-green-600 text-sm">{success}</p>
                </div>
              )}

              <div className="space-y-6">
                {/* Profile Picture */}
                <div className="flex items-center space-x-4">
                  <div className="relative w-20 h-20">
                    {(isEditing ? formData.photoURL : userData.photoURL) ? (
                      <Image
                        src={isEditing ? formData.photoURL : userData.photoURL!}
                        alt="Profile"
                        fill
                        className="rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-20 h-20 bg-gray-300 rounded-full flex items-center justify-center">
                        <svg className="w-8 h-8 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                      </div>
                    )}
                  </div>
                  {isEditing && (
                    <div className="flex-1">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {language === 'ja' ? 'プロフィール画像URL' : 'Profile Image URL'}
                      </label>
                      <input
                        type="url"
                        name="photoURL"
                        value={formData.photoURL}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        placeholder="https://example.com/image.jpg"
                      />
                    </div>
                  )}
                </div>

                {/* Display Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {language === 'ja' ? '表示名' : 'Display Name'}
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      name="displayName"
                      value={formData.displayName}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      required
                    />
                  ) : (
                    <p className="text-gray-900">{userData.displayName}</p>
                  )}
                </div>

                {/* Email */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {language === 'ja' ? 'メールアドレス' : 'Email Address'}
                  </label>
                  <p className="text-gray-900">{userData.email}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {language === 'ja' ? 'メールアドレスは変更できません' : 'Email address cannot be changed'}
                  </p>
                </div>

                {/* Gender */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {language === 'ja' ? '性別' : 'Gender'}
                  </label>
                  {isEditing ? (
                    <select
                      name="gender"
                      value={formData.gender}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    >
                      <option value="">{language === 'ja' ? '選択してください' : 'Please select'}</option>
                      <option value="male">{language === 'ja' ? '男性' : 'Male'}</option>
                      <option value="female">{language === 'ja' ? '女性' : 'Female'}</option>
                      <option value="non-binary">{language === 'ja' ? '中性' : 'Non-binary'}</option>
                      <option value="prefer-not-to-say">{language === 'ja' ? '無回答' : 'Prefer not to say'}</option>
                    </select>
                  ) : (
                    <p className="text-gray-900">
                      {userData.gender ? getGenderLabel(userData.gender) : (language === 'ja' ? '未設定' : 'Not set')}
                    </p>
                  )}
                </div>

                {/* Birth Date */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {language === 'ja' ? '生年月日' : 'Birth Date'}
                  </label>
                  {isEditing ? (
                    <input
                      type="date"
                      name="birthDate"
                      value={formData.birthDate}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  ) : (
                    <p className="text-gray-900">
                      {userData.birthDate
                        ? userData.birthDate.toDate().toLocaleDateString(language === 'ja' ? 'ja-JP' : 'en-US')
                        : (language === 'ja' ? '未設定' : 'Not set')
                      }
                    </p>
                  )}
                </div>

                {isEditing && (
                  <div className="flex space-x-4">
                    <button
                      onClick={handleSave}
                      disabled={loading}
                      className="bg-primary text-white px-6 py-2 rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50"
                    >
                      {loading
                        ? (language === 'ja' ? '保存中...' : 'Saving...')
                        : (language === 'ja' ? '保存' : 'Save')
                      }
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Points Card */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {language === 'ja' ? 'ポイント' : 'Points'}
              </h3>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">
                  {userData.points}
                </div>
                <p className="text-sm text-gray-600">
                  {language === 'ja' ? '現在のポイント' : 'Current Points'}
                </p>
                {userData.subscriptionTier === 'free' && (
                  <p className="text-xs text-gray-500 mt-2">
                    {language === 'ja'
                      ? '無料会員：毎日最大50ポイントまで補充'
                      : 'Free tier: Daily refresh up to 50 points'
                    }
                  </p>
                )}
              </div>
            </div>

            {/* Account Actions */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {language === 'ja' ? 'アカウント' : 'Account'}
              </h3>
              <div className="space-y-3">
                <button
                  onClick={() => router.push('/points')}
                  className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
                >
                  {language === 'ja' ? 'ポイント履歴' : 'Points History'}
                </button>
                <button
                  onClick={logout}
                  className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
                >
                  {language === 'ja' ? 'ログアウト' : 'Logout'}
                </button>
                <button
                  onClick={handleDeleteAccount}
                  className="w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                >
                  {language === 'ja' ? '退会' : 'Delete Account'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
