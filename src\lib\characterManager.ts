/**
 * Character Management System
 * Handles character data, media files, and metadata
 */

import fs from 'fs';
import path from 'path';

export interface CharacterMedia {
  // Profile images
  profileImage: string;
  thumbnailImage: string;

  // Video files for different states
  idleVideo: string;
  speakingVideo: string;
  listeningVideo?: string;

  // Audio files (optional)
  voiceSample?: string;
}

export interface VideoSet {
  id: string;
  normalVideoPath?: string;
  lipSyncVideoPath?: string;
  uploaded: boolean;
}

export interface CharacterMetadata {
  id: string;
  name: {
    en: string;
    ja: string;
  };
  description: {
    en: string;
    ja: string;
  };
  personality: {
    en: string[];
    ja: string[];
  };
  interests: {
    en: string[];
    ja: string[];
  };
  age?: number;
  occupation?: {
    en: string;
    ja: string;
  };
  background?: {
    en: string;
    ja: string;
  };
  welcomeMessage?: {
    en: string;
    ja: string;
  };
  isActive: boolean;
  isPremium: boolean;
  sortOrder: number;
  elevenLabsVoiceId?: string;
  aiProvider?: string;
  aiModel?: string;
  videoSets?: VideoSet[];
  createdAt: string;
  updatedAt: string;
}

export interface FullCharacterData extends CharacterMetadata {
  media: CharacterMedia;
}

// File paths for data persistence
const DATA_DIR = path.join(process.cwd(), 'data');
const CHARACTERS_FILE = path.join(DATA_DIR, 'characters.json');

// Ensure data directory exists
if (typeof window === 'undefined') { // Server-side only
  if (!fs.existsSync(DATA_DIR)) {
    fs.mkdirSync(DATA_DIR, { recursive: true });
  }
}

// Load characters from file or use default data
function loadCharactersFromFile(): Record<string, FullCharacterData> {
  if (typeof window !== 'undefined') {
    // Client-side: return empty object, data will be fetched via API
    return {};
  }

  try {
    if (fs.existsSync(CHARACTERS_FILE)) {
      const data = fs.readFileSync(CHARACTERS_FILE, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Error loading characters from file:', error);
  }

  // Return default characters if file doesn't exist or has errors
  return getDefaultCharacters();
}

// Save characters to file
function saveCharactersToFile(characters: Record<string, FullCharacterData>): boolean {
  if (typeof window !== 'undefined') {
    // Client-side: cannot save to file
    return false;
  }

  try {
    fs.writeFileSync(CHARACTERS_FILE, JSON.stringify(characters, null, 2));
    return true;
  } catch (error) {
    console.error('Error saving characters to file:', error);
    return false;
  }
}

// Default character database
function getDefaultCharacters(): Record<string, FullCharacterData> {
  return {
  sakura: {
    id: 'sakura',
    name: {
      en: 'Sakura',
      ja: 'さくら'
    },
    description: {
      en: 'A gentle and empathetic companion who loves nature and meaningful conversations.',
      ja: '自然と意味のある会話を愛する、優しく共感的なコンパニオン。'
    },
    personality: {
      en: ['Gentle', 'Empathetic', 'Wise', 'Caring', 'Optimistic'],
      ja: ['優しい', '共感的', '賢い', '思いやりがある', '楽観的']
    },
    interests: {
      en: ['Nature', 'Art', 'Meditation', 'Poetry', 'Gardening'],
      ja: ['自然', 'アート', '瞑想', '詩', 'ガーデニング']
    },
    age: 25,
    occupation: {
      en: 'Nature Guide & Counselor',
      ja: 'ネイチャーガイド・カウンセラー'
    },
    background: {
      en: 'Sakura grew up surrounded by cherry blossoms and learned the art of finding beauty in every moment.',
      ja: 'さくらは桜に囲まれて育ち、あらゆる瞬間に美しさを見つける術を学びました。'
    },
    isActive: true,
    isPremium: false,
    sortOrder: 1,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    media: {
      profileImage: '/characters/images/sakura-profile.jpg',
      thumbnailImage: '/characters/images/sakura-thumb.jpg',
      idleVideo: '/characters/videos/sakura-idle.mp4',
      speakingVideo: '/characters/videos/sakura-speaking.mp4',
      listeningVideo: '/characters/videos/sakura-listening.mp4',
      voiceSample: '/characters/audio/sakura-voice.mp3'
    }
  },
  alex: {
    id: 'alex',
    name: {
      en: 'Alex',
      ja: 'アレックス'
    },
    description: {
      en: 'An enthusiastic gamer and tech expert who loves sharing gaming experiences.',
      ja: 'ゲーム体験を共有することを愛する、熱心なゲーマーでテック専門家。'
    },
    personality: {
      en: ['Energetic', 'Friendly', 'Competitive', 'Tech-savvy', 'Supportive'],
      ja: ['エネルギッシュ', 'フレンドリー', '競争心がある', 'テクノロジーに精通', 'サポート的']
    },
    interests: {
      en: ['Gaming', 'Technology', 'Esports', 'Streaming', 'Game Development'],
      ja: ['ゲーミング', 'テクノロジー', 'eスポーツ', 'ストリーミング', 'ゲーム開発']
    },
    age: 23,
    occupation: {
      en: 'Professional Gamer & Streamer',
      ja: 'プロゲーマー・ストリーマー'
    },
    background: {
      en: 'Alex started gaming at a young age and turned passion into profession through dedication and skill.',
      ja: 'アレックスは幼い頃からゲームを始め、献身とスキルによって情熱を職業に変えました。'
    },
    isActive: true,
    isPremium: false,
    sortOrder: 2,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    media: {
      profileImage: '/characters/images/alex-profile.jpg',
      thumbnailImage: '/characters/images/alex-thumb.jpg',
      idleVideo: '/characters/videos/alex-idle.mp4',
      speakingVideo: '/characters/videos/alex-speaking.mp4',
      listeningVideo: '/characters/videos/alex-listening.mp4',
      voiceSample: '/characters/audio/alex-voice.mp3'
    }
  },
  luna: {
    id: 'luna',
    name: {
      en: 'Luna',
      ja: 'ルナ'
    },
    description: {
      en: 'A mystical and wise companion with deep knowledge of spirituality and magic.',
      ja: 'スピリチュアリティと魔法の深い知識を持つ、神秘的で賢いコンパニオン。'
    },
    personality: {
      en: ['Mystical', 'Wise', 'Intuitive', 'Calm', 'Spiritual'],
      ja: ['神秘的', '賢い', '直感的', '穏やか', 'スピリチュアル']
    },
    interests: {
      en: ['Astrology', 'Tarot', 'Meditation', 'Crystals', 'Moon Phases'],
      ja: ['占星術', 'タロット', '瞑想', 'クリスタル', '月の満ち欠け']
    },
    age: 28,
    occupation: {
      en: 'Spiritual Guide & Mystic',
      ja: 'スピリチュアルガイド・神秘家'
    },
    background: {
      en: 'Luna has studied ancient wisdom traditions and helps others connect with their inner magic.',
      ja: 'ルナは古代の知恵の伝統を学び、他の人が内なる魔法とつながるのを助けています。'
    },
    isActive: true,
    isPremium: true,
    sortOrder: 3,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    media: {
      profileImage: '/characters/images/luna-profile.jpg',
      thumbnailImage: '/characters/images/luna-thumb.jpg',
      idleVideo: '/characters/videos/luna-idle.mp4',
      speakingVideo: '/characters/videos/luna-speaking.mp4',
      listeningVideo: '/characters/videos/luna-listening.mp4',
      voiceSample: '/characters/audio/luna-voice.mp3'
    }
  },
  kai: {
    id: 'kai',
    name: {
      en: 'Kai',
      ja: 'カイ'
    },
    description: {
      en: 'An adventurous nature lover who promotes outdoor activities and environmental awareness.',
      ja: 'アウトドア活動と環境意識を促進する、冒険好きの自然愛好家。'
    },
    personality: {
      en: ['Adventurous', 'Grounded', 'Active', 'Environmental', 'Balanced'],
      ja: ['冒険的', '地に足がついた', 'アクティブ', '環境意識が高い', 'バランスが取れた']
    },
    interests: {
      en: ['Hiking', 'Camping', 'Rock Climbing', 'Photography', 'Conservation'],
      ja: ['ハイキング', 'キャンプ', 'ロッククライミング', '写真', '自然保護']
    },
    age: 26,
    occupation: {
      en: 'Outdoor Adventure Guide',
      ja: 'アウトドアアドベンチャーガイド'
    },
    background: {
      en: 'Kai grew up in the mountains and dedicates life to sharing the beauty and importance of nature.',
      ja: 'カイは山で育ち、自然の美しさと重要性を共有することに人生を捧げています。'
    },
    isActive: true,
    isPremium: false,
    sortOrder: 4,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    media: {
      profileImage: '/characters/images/kai-profile.jpg',
      thumbnailImage: '/characters/images/kai-thumb.jpg',
      idleVideo: '/characters/videos/kai-idle.mp4',
      speakingVideo: '/characters/videos/kai-speaking.mp4',
      listeningVideo: '/characters/videos/kai-listening.mp4',
      voiceSample: '/characters/audio/kai-voice.mp3'
    }
  }
  };
}

// Character database - loaded from file with cache invalidation
let CHARACTER_DATABASE = loadCharactersFromFile();
let lastFileModTime = 0;

/**
 * Check if characters file has been modified and reload if necessary
 */
function checkAndReloadCharacters(): void {
  if (typeof window !== 'undefined') {
    // Client-side: skip file system checks
    return;
  }

  try {
    const stats = fs.statSync(CHARACTERS_FILE);
    const currentModTime = stats.mtime.getTime();

    if (currentModTime > lastFileModTime) {
      console.log('Characters file modified, reloading...');
      CHARACTER_DATABASE = loadCharactersFromFile();
      lastFileModTime = currentModTime;
    }
  } catch (error) {
    console.warn('Could not check characters file modification time:', error);
  }
}

/**
 * Get character data by ID (with automatic reload on file changes)
 */
export function getCharacterData(characterId: string): FullCharacterData | null {
  // Check for file changes and reload if necessary
  checkAndReloadCharacters();
  return CHARACTER_DATABASE[characterId] || null;
}

/**
 * Get all active characters (with automatic reload on file changes)
 */
export function getAllActiveCharacters(): FullCharacterData[] {
  // Check for file changes and reload if necessary
  checkAndReloadCharacters();
  return Object.values(CHARACTER_DATABASE)
    .filter(char => char.isActive)
    .sort((a, b) => a.sortOrder - b.sortOrder);
}

/**
 * Get character metadata only (without media paths for security)
 */
export function getCharacterMetadata(characterId: string): CharacterMetadata | null {
  // Check for file changes and reload if necessary
  checkAndReloadCharacters();
  const character = CHARACTER_DATABASE[characterId];
  if (!character) return null;

  const { media, ...metadata } = character;
  return metadata;
}

/**
 * Get character media paths (server-side only)
 */
export function getCharacterMedia(characterId: string): CharacterMedia | null {
  // Check for file changes and reload if necessary
  checkAndReloadCharacters();
  const character = CHARACTER_DATABASE[characterId];
  return character?.media || null;
}

/**
 * Add new character (admin function)
 */
export function addCharacter(characterData: FullCharacterData): boolean {
  try {
    CHARACTER_DATABASE[characterData.id] = {
      ...characterData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Save to file
    const saved = saveCharactersToFile(CHARACTER_DATABASE);
    if (!saved) {
      console.warn('Character added to memory but failed to save to file');
    } else {
      // Update file modification time tracking
      try {
        const stats = fs.statSync(CHARACTERS_FILE);
        lastFileModTime = stats.mtime.getTime();
      } catch (error) {
        console.warn('Could not update file modification time:', error);
      }
    }

    return true;
  } catch (error) {
    console.error('Error adding character:', error);
    return false;
  }
}

/**
 * Update character (admin function)
 */
export function updateCharacter(characterId: string, updates: Partial<FullCharacterData>): boolean {
  try {
    if (!CHARACTER_DATABASE[characterId]) return false;

    CHARACTER_DATABASE[characterId] = {
      ...CHARACTER_DATABASE[characterId],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    // Save to file
    const saved = saveCharactersToFile(CHARACTER_DATABASE);
    if (!saved) {
      console.warn('Character updated in memory but failed to save to file');
    } else {
      // Update file modification time tracking
      try {
        const stats = fs.statSync(CHARACTERS_FILE);
        lastFileModTime = stats.mtime.getTime();
      } catch (error) {
        console.warn('Could not update file modification time:', error);
      }
    }

    return true;
  } catch (error) {
    console.error('Error updating character:', error);
    return false;
  }
}

/**
 * Delete character (admin function)
 */
export function deleteCharacter(characterId: string): boolean {
  try {
    if (!CHARACTER_DATABASE[characterId]) return false;

    delete CHARACTER_DATABASE[characterId];

    // Save to file
    const saved = saveCharactersToFile(CHARACTER_DATABASE);
    if (!saved) {
      console.warn('Character deleted from memory but failed to save to file');
    } else {
      // Update file modification time tracking
      try {
        const stats = fs.statSync(CHARACTERS_FILE);
        lastFileModTime = stats.mtime.getTime();
      } catch (error) {
        console.warn('Could not update file modification time:', error);
      }
    }

    return true;
  } catch (error) {
    console.error('Error deleting character:', error);
    return false;
  }
}

/**
 * Reload characters from file (admin function)
 */
export function reloadCharacters(): boolean {
  try {
    CHARACTER_DATABASE = loadCharactersFromFile();
    // Update file modification time tracking
    try {
      const stats = fs.statSync(CHARACTERS_FILE);
      lastFileModTime = stats.mtime.getTime();
    } catch (error) {
      console.warn('Could not update file modification time:', error);
    }
    return true;
  } catch (error) {
    console.error('Error reloading characters:', error);
    return false;
  }
}

/**
 * Force reload characters from file (bypasses cache)
 */
export function forceReloadCharacters(): boolean {
  try {
    console.log('Force reloading characters from file...');
    CHARACTER_DATABASE = loadCharactersFromFile();
    // Update file modification time tracking
    try {
      const stats = fs.statSync(CHARACTERS_FILE);
      lastFileModTime = stats.mtime.getTime();
      console.log('Characters reloaded successfully, new mod time:', lastFileModTime);
    } catch (error) {
      console.warn('Could not update file modification time:', error);
    }
    return true;
  } catch (error) {
    console.error('Error force reloading characters:', error);
    return false;
  }
}
