// Advanced voice caching system for ultra-fast audio responses

interface CacheEntry {
  audioData: string; // base64 audio data
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  textLength: number;
  characterId: string;
  language: 'ja' | 'en';
}

interface CacheStats {
  hits: number;
  misses: number;
  totalRequests: number;
  cacheSize: number;
  hitRate: number;
}

class VoiceCache {
  private cache = new Map<string, CacheEntry>();
  private readonly maxSize = 500; // Maximum cache entries
  private readonly ttl = 24 * 60 * 60 * 1000; // 24 hours TTL
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    totalRequests: 0,
    cacheSize: 0,
    hitRate: 0
  };

  // Generate cache key from text, character, and language (client-side safe)
  private generateKey(text: string, characterId: string, voiceId: string, language: 'ja' | 'en'): string {
    // Normalize text for better cache hits
    const normalizedText = text.trim().toLowerCase()
      .replace(/[.,!?;:]/g, '') // Remove punctuation
      .replace(/\s+/g, ' '); // Normalize whitespace

    const keyData = `${characterId}:${voiceId}:${language}:${normalizedText}`;

    // Simple hash function for client-side compatibility
    let hash = 0;
    for (let i = 0; i < keyData.length; i++) {
      const char = keyData.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  // Get cached audio
  get(text: string, characterId: string, voiceId: string, language: 'ja' | 'en'): string | null {
    this.stats.totalRequests++;
    
    const key = this.generateKey(text, characterId, voiceId, language);
    const entry = this.cache.get(key);

    if (!entry) {
      this.stats.misses++;
      this.updateStats();
      return null;
    }

    // Check TTL
    const now = Date.now();
    if (now - entry.timestamp > this.ttl) {
      this.cache.delete(key);
      this.stats.misses++;
      this.updateStats();
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = now;
    this.stats.hits++;
    this.updateStats();

    console.log(`=== VOICE CACHE HIT ===`);
    console.log(`Character: ${characterId}, Language: ${language}`);
    console.log(`Text: ${text.substring(0, 50)}...`);
    console.log(`Cache hit rate: ${this.stats.hitRate.toFixed(2)}%`);

    return entry.audioData;
  }

  // Store audio in cache
  set(text: string, characterId: string, voiceId: string, language: 'ja' | 'en', audioData: string): void {
    const key = this.generateKey(text, characterId, voiceId, language);
    const now = Date.now();

    // Check cache size and evict if necessary
    if (this.cache.size >= this.maxSize) {
      this.evictLeastUsed();
    }

    const entry: CacheEntry = {
      audioData,
      timestamp: now,
      accessCount: 1,
      lastAccessed: now,
      textLength: text.length,
      characterId,
      language
    };

    this.cache.set(key, entry);
    this.stats.cacheSize = this.cache.size;

    console.log(`=== VOICE CACHED ===`);
    console.log(`Character: ${characterId}, Language: ${language}`);
    console.log(`Text length: ${text.length}, Cache size: ${this.cache.size}`);
  }

  // Evict least recently used entries
  private evictLeastUsed(): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      console.log(`Evicted cache entry: ${oldestKey}`);
    }
  }

  // Update cache statistics
  private updateStats(): void {
    this.stats.cacheSize = this.cache.size;
    this.stats.hitRate = this.stats.totalRequests > 0 
      ? (this.stats.hits / this.stats.totalRequests) * 100 
      : 0;
  }

  // Get cache statistics
  getStats(): CacheStats {
    return { ...this.stats };
  }

  // Clear expired entries
  clearExpired(): void {
    const now = Date.now();
    let clearedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.ttl) {
        this.cache.delete(key);
        clearedCount++;
      }
    }

    this.stats.cacheSize = this.cache.size;
    
    if (clearedCount > 0) {
      console.log(`Cleared ${clearedCount} expired cache entries`);
    }
  }

  // Preload common phrases for characters
  async preloadCommonPhrases(characterId: string, voiceId: string, language: 'ja' | 'en'): Promise<void> {
    const commonPhrases = language === 'ja' ? [
      'こんにちは！',
      'はじめまして！',
      'ありがとうございます。',
      'そうですね。',
      'わかりました。',
      'いかがですか？',
      'お疲れさまです。',
      'よろしくお願いします。'
    ] : [
      'Hello!',
      'Nice to meet you!',
      'Thank you.',
      'I see.',
      'I understand.',
      'How are you?',
      'Great!',
      'You\'re welcome.'
    ];

    console.log(`=== Preloading Common Phrases for ${characterId} (${language}) ===`);

    for (const phrase of commonPhrases) {
      // Check if already cached
      if (!this.get(phrase, characterId, voiceId, language)) {
        try {
          const audioData = await this.generateVoiceForPreload(phrase, voiceId, language, characterId);
          if (audioData) {
            this.set(phrase, characterId, voiceId, language, audioData);
          }
        } catch (error) {
          console.warn(`Failed to preload phrase "${phrase}":`, error);
        }
        
        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    console.log(`Preloading completed for ${characterId}`);
  }

  // Generate voice for preloading (client-side safe - uses API)
  private async generateVoiceForPreload(text: string, voiceId: string, language: 'ja' | 'en', characterId?: string): Promise<string | null> {
    try {
      // Use internal API endpoint for voice generation
      const response = await fetch('/api/voice/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text,
          voiceId,
          language,
          characterId,
          preload: true // Flag to indicate this is for preloading
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.audioUrl) {
          return data.audioUrl;
        }
      }
    } catch (error) {
      console.warn('Preload generation error:', error);
    }

    return null;
  }

  // Clear all cache
  clear(): void {
    this.cache.clear();
    this.stats = {
      hits: 0,
      misses: 0,
      totalRequests: 0,
      cacheSize: 0,
      hitRate: 0
    };
    console.log('Voice cache cleared');
  }
}

// Global cache instance
export const voiceCache = new VoiceCache();

// Start cache maintenance
export function startCacheMaintenance(): void {
  // Clear expired entries every hour
  setInterval(() => {
    voiceCache.clearExpired();
  }, 60 * 60 * 1000);

  console.log('Voice cache maintenance started');
}
