"use client";

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { applyActionCode, verifyPasswordResetCode } from 'firebase/auth';
import { auth } from '@/lib/firebase';

export default function AuthActionClient() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');
  const [isJapanese, setIsJapanese] = useState(false);

  useEffect(() => {
    // Detect language from URL or browser
    const lang = searchParams.get('lang') || 'en';
    setIsJapanese(lang === 'ja');

    const handleAuthAction = async () => {
      if (!auth) {
        setStatus('error');
        setMessage(isJapanese ? 'Firebase が初期化されていません。' : 'Firebase not initialized.');
        return;
      }

      const mode = searchParams.get('mode');
      const actionCode = searchParams.get('oobCode');

      if (!mode || !actionCode) {
        setStatus('error');
        setMessage(isJapanese 
          ? 'このページは直接アクセスできません。メールに記載されたリンクからアクセスしてください。' 
          : 'This page cannot be accessed directly. Please use the link provided in your email.'
        );
        
        // Redirect to home after 5 seconds
        setTimeout(() => {
          router.push(isJapanese ? '/ja' : '/');
        }, 5000);
        
        return;
      }

      try {
        switch (mode) {
          case 'verifyEmail':
            await applyActionCode(auth, actionCode);

            // Force reload current user to update emailVerified status
            if (auth.currentUser) {
              await auth.currentUser.reload();
              console.log('User emailVerified status after reload:', auth.currentUser.emailVerified);
            }

            setStatus('success');
            setMessage(isJapanese
              ? 'メールアドレスの確認が完了しました！'
              : 'Email verification completed successfully!'
            );

            // Set session flag to prevent verification modal from showing
            if (auth.currentUser) {
              const sessionKey = `emailVerificationCompleted_${auth.currentUser.uid}`;
              sessionStorage.setItem(sessionKey, 'true');
            }

            // Redirect to home after 3 seconds
            setTimeout(() => {
              router.push(isJapanese ? '/ja' : '/');
            }, 3000);
            break;

          case 'resetPassword':
            // Verify the password reset code
            await verifyPasswordResetCode(auth, actionCode);
            setStatus('success');
            setMessage(isJapanese 
              ? 'パスワードリセットコードが確認されました。新しいパスワードを設定してください。' 
              : 'Password reset code verified. Please set your new password.'
            );
            
            // Redirect to password reset page with the code
            setTimeout(() => {
              router.push(`/reset-password?oobCode=${actionCode}&lang=${lang}`);
            }, 2000);
            break;

          case 'recoverEmail':
            await applyActionCode(auth, actionCode);
            setStatus('success');
            setMessage(isJapanese 
              ? 'メールアドレスの復旧が完了しました。' 
              : 'Email recovery completed successfully.'
            );
            
            setTimeout(() => {
              router.push(isJapanese ? '/ja' : '/');
            }, 3000);
            break;

          default:
            setStatus('error');
            setMessage(isJapanese ? 'サポートされていないアクションです。' : 'Unsupported action.');
        }
      } catch (error: any) {
        console.error('Auth action error:', error);
        setStatus('error');
        
        // Handle specific error codes
        switch (error.code) {
          case 'auth/expired-action-code':
            setMessage(isJapanese 
              ? 'このリンクは期限切れです。新しいリンクをリクエストしてください。' 
              : 'This link has expired. Please request a new one.'
            );
            break;
          case 'auth/invalid-action-code':
            setMessage(isJapanese 
              ? 'このリンクは無効です。' 
              : 'This link is invalid.'
            );
            break;
          case 'auth/user-disabled':
            setMessage(isJapanese 
              ? 'このアカウントは無効化されています。' 
              : 'This account has been disabled.'
            );
            break;
          case 'auth/user-not-found':
            setMessage(isJapanese 
              ? 'ユーザーが見つかりません。' 
              : 'User not found.'
            );
            break;
          default:
            setMessage(isJapanese 
              ? 'エラーが発生しました。もう一度お試しください。' 
              : 'An error occurred. Please try again.'
            );
        }
      }
    };

    handleAuthAction();
  }, [searchParams, router, isJapanese]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-md w-full p-8 text-center">
        {status === 'loading' && (
          <>
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="animate-spin w-8 h-8 text-blue-500" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-800 mb-4">
              {isJapanese ? '処理中...' : 'Processing...'}
            </h1>
            <p className="text-gray-600">
              {isJapanese 
                ? 'しばらくお待ちください。' 
                : 'Please wait a moment.'
              }
            </p>
          </>
        )}

        {status === 'success' && (
          <>
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-800 mb-4">
              {isJapanese ? '成功！' : 'Success!'}
            </h1>
            <p className="text-gray-600 mb-6">{message}</p>
            <p className="text-sm text-gray-500">
              {isJapanese 
                ? '自動的にホーム画面に移動します...' 
                : 'Redirecting to home page...'
              }
            </p>
          </>
        )}

        {status === 'error' && (
          <>
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-800 mb-4">
              {isJapanese ? 'エラー' : 'Error'}
            </h1>
            <p className="text-gray-600 mb-6">{message}</p>
            <div className="space-y-3">
              <button
                onClick={() => router.push(isJapanese ? '/ja' : '/')}
                className="w-full bg-blue-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-600 transition-colors"
              >
                {isJapanese ? 'ホームに戻る' : 'Go to Home'}
              </button>
              <button
                onClick={() => window.location.reload()}
                className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors"
              >
                {isJapanese ? '再試行' : 'Try Again'}
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
