"use client";

import { useEffect, useState } from 'react';
import { 
  initializeVoiceOptimization, 
  getOptimizationStatus, 
  logPerformanceMetrics,
  autoOptimize,
  healthCheck
} from '@/lib/voiceOptimization';

interface VoiceOptimizationProviderProps {
  children: React.ReactNode;
}

export function VoiceOptimizationProvider({ children }: VoiceOptimizationProviderProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [initializationError, setInitializationError] = useState<string | null>(null);

  useEffect(() => {
    // Initialize voice optimization systems
    const initializeOptimization = async () => {
      try {
        console.log('🚀 Starting voice optimization initialization...');
        
        // Initialize with production-optimized config
        initializeVoiceOptimization({
          enableWarmup: true,
          enableAdvancedCache: true,
          enablePreloading: true,
          warmupInterval: 30 * 60 * 1000, // 30 minutes
          cacheMaintenanceInterval: 60 * 60 * 1000 // 1 hour
        });

        // Wait a moment for initialization
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Verify initialization
        const status = getOptimizationStatus();
        if (status.initialized) {
          setIsInitialized(true);
          console.log('✅ Voice optimization initialized successfully');
          
          // Log initial status
          console.log('Initial optimization status:', status);
        } else {
          throw new Error('Initialization verification failed');
        }

      } catch (error) {
        console.error('❌ Failed to initialize voice optimization:', error);
        setInitializationError(error instanceof Error ? error.message : 'Unknown error');
      }
    };

    initializeOptimization();

    // Performance monitoring interval
    const performanceInterval = setInterval(() => {
      if (isInitialized) {
        logPerformanceMetrics();
      }
    }, 5 * 60 * 1000); // Every 5 minutes

    // Auto-optimization interval
    const autoOptimizeInterval = setInterval(() => {
      if (isInitialized) {
        autoOptimize();
      }
    }, 10 * 60 * 1000); // Every 10 minutes

    // Health check interval
    const healthCheckInterval = setInterval(() => {
      if (isInitialized) {
        const health = healthCheck();
        if (health.status !== 'healthy') {
          console.warn('Voice optimization health check:', health);
        }
      }
    }, 15 * 60 * 1000); // Every 15 minutes

    // Cleanup
    return () => {
      clearInterval(performanceInterval);
      clearInterval(autoOptimizeInterval);
      clearInterval(healthCheckInterval);
    };
  }, [isInitialized]);

  // Development mode: Show initialization status
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const logStatus = () => {
        if (isInitialized) {
          const status = getOptimizationStatus();
          console.log('🔧 Voice Optimization Status (Dev Mode):', {
            initialized: status.initialized,
            cacheStats: status.cacheStats,
            health: healthCheck()
          });
        }
      };

      const devInterval = setInterval(logStatus, 30000); // Every 30 seconds in dev
      return () => clearInterval(devInterval);
    }
  }, [isInitialized]);

  // Show initialization error in development
  if (process.env.NODE_ENV === 'development' && initializationError) {
    console.error('Voice Optimization Error:', initializationError);
  }

  return (
    <>
      {children}
      {/* Development mode indicator */}
      {process.env.NODE_ENV === 'development' && (
        <div 
          style={{
            position: 'fixed',
            bottom: '10px',
            right: '10px',
            background: isInitialized ? '#10b981' : '#ef4444',
            color: 'white',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            zIndex: 9999,
            fontFamily: 'monospace'
          }}
          title={initializationError || 'Voice optimization status'}
        >
          🎵 {isInitialized ? 'Optimized' : 'Initializing...'}
        </div>
      )}
    </>
  );
}
