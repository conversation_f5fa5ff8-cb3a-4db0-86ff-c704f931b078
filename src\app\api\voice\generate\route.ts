// Voice generation API endpoint (server-side only)
import { NextRequest, NextResponse } from 'next/server';
import { getSecret } from '@/lib/secretManager';

export const dynamic = 'force-dynamic';
// Note: Edge Runtime disabled for broader Node.js compatibility
// export const runtime = 'edge';
export const preferredRegion = 'auto';

interface VoiceGenerationRequest {
  text: string;
  voiceId: string;
  language: 'ja' | 'en';
  preload?: boolean;
  characterId?: string;
  provider?: 'elevenlabs' | 'minimax';
}

// POST: Generate voice audio
export async function POST(request: NextRequest) {
  try {
    const { text, voiceId, language, preload = false, characterId, provider }: VoiceGenerationRequest = await request.json();

    // Validate required fields
    if (!text || !voiceId) {
      return NextResponse.json(
        { success: false, error: 'Text and voiceId are required' },
        { status: 400 }
      );
    }

    console.log(`=== Voice Generation API ===`);
    console.log(`Text: ${text.substring(0, 50)}...`);
    console.log(`VoiceId: ${voiceId}`);
    console.log(`Language: ${language}`);
    console.log(`Preload: ${preload}`);
    console.log(`Provider: ${provider || 'auto-detect'}`);

    // Determine voice provider
    let detectedProvider = provider;
    if (!detectedProvider && characterId) {
      try {
        const { loadCharactersFromServer } = await import('@/lib/serverFileManager');
        const serverCharacters = loadCharactersFromServer();
        const character = serverCharacters[characterId];
        detectedProvider = character?.voiceProvider || 'elevenlabs';
      } catch (error) {
        console.warn('Failed to load character data for provider detection:', error);
        detectedProvider = 'elevenlabs'; // fallback
      }
    } else {
      detectedProvider = detectedProvider || 'elevenlabs'; // fallback
    }

    console.log(`Using voice provider: ${detectedProvider}`);

    // Route to appropriate provider
    if (detectedProvider === 'minimax') {
      return await handleMinimaxGeneration(text, voiceId, language, characterId);
    } else {
      return await handleElevenLabsGeneration(text, voiceId, language, preload);
    }

  } catch (error) {
    console.error('Voice generation API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle ElevenLabs voice generation
async function handleElevenLabsGeneration(text: string, voiceId: string, language: 'ja' | 'en', preload: boolean) {
  // Check API key
  const apiKey = await getSecret('ELEVENLABS_API_KEY');
  if (!apiKey) {
    return NextResponse.json(
      { success: false, error: 'ElevenLabs API key not configured' },
      { status: 500 }
    );
  }

  const startTime = Date.now();

  // Generate voice using ElevenLabs
  const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voiceId}/stream`, {
    method: 'POST',
    headers: {
      'Accept': 'audio/mpeg',
      'Content-Type': 'application/json',
      'xi-api-key': apiKey
    },
    body: JSON.stringify({
      text,
      model_id: 'eleven_turbo_v2_5',
      voice_settings: {
        stability: 0.3,
        similarity_boost: 0.3,
        style: 0.0,
        use_speaker_boost: false
      },
      optimize_streaming_latency: 4,
      output_format: "mp3_22050_32"
    })
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error('ElevenLabs API error:', response.status, errorText);
    return NextResponse.json(
      { success: false, error: `Voice generation failed: ${response.status}` },
      { status: 500 }
    );
  }

  // Convert to base64 data URL
  const audioBuffer = await response.arrayBuffer();
  const base64Audio = Buffer.from(audioBuffer).toString('base64');
  const audioDataUrl = `data:audio/mpeg;base64,${base64Audio}`;

  const generationTime = Date.now() - startTime;
  console.log(`ElevenLabs voice generation completed in ${generationTime}ms`);
  console.log(`Audio size: ${audioBuffer.byteLength} bytes`);

  return NextResponse.json({
    success: true,
    audioUrl: audioDataUrl,
    generationTime,
    audioSize: audioBuffer.byteLength,
    cached: false // This endpoint doesn't use cache
  });
}

// Handle MiniMax voice generation
async function handleMinimaxGeneration(text: string, voiceId: string, language: 'ja' | 'en', characterId?: string) {
  try {
    console.log(`Routing to MiniMax API for voice generation`);

    // Call MiniMax API directly to avoid relative URL issues
    const apiKey = await getSecret('MINIMAX_API_KEY');
    const groupId = await getSecret('MINIMAX_GROUP_ID');

    if (!apiKey || !groupId) {
      throw new Error('MiniMax API credentials not configured');
    }

    const apiUrl = `https://api.minimaxi.chat/v1/t2a_v2?GroupId=${groupId}`;
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        text,
        voice_setting: {
          voice_id: voiceId,
          speed: 1.0,
          vol: 1.0,
          pitch: 0,
          audio_sample_rate: 22050,
          bitrate: 128000
        },
        model: 'speech-01-turbo',
        audio_setting: {
          sample_rate: 22050,
          bitrate: 128000,
          format: 'mp3'
        },
        stream: true
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('MiniMax API error:', response.status, errorText);
      return NextResponse.json(
        { success: false, error: `MiniMax voice generation failed: ${response.status}` },
        { status: 500 }
      );
    }

    // Handle streaming response
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response body from MiniMax API');
    }

    const decoder = new TextDecoder();
    const audioChunks: Uint8Array[] = [];
    let totalLength = 0;
    let buffer = '';

    // Helper function to convert hex to bytes
    function hexToBytes(hex: string): Uint8Array {
      const bytes = new Uint8Array(hex.length / 2);
      for (let i = 0; i < hex.length; i += 2) {
        bytes[i / 2] = parseInt(hex.substring(i, i + 2), 16);
      }
      return bytes;
    }

    while (true) {
      const { done, value } = await reader.read();

      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith('data:')) {
          try {
            const jsonStr = line.substring(5).trim();
            if (jsonStr) {
              const data = JSON.parse(jsonStr);

              if (data.data && data.data.audio && !data.extra_info) {
                const hexAudio = data.data.audio;
                const audioBytes = hexToBytes(hexAudio);
                audioChunks.push(audioBytes);
                totalLength += audioBytes.length;
              }
            }
          } catch (parseError) {
            console.error('Error parsing MiniMax chunk:', parseError);
          }
        }
      }
    }

    if (audioChunks.length === 0) {
      throw new Error('No audio chunks received from MiniMax');
    }

    // Combine all chunks
    const combinedAudio = new Uint8Array(totalLength);
    let offset = 0;
    for (const chunk of audioChunks) {
      combinedAudio.set(chunk, offset);
      offset += chunk.length;
    }

    const finalBase64 = Buffer.from(combinedAudio).toString('base64');
    const audioUrl = `data:audio/mpeg;base64,${finalBase64}`;

    return NextResponse.json({
      success: true,
      audioUrl: audioUrl,
      generationTime: 0, // MiniMax handles timing internally
      audioSize: 0, // Size not available in this flow
      cached: false
    });

  } catch (error) {
    console.error('MiniMax voice generation failed:', error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'MiniMax generation failed' },
      { status: 500 }
    );
  }

}

// GET: Health check for voice generation
export async function GET() {
  try {
    const elevenLabsApiKey = await getSecret('ELEVENLABS_API_KEY');
    const minimaxApiKey = await getSecret('MINIMAX_API_KEY');

    return NextResponse.json({
      success: true,
      status: 'Voice generation API is running',
      providers: {
        elevenlabs: !!elevenLabsApiKey,
        minimax: !!minimaxApiKey
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
