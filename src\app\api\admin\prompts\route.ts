import { NextRequest, NextResponse } from 'next/server';
import { CharacterService } from '@/lib/characterService';

// Admin token validation
function validateAdminToken(request: NextRequest): boolean {
  const adminToken = request.headers.get('x-admin-token');
  const expectedToken = process.env.NEXT_PUBLIC_ADMIN_TOKEN || 'your-secure-admin-token-here-change-this';
  return adminToken === expectedToken;
}

// GET: Load character system prompts
export async function GET(request: NextRequest) {
  try {
    // Validate admin token
    if (!validateAdminToken(request)) {
      return NextResponse.json(
        { error: 'Unauthorized: Invalid admin token' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const characterId = searchParams.get('characterId');

    if (!characterId) {
      return NextResponse.json(
        { error: 'Character ID is required' },
        { status: 400 }
      );
    }

    console.log(`Loading prompts for character: ${characterId}`);

    // Load character from unified service
    const character = CharacterService.getCharacter(characterId);

    if (!character || !character.systemPrompt) {
      return NextResponse.json({
        success: true,
        characterPrompts: { ja: '', en: '' },
        characterPrompt: '' // Legacy compatibility
      });
    }

    // Return prompts from character data
    return NextResponse.json({
      success: true,
      characterPrompts: character.systemPrompt,
      characterPrompt: character.systemPrompt.en || character.systemPrompt.ja || '' // Legacy compatibility
    });
  } catch (error) {
    console.error('Error loading character prompts:', error);
    return NextResponse.json(
      { error: 'Failed to load character prompts' },
      { status: 500 }
    );
  }
}

// POST: Save character system prompts
export async function POST(request: NextRequest) {
  try {
    // Validate admin token
    if (!validateAdminToken(request)) {
      return NextResponse.json(
        { error: 'Unauthorized: Invalid admin token' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { characterId, prompts } = body;

    if (!characterId) {
      return NextResponse.json(
        { error: 'Character ID is required' },
        { status: 400 }
      );
    }

    if (!prompts) {
      return NextResponse.json(
        { error: 'Prompts data is required' },
        { status: 400 }
      );
    }

    console.log(`Saving prompts for character: ${characterId}`);

    // Load existing character
    const character = CharacterService.getCharacter(characterId);

    if (!character) {
      return NextResponse.json(
        { error: 'Character not found' },
        { status: 404 }
      );
    }

    // Handle different prompt formats
    let systemPrompt;

    if (typeof prompts === 'string') {
      // Legacy single prompt - use for both languages
      systemPrompt = { en: prompts, ja: prompts };
    } else if (typeof prompts === 'object' && (prompts.ja || prompts.en)) {
      // Multi-language prompts
      systemPrompt = prompts;
    } else {
      return NextResponse.json(
        { error: 'Invalid prompts format' },
        { status: 400 }
      );
    }

    // Update character with new prompts
    const updatedCharacter = {
      ...character,
      systemPrompt,
      updatedAt: new Date().toISOString()
    };

    const success = CharacterService.saveCharacter(updatedCharacter);

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Character prompts saved successfully'
      });
    } else {
      return NextResponse.json(
        { error: 'Failed to save character prompts' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error saving character prompts:', error);
    return NextResponse.json(
      { error: 'Failed to save character prompts' },
      { status: 500 }
    );
  }
}

// PUT: Update character system prompts (alias for POST)
export async function PUT(request: NextRequest) {
  return POST(request);
}
