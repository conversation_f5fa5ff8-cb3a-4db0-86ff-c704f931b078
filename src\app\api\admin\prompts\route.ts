import { NextRequest, NextResponse } from 'next/server';
import { loadPromptFromServer, savePromptToServer } from '@/lib/serverFileManager';

// Admin token validation
function validateAdminToken(request: NextRequest): boolean {
  const adminToken = request.headers.get('x-admin-token');
  const expectedToken = process.env.NEXT_PUBLIC_ADMIN_TOKEN || 'your-secure-admin-token-here-change-this';
  return adminToken === expectedToken;
}

// GET: Load character system prompts
export async function GET(request: NextRequest) {
  try {
    // Validate admin token
    if (!validateAdminToken(request)) {
      return NextResponse.json(
        { error: 'Unauthorized: Invalid admin token' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const characterId = searchParams.get('characterId');

    if (!characterId) {
      return NextResponse.json(
        { error: 'Character ID is required' },
        { status: 400 }
      );
    }

    console.log(`Loading prompts for character: ${characterId}`);

    // Load prompts from server files
    const prompts = loadPromptFromServer(characterId);

    if (typeof prompts === 'string') {
      // Legacy single prompt - return as both languages
      return NextResponse.json({
        success: true,
        characterPrompts: {
          ja: prompts,
          en: prompts
        },
        characterPrompt: prompts // Legacy compatibility
      });
    } else {
      // Multi-language prompts
      return NextResponse.json({
        success: true,
        characterPrompts: prompts,
        characterPrompt: prompts.en || prompts.ja || '' // Legacy compatibility
      });
    }
  } catch (error) {
    console.error('Error loading character prompts:', error);
    return NextResponse.json(
      { error: 'Failed to load character prompts' },
      { status: 500 }
    );
  }
}

// POST: Save character system prompts
export async function POST(request: NextRequest) {
  try {
    // Validate admin token
    if (!validateAdminToken(request)) {
      return NextResponse.json(
        { error: 'Unauthorized: Invalid admin token' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { characterId, prompts } = body;

    if (!characterId) {
      return NextResponse.json(
        { error: 'Character ID is required' },
        { status: 400 }
      );
    }

    if (!prompts) {
      return NextResponse.json(
        { error: 'Prompts data is required' },
        { status: 400 }
      );
    }

    console.log(`Saving prompts for character: ${characterId}`);

    // Handle different prompt formats
    let promptsToSave;
    
    if (typeof prompts === 'string') {
      // Legacy single prompt
      promptsToSave = prompts;
    } else if (typeof prompts === 'object' && (prompts.ja || prompts.en)) {
      // Multi-language prompts
      promptsToSave = prompts;
    } else {
      return NextResponse.json(
        { error: 'Invalid prompts format' },
        { status: 400 }
      );
    }

    // Save prompts to server files
    const success = savePromptToServer(characterId, promptsToSave);

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Character prompts saved successfully'
      });
    } else {
      return NextResponse.json(
        { error: 'Failed to save character prompts' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error saving character prompts:', error);
    return NextResponse.json(
      { error: 'Failed to save character prompts' },
      { status: 500 }
    );
  }
}

// PUT: Update character system prompts (alias for POST)
export async function PUT(request: NextRequest) {
  return POST(request);
}
