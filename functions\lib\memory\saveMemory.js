"use strict";
/**
 * 有料ユーザー限定 - 長期記憶保存Cloud Function
 * Firestoreの会話保存をトリガーに、ベクトル化してSupabaseに保存
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.saveMemoryOnMessage = void 0;
const firestore_1 = require("firebase-functions/v2/firestore");
const firebase_functions_1 = require("firebase-functions");
const admin = __importStar(require("firebase-admin"));
const supabase_js_1 = require("@supabase/supabase-js");
// Firebase Admin（既に初期化済みを使用）
const db = admin.firestore();
// Supabase初期化（環境変数チェック付き）
const supabaseUrl = process.env.SUPABASE_PROJECT_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
let supabase = null;
if (supabaseUrl && supabaseKey) {
    supabase = (0, supabase_js_1.createClient)(supabaseUrl, supabaseKey);
}
else {
    firebase_functions_1.logger.warn('Supabase credentials not configured. Memory system will be disabled.');
}
// Google AI API用の設定
const GOOGLE_AI_API_KEY = process.env.GOOGLE_AI_API_KEY;
/**
 * 課金状態確認
 */
async function checkUserSubscription(userId) {
    try {
        const userDoc = await db.collection('users').doc(userId).get();
        if (!userDoc.exists) {
            firebase_functions_1.logger.warn(`User document not found: ${userId}`);
            return false;
        }
        const userData = userDoc.data();
        const subscriptionTier = userData.subscriptionTier || 'free';
        // "free"以外は有料ユーザー
        const isPaidUser = subscriptionTier !== 'free';
        firebase_functions_1.logger.info(`User ${userId} subscription check:`, {
            tier: subscriptionTier,
            isPaid: isPaidUser
        });
        return isPaidUser;
    }
    catch (error) {
        firebase_functions_1.logger.error('Error checking user subscription:', error);
        return false;
    }
}
/**
 * テキストをベクトル化（Google AI API使用）
 */
async function generateEmbedding(text) {
    var _a;
    try {
        if (!GOOGLE_AI_API_KEY) {
            throw new Error('Google AI API key not configured');
        }
        const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:embedContent', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-goog-api-key': GOOGLE_AI_API_KEY
            },
            body: JSON.stringify({
                content: {
                    parts: [{ text: text }]
                }
            })
        });
        if (!response.ok) {
            throw new Error(`Embedding API error: ${response.status}`);
        }
        const data = await response.json();
        if (!((_a = data.embedding) === null || _a === void 0 ? void 0 : _a.values)) {
            throw new Error('No embedding values returned');
        }
        return data.embedding.values;
    }
    catch (error) {
        firebase_functions_1.logger.error('Error generating embedding:', error);
        throw error;
    }
}
/**
 * 記憶の重要度スコア計算
 */
function calculateImportanceScore(message) {
    let score = 0.5; // ベーススコア
    const content = message.content.toLowerCase();
    // 感情的な内容
    const emotionalKeywords = ['好き', 'love', '嫌い', 'hate', '悲しい', 'sad', '嬉しい', 'happy', '怒り', 'angry'];
    if (emotionalKeywords.some(keyword => content.includes(keyword))) {
        score += 0.2;
    }
    // 個人情報
    const personalKeywords = ['名前', 'name', '年齢', 'age', '仕事', 'job', '家族', 'family', '趣味', 'hobby'];
    if (personalKeywords.some(keyword => content.includes(keyword))) {
        score += 0.3;
    }
    // 長いメッセージ（詳細な情報）
    if (content.length > 100) {
        score += 0.1;
    }
    // AIからの重要な応答
    if (message.sender === 'ai' && content.length > 50) {
        score += 0.1;
    }
    return Math.min(score, 1.0);
}
/**
 * Supabaseに記憶を保存
 */
async function saveToSupabase(userId, characterId, content, embedding, importance, metadata) {
    try {
        if (!supabase) {
            firebase_functions_1.logger.warn('Supabase not configured, skipping memory save');
            return null;
        }
        const { data, error } = await supabase
            .from('long_term_memories')
            .insert({
            user_id: userId,
            character_id: characterId,
            content: content,
            embedding: embedding,
            importance_score: importance,
            emotion_context: (metadata === null || metadata === void 0 ? void 0 : metadata.emotion) || null,
            conversation_context: (metadata === null || metadata === void 0 ? void 0 : metadata.context) || null,
            tags: (metadata === null || metadata === void 0 ? void 0 : metadata.tags) || []
        });
        if (error) {
            throw error;
        }
        firebase_functions_1.logger.info('Memory saved to Supabase successfully:', {
            userId,
            characterId,
            importance,
            contentLength: content.length
        });
        return data;
    }
    catch (error) {
        firebase_functions_1.logger.error('Error saving to Supabase:', error);
        throw error;
    }
}
/**
 * メイン処理：メッセージ保存トリガー
 */
exports.saveMemoryOnMessage = (0, firestore_1.onDocumentCreated)('messages/{messageId}', async (event) => {
    var _a;
    const messageData = (_a = event.data) === null || _a === void 0 ? void 0 : _a.data();
    if (!messageData) {
        firebase_functions_1.logger.error('No message data found');
        return;
    }
    const { userId, characterId, content, sender } = messageData;
    firebase_functions_1.logger.info('Memory save triggered:', {
        userId,
        characterId,
        sender,
        contentLength: (content === null || content === void 0 ? void 0 : content.length) || 0
    });
    try {
        // 1. 課金状態確認（最重要）
        const isPaidUser = await checkUserSubscription(userId);
        if (!isPaidUser) {
            firebase_functions_1.logger.info(`Skipping memory save for free user: ${userId}`);
            return; // 無料ユーザーは処理終了
        }
        // 2. 有料ユーザーのみ以下の処理を実行
        firebase_functions_1.logger.info(`Processing memory for paid user: ${userId}`);
        // 3. 内容の妥当性チェック
        if (!content || content.trim().length < 10) {
            firebase_functions_1.logger.info('Content too short, skipping memory save');
            return;
        }
        // 4. ベクトル化
        const embedding = await generateEmbedding(content);
        // 5. 重要度スコア計算
        const importance = calculateImportanceScore(messageData);
        // 6. Supabaseに保存
        await saveToSupabase(userId, characterId, content, embedding, importance, messageData.metadata);
        firebase_functions_1.logger.info('Memory processing completed successfully');
    }
    catch (error) {
        firebase_functions_1.logger.error('Error in memory save process:', error);
        // エラーでも処理は継続（メッセージ保存は成功している）
    }
});
//# sourceMappingURL=saveMemory.js.map