"use strict";
/**
 * 有料ユーザー限定 - 長期記憶保存Cloud Function
 * Firestoreの会話保存をトリガーに、ベクトル化してSupabaseに保存
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.saveMemoryOnMessage = void 0;
const firestore_1 = require("firebase-functions/v2/firestore");
const firebase_functions_1 = require("firebase-functions");
const app_1 = require("firebase-admin/app");
const firestore_2 = require("firebase-admin/firestore");
const supabase_js_1 = require("@supabase/supabase-js");
const vertexai_1 = require("@google-cloud/vertexai");
// Firebase Admin初期化
if (!getApps().length) {
    (0, app_1.initializeApp)();
}
const db = (0, firestore_2.getFirestore)();
// Supabase初期化
const supabaseUrl = process.env.SUPABASE_PROJECT_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = (0, supabase_js_1.createClient)(supabaseUrl, supabaseKey);
// Vertex AI初期化
const vertexAI = new vertexai_1.VertexAI({
    project: process.env.GOOGLE_CLOUD_PROJECT,
    location: 'us-central1'
});
/**
 * 課金状態確認
 */
async function checkUserSubscription(userId) {
    try {
        const userDoc = await db.collection('users').doc(userId).get();
        if (!userDoc.exists) {
            firebase_functions_1.logger.warn(`User document not found: ${userId}`);
            return false;
        }
        const userData = userDoc.data();
        const subscriptionTier = userData.subscriptionTier || 'free';
        // "free"以外は有料ユーザー
        const isPaidUser = subscriptionTier !== 'free';
        firebase_functions_1.logger.info(`User ${userId} subscription check:`, {
            tier: subscriptionTier,
            isPaid: isPaidUser
        });
        return isPaidUser;
    }
    catch (error) {
        firebase_functions_1.logger.error('Error checking user subscription:', error);
        return false;
    }
}
/**
 * テキストをベクトル化
 */
async function generateEmbedding(text) {
    var _a;
    try {
        const model = vertexAI.getGenerativeModel({
            model: 'text-multilingual-embedding-002'
        });
        const result = await model.embedContent(text);
        if (!((_a = result.embedding) === null || _a === void 0 ? void 0 : _a.values)) {
            throw new Error('No embedding values returned');
        }
        return result.embedding.values;
    }
    catch (error) {
        firebase_functions_1.logger.error('Error generating embedding:', error);
        throw error;
    }
}
/**
 * 記憶の重要度スコア計算
 */
function calculateImportanceScore(message) {
    let score = 0.5; // ベーススコア
    const content = message.content.toLowerCase();
    // 感情的な内容
    const emotionalKeywords = ['好き', 'love', '嫌い', 'hate', '悲しい', 'sad', '嬉しい', 'happy', '怒り', 'angry'];
    if (emotionalKeywords.some(keyword => content.includes(keyword))) {
        score += 0.2;
    }
    // 個人情報
    const personalKeywords = ['名前', 'name', '年齢', 'age', '仕事', 'job', '家族', 'family', '趣味', 'hobby'];
    if (personalKeywords.some(keyword => content.includes(keyword))) {
        score += 0.3;
    }
    // 長いメッセージ（詳細な情報）
    if (content.length > 100) {
        score += 0.1;
    }
    // AIからの重要な応答
    if (message.sender === 'ai' && content.length > 50) {
        score += 0.1;
    }
    return Math.min(score, 1.0);
}
/**
 * Supabaseに記憶を保存
 */
async function saveToSupabase(userId, characterId, content, embedding, importance, metadata) {
    try {
        const { data, error } = await supabase
            .from('long_term_memories')
            .insert({
            user_id: userId,
            character_id: characterId,
            content: content,
            embedding: embedding,
            importance_score: importance,
            emotion_context: (metadata === null || metadata === void 0 ? void 0 : metadata.emotion) || null,
            conversation_context: (metadata === null || metadata === void 0 ? void 0 : metadata.context) || null,
            tags: (metadata === null || metadata === void 0 ? void 0 : metadata.tags) || []
        });
        if (error) {
            throw error;
        }
        firebase_functions_1.logger.info('Memory saved to Supabase successfully:', {
            userId,
            characterId,
            importance,
            contentLength: content.length
        });
        return data;
    }
    catch (error) {
        firebase_functions_1.logger.error('Error saving to Supabase:', error);
        throw error;
    }
}
/**
 * メイン処理：メッセージ保存トリガー
 */
exports.saveMemoryOnMessage = (0, firestore_1.onDocumentCreated)('messages/{messageId}', async (event) => {
    var _a;
    const messageData = (_a = event.data) === null || _a === void 0 ? void 0 : _a.data();
    if (!messageData) {
        firebase_functions_1.logger.error('No message data found');
        return;
    }
    const { userId, characterId, content, sender } = messageData;
    firebase_functions_1.logger.info('Memory save triggered:', {
        userId,
        characterId,
        sender,
        contentLength: (content === null || content === void 0 ? void 0 : content.length) || 0
    });
    try {
        // 1. 課金状態確認（最重要）
        const isPaidUser = await checkUserSubscription(userId);
        if (!isPaidUser) {
            firebase_functions_1.logger.info(`Skipping memory save for free user: ${userId}`);
            return; // 無料ユーザーは処理終了
        }
        // 2. 有料ユーザーのみ以下の処理を実行
        firebase_functions_1.logger.info(`Processing memory for paid user: ${userId}`);
        // 3. 内容の妥当性チェック
        if (!content || content.trim().length < 10) {
            firebase_functions_1.logger.info('Content too short, skipping memory save');
            return;
        }
        // 4. ベクトル化
        const embedding = await generateEmbedding(content);
        // 5. 重要度スコア計算
        const importance = calculateImportanceScore(messageData);
        // 6. Supabaseに保存
        await saveToSupabase(userId, characterId, content, embedding, importance, messageData.metadata);
        firebase_functions_1.logger.info('Memory processing completed successfully');
    }
    catch (error) {
        firebase_functions_1.logger.error('Error in memory save process:', error);
        // エラーでも処理は継続（メッセージ保存は成功している）
    }
});
//# sourceMappingURL=saveMemory.js.map