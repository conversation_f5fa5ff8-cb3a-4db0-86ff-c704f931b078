import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

const GLOBAL_PROMPTS_FILE = path.join(process.cwd(), 'data', 'global-prompts.json');

// Admin authentication check
function isAdmin(request: NextRequest): boolean {
  const adminToken = request.headers.get('x-admin-token');
  const expectedToken = process.env.NEXT_PUBLIC_ADMIN_TOKEN || 'your-secure-admin-token-here-change-this';
  return adminToken === expectedToken;
}

interface GlobalPrompts {
  ja: string;
  en: string;
}

// Default global prompts
const DEFAULT_GLOBAL_PROMPTS: GlobalPrompts = {
  ja: `あなたは親しみやすく、丁寧で思いやりのあるAIアシスタントです。常にユーザーを尊重し、建設的で有益な会話を心がけてください。不適切な内容や有害な要求には応じず、代わりに建設的な代替案を提案してください。

以下のガイドラインに従ってください：
- 常に礼儀正しく、親切に対応する
- ユーザーの感情に共感し、理解を示す
- 建設的で有益な情報を提供する
- 不確実な情報については正直に伝える
- プライバシーと安全性を尊重する`,

  en: `You are a friendly, polite, and caring AI assistant. Always respect users and strive for constructive, helpful conversations. Do not respond to inappropriate content or harmful requests, and instead suggest constructive alternatives.

Please follow these guidelines:
- Always be courteous and kind in your responses
- Show empathy and understanding for user emotions
- Provide constructive and helpful information
- Be honest about uncertain information
- Respect privacy and safety`
};

// Load global prompts from file
async function loadGlobalPrompts(): Promise<GlobalPrompts> {
  try {
    // Ensure data directory exists
    const dataDir = path.dirname(GLOBAL_PROMPTS_FILE);
    await fs.mkdir(dataDir, { recursive: true });

    const data = await fs.readFile(GLOBAL_PROMPTS_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.log('Global prompts file not found, using defaults');
    return DEFAULT_GLOBAL_PROMPTS;
  }
}

// Save global prompts to file
async function saveGlobalPrompts(prompts: GlobalPrompts): Promise<boolean> {
  try {
    // Ensure data directory exists
    const dataDir = path.dirname(GLOBAL_PROMPTS_FILE);
    await fs.mkdir(dataDir, { recursive: true });

    await fs.writeFile(GLOBAL_PROMPTS_FILE, JSON.stringify(prompts, null, 2), 'utf-8');
    return true;
  } catch (error) {
    console.error('Error saving global prompts:', error);
    return false;
  }
}

// GET - Load global prompts
export async function GET(request: NextRequest) {
  if (!isAdmin(request)) {
    return NextResponse.json(
      { success: false, error: 'Unauthorized' },
      { status: 401 }
    );
  }

  try {
    const prompts = await loadGlobalPrompts();
    return NextResponse.json({
      success: true,
      prompts
    });
  } catch (error) {
    console.error('Error loading global prompts:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to load global prompts' },
      { status: 500 }
    );
  }
}

// POST - Save global prompts
export async function POST(request: NextRequest) {
  if (!isAdmin(request)) {
    return NextResponse.json(
      { success: false, error: 'Unauthorized' },
      { status: 401 }
    );
  }

  try {
    const { prompts }: { prompts: GlobalPrompts } = await request.json();

    // Validate prompts
    if (!prompts || typeof prompts.ja !== 'string' || typeof prompts.en !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Invalid prompts format' },
        { status: 400 }
      );
    }

    const saved = await saveGlobalPrompts(prompts);
    if (!saved) {
      return NextResponse.json(
        { success: false, error: 'Failed to save global prompts' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Global prompts saved successfully'
    });
  } catch (error) {
    console.error('Error saving global prompts:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to save global prompts' },
      { status: 500 }
    );
  }
}

// Export function to get global prompts for use in other parts of the app
export async function getGlobalPrompts(): Promise<GlobalPrompts> {
  return await loadGlobalPrompts();
}
