'use client';

import { useEffect } from 'react';
import '@/lib/firebase';

export default function FirebaseInit() {
  useEffect(() => {
    console.log('🔥 FirebaseInit component mounted - forcing Firebase initialization');
    
    // Check if Firebase is properly initialized
    const checkFirebase = async () => {
      try {
        const { auth, db, storage } = await import('@/lib/firebase');
        console.log('🔥 Firebase services check:', {
          auth: !!auth,
          db: !!db,
          storage: !!storage
        });
      } catch (error) {
        console.error('❌ Firebase initialization error:', error);
      }
    };
    
    checkFirebase();
  }, []);

  return null; // This component doesn't render anything
}
