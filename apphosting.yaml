# Firebase App Hosting Configuration
runConfig:
  # Cost-optimized Cloud Run configuration
  minInstances: 0      # No idle cost - scale to zero when not in use
  maxInstances: 100    # Budget-conscious upper limit for scaling
  concurrency: 80      # Optimized for chat application characteristics
  cpu: 1              # Standard CPU allocation for AI chat processing
  memoryMiB: 512      # Minimum allowed memory for Firebase App Hosting
  
  # Environment variables configuration
  # All sensitive data stored in Google Cloud Secret Manager for security
  env:
    # ElevenLabs API for voice generation
    - variable: ELEVENLABS_API_KEY
      secret: ELEVENLABS_API_KEY
    
    # OpenRouter API for AI responses
    - variable: OPENROUTER_API_KEY
      secret: OPENROUTER_API_KEY
    
    # Venice AI API (alternative provider)
    - variable: VENICE_API_KEY
      secret: VENICE_API_KEY

    # MiniMax API for voice generation
    - variable: MINIMAX_API_KEY
      secret: MINIMAX_API_KEY
    - variable: MINIMAX_GROUP_ID
      secret: MINIMAX_GROUP_ID

    # Brevo SMTP configuration for contact form
    - variable: SMTP_HOST
      secret: SMTP_HOST
    - variable: SMTP_PORT
      secret: SMTP_PORT
    - variable: SMTP_USER
      secret: SMTP_USER
    - variable: SMTP_PASSWORD
      secret: SMTP_PASSWORD
    
    # Firebase configuration (automatically provided)
    - variable: FIREBASE_CONFIG
      availability: [BUILD, RUNTIME]
    
    # Next.js public environment variables
    - variable: NEXT_PUBLIC_SITE_URL
      value: "https://ailuvchat.com"

# Build configuration for Next.js application
buildConfig:
  runtime: nodejs20        # Latest stable Node.js runtime
  commands:
    - npm ci              # Clean install for reproducible builds
    - npm run build       # Next.js production build
