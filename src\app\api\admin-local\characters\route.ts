import { NextRequest, NextResponse } from 'next/server';
import { CharacterService } from '@/lib/characterService';
import { FullCharacterData } from '@/types/firebase';

// GET - Load all characters (local development only)
export async function GET() {
  try {
    // Only allow in development mode
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { error: 'Admin API only available in development mode' },
        { status: 403 }
      );
    }

    const charactersArray = CharacterService.getAllActiveCharacters();

    return NextResponse.json({
      success: true,
      characters: charactersArray,
      count: charactersArray.length
    });
  } catch (error) {
    console.error('Error loading characters:', error);
    return NextResponse.json(
      { error: 'Failed to load characters' },
      { status: 500 }
    );
  }
}

// POST - Create new character (local development only)
export async function POST(request: NextRequest) {
  try {
    // Only allow in development mode
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { error: 'Admin API only available in development mode' },
        { status: 403 }
      );
    }

    const characterData = await request.json();

    // Validate required fields
    if (!characterData.id || !characterData.name) {
      return NextResponse.json(
        { error: 'Character ID and name are required' },
        { status: 400 }
      );
    }

    // Add timestamps
    const newCharacter: FullCharacterData = {
      ...characterData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true
    };

    const success = CharacterService.saveCharacter(newCharacter);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to save character' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      character: newCharacter,
      message: 'Character created successfully'
    });
  } catch (error) {
    console.error('Error creating character:', error);
    return NextResponse.json(
      { error: 'Failed to create character' },
      { status: 500 }
    );
  }
}

// PUT - Update character (local development only)
export async function PUT(request: NextRequest) {
  try {
    // Only allow in development mode
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { error: 'Admin API only available in development mode' },
        { status: 403 }
      );
    }

    const updateData = await request.json();

    if (!updateData.id) {
      return NextResponse.json(
        { error: 'Character ID is required' },
        { status: 400 }
      );
    }

    // Load existing character
    const existingCharacter = CharacterService.getCharacter(updateData.id);

    if (!existingCharacter) {
      return NextResponse.json(
        { error: 'Character not found' },
        { status: 404 }
      );
    }

    // Merge updates with existing data
    const updatedCharacter: FullCharacterData = {
      ...existingCharacter,
      ...updateData,
      id: updateData.id, // Ensure ID doesn't change
      updatedAt: new Date().toISOString()
    };

    const success = CharacterService.saveCharacter(updatedCharacter);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to update character' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      character: updatedCharacter,
      message: 'Character updated successfully'
    });
  } catch (error) {
    console.error('Error updating character:', error);
    return NextResponse.json(
      { error: 'Failed to update character' },
      { status: 500 }
    );
  }
}

// DELETE - Delete character (local development only)
export async function DELETE(request: NextRequest) {
  try {
    // Only allow in development mode
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { error: 'Admin API only available in development mode' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const characterId = searchParams.get('id');

    if (!characterId) {
      return NextResponse.json(
        { error: 'Character ID is required' },
        { status: 400 }
      );
    }

    const success = CharacterService.deleteCharacter(characterId);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete character' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Character deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting character:', error);
    return NextResponse.json(
      { error: 'Failed to delete character' },
      { status: 500 }
    );
  }
}
