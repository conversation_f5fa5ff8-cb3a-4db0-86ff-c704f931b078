{"name": "ailuvchhat-functions", "version": "1.0.0", "description": "Firebase Functions for AiLuvChat", "main": "lib/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "dependencies": {"@genkit-ai/firebase": "^0.5.0", "@genkit-ai/googleai": "^0.5.0", "@supabase/supabase-js": "^2.39.0", "cors": "^2.8.5", "elevenlabs-node": "^1.0.0", "express": "^4.18.0", "firebase-admin": "^12.0.0", "firebase-functions": "^4.9.0", "genkit": "^0.5.0", "openai": "^4.0.0"}, "devDependencies": {"@types/cors": "^2.8.0", "@types/express": "^4.17.0", "typescript": "^5.0.0"}, "private": true}