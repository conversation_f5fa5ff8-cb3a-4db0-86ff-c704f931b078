"use client";

import { useState, useRef, useEffect } from "react";
import { Character } from "@/types/firebase";
import { useI18n } from "@/contexts/I18nProvider";

interface VideoBackgroundProps {
  character: Character;
  isSpeaking: boolean;
}

export default function VideoBackground({ character, isSpeaking }: VideoBackgroundProps) {
  const { language } = useI18n();
  const [videoError, setVideoError] = useState(false);
  const [normalVideoLoaded, setNormalVideoLoaded] = useState(false);
  const [lipSyncVideoLoaded, setLipSyncVideoLoaded] = useState(false);
  const [videosStarted, setVideosStarted] = useState(false);
  const normalVideoRef = useRef<HTMLVideoElement>(null);
  const lipSyncVideoRef = useRef<HTMLVideoElement>(null);

  // Debug: Log isSpeaking changes
  useEffect(() => {
    console.log(`VideoBackground: isSpeaking changed to ${isSpeaking} for character ${character.id}`);
  }, [isSpeaking, character.id]);

  // Safe string extraction for multilingual data
  const characterName = typeof character.name === 'object'
    ? (character.name[language] || character.name.en || 'Unknown')
    : String(character.name);

  const characterDescription = typeof character.description === 'object'
    ? (character.description[language] || character.description.en || 'AI Companion')
    : String(character.description);

  // Video style for opacity control - positioning handled by CSS
  const getVideoStyle = (opacity: number) => ({
    opacity,
    transition: 'opacity 0.3s ease-in-out',
  });

  // Initialize videos when character changes
  useEffect(() => {
    console.log('Initializing VideoBackground for character:', character.id);

    // Reset states
    setVideoError(false);
    setNormalVideoLoaded(false);
    setLipSyncVideoLoaded(false);
    setVideosStarted(false);

    const normalVideo = normalVideoRef.current;
    const lipSyncVideo = lipSyncVideoRef.current;

    if (normalVideo && lipSyncVideo) {
      // Get video sources from character data
      const getVideoSources = () => {
        // Try to get from videoSets first (new structure)
        if (character.videoSets && character.videoSets.length > 0) {
          const firstVideoSet = character.videoSets[0];
          return {
            normal: firstVideoSet.normalVideoPath || `/characters/video-sets/${character.id}/${character.id}-set1-normal.mp4`,
            lipSync: firstVideoSet.lipSyncVideoPath || `/characters/video-sets/${character.id}/${character.id}-set1-lipsync.mp4`
          };
        }

        // Fallback to legacy structure
        if (character.videos) {
          return {
            normal: character.videos.idle?.[0] || `/characters/video-sets/${character.id}/${character.id}-set1-normal.mp4`,
            lipSync: character.videos.speaking?.[0] || `/characters/video-sets/${character.id}/${character.id}-set1-lipsync.mp4`
          };
        }

        // Final fallback to constructed paths
        return {
          normal: `/characters/video-sets/${character.id}/${character.id}-set1-normal.mp4`,
          lipSync: `/characters/video-sets/${character.id}/${character.id}-set1-lipsync.mp4`
        };
      };

      const videoSources = getVideoSources();

      // Set video sources
      normalVideo.src = videoSources.normal;
      lipSyncVideo.src = videoSources.lipSync;

      console.log('Video sources for character:', character.id, videoSources);

      // Load videos
      normalVideo.load();
      lipSyncVideo.load();
    }
  }, [character.id]);

  // Video synchronization - keep both videos in sync
  useEffect(() => {
    if (!videosStarted || !normalVideoLoaded || !lipSyncVideoLoaded) return;

    const normalVideo = normalVideoRef.current;
    const lipSyncVideo = lipSyncVideoRef.current;

    if (!normalVideo || !lipSyncVideo) return;

    const syncVideos = () => {
      const timeDiff = Math.abs(normalVideo.currentTime - lipSyncVideo.currentTime);
      if (timeDiff > 0.1) { // Sync if difference is more than 100ms
        lipSyncVideo.currentTime = normalVideo.currentTime;
      }
    };

    // Set up sync interval
    const syncInterval = setInterval(syncVideos, 100);

    return () => {
      clearInterval(syncInterval);
    };
  }, [videosStarted, normalVideoLoaded, lipSyncVideoLoaded]);

  // Start videos when both are loaded
  useEffect(() => {
    if (!normalVideoLoaded || !lipSyncVideoLoaded || videosStarted) return;

    const normalVideo = normalVideoRef.current;
    const lipSyncVideo = lipSyncVideoRef.current;

    if (!normalVideo || !lipSyncVideo) return;

    const startVideos = async () => {
      try {
        // Start both videos simultaneously
        await Promise.all([
          normalVideo.play(),
          lipSyncVideo.play()
        ]);

        // Sync them immediately
        lipSyncVideo.currentTime = normalVideo.currentTime;

        setVideosStarted(true);
        console.log('Both videos started and synced successfully');

      } catch (error) {
        console.warn('Video autoplay failed, waiting for user interaction:', error);

        // Set up user interaction handler
        const handleUserInteraction = async () => {
          try {
            await Promise.all([
              normalVideo.play(),
              lipSyncVideo.play()
            ]);

            lipSyncVideo.currentTime = normalVideo.currentTime;
            setVideosStarted(true);

            document.removeEventListener('click', handleUserInteraction);
            document.removeEventListener('keydown', handleUserInteraction);
            console.log('Videos started after user interaction');

          } catch (playError) {
            console.error('Failed to start videos even after user interaction:', playError);
            setVideoError(true);
          }
        };

        document.addEventListener('click', handleUserInteraction);
        document.addEventListener('keydown', handleUserInteraction);
      }
    };

    startVideos();
  }, [normalVideoLoaded, lipSyncVideoLoaded, videosStarted]);

  // Video event handlers
  const handleNormalVideoLoad = () => {
    console.log('Normal video loaded for character:', character.id);
    setNormalVideoLoaded(true);
    setVideoError(false);
  };

  const handleNormalVideoCanPlay = () => {
    console.log('Normal video can play for character:', character.id);
    setNormalVideoLoaded(true);
    setVideoError(false);
  };

  const handleLipSyncVideoLoad = () => {
    console.log('Lip-sync video loaded for character:', character.id);
    setLipSyncVideoLoaded(true);
    setVideoError(false);
  };

  const handleLipSyncVideoCanPlay = () => {
    console.log('Lip-sync video can play for character:', character.id);
    setLipSyncVideoLoaded(true);
    setVideoError(false);
  };

  const handleVideoError = (videoType: string) => {
    console.warn(`${videoType} video error for character:`, character.id);
    setVideoError(true);
  };

  if (videoError) {
    return (
      <div className="video-background">
        <div className="w-full h-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-4xl">👤</span>
            </div>
            <h2 className="text-2xl font-semibold mb-2">{characterName}</h2>
            <p className="text-lg opacity-90">{characterDescription}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="video-background relative w-full h-full flex items-center justify-center bg-black">
      {/* Loading placeholder - show until both videos are loaded and started */}
      {(!videosStarted || !normalVideoLoaded) && !videoError && (
        <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center z-10">
          <div className="text-center text-white">
            <div className="w-16 h-16 border-4 border-white/30 border-t-white rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-lg">Loading {characterName}...</p>
          </div>
        </div>
      )}

      {/* Normal Video (always visible background layer) */}
      <video
        ref={normalVideoRef}
        style={{
          ...getVideoStyle(1),
          zIndex: 1
        }}
        loop
        muted
        playsInline
        preload="auto"
        onLoadedData={handleNormalVideoLoad}
        onCanPlay={handleNormalVideoCanPlay}
        onError={() => handleVideoError('Normal')}
      >
        Your browser does not support the video tag.
      </video>

      {/* Lip-Sync Video (overlay layer - only visible when speaking) */}
      <video
        ref={lipSyncVideoRef}
        style={{
          ...getVideoStyle(isSpeaking ? 1 : 0),
          zIndex: 2
        }}
        loop
        muted
        playsInline
        preload="auto"
        onLoadedData={handleLipSyncVideoLoad}
        onCanPlay={handleLipSyncVideoCanPlay}
        onError={() => handleVideoError('Lip-sync')}
      >
        Your browser does not support the video tag.
      </video>

      {/* Character Info Overlay (visible when videos fail to load) */}
      {videoError && (
        <div className="absolute inset-0 flex items-center justify-center z-20">
          <div className="text-center text-white bg-black/50 backdrop-blur-sm rounded-lg p-8">
            <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-4xl">👤</span>
            </div>
            <h2 className="text-2xl font-semibold mb-2">{characterName}</h2>
            <p className="text-lg opacity-90">{characterDescription}</p>
          </div>
        </div>
      )}
    </div>
  );
}
