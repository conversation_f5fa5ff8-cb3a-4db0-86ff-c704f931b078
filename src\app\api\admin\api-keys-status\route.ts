import { NextRequest, NextResponse } from 'next/server';
import { getConfiguredProviders, AI_PROVIDERS } from '@/lib/aiProviders';

// Admin authentication check
function isAdmin(request: NextRequest): boolean {
  const adminToken = request.headers.get('x-admin-token');
  const expectedToken = process.env.NEXT_PUBLIC_ADMIN_TOKEN || 'your-secure-admin-token-here-change-this';
  return adminToken === expectedToken;
}

// GET - Check API key configuration status
export async function GET(request: NextRequest) {
  try {
    if (!isAdmin(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const configuredProviders = getConfiguredProviders();
    const allProviders = Object.keys(AI_PROVIDERS);
    
    const providerStatus = allProviders.map(provider => ({
      provider,
      name: AI_PROVIDERS[provider].name,
      configured: configuredProviders.includes(provider),
      envVar: AI_PROVIDERS[provider].apiKeyEnvVar
    }));

    return NextResponse.json({
      success: true,
      configuredProviders,
      allProviders,
      providerStatus,
      summary: {
        total: allProviders.length,
        configured: configuredProviders.length,
        missing: allProviders.length - configuredProviders.length
      }
    });
  } catch (error) {
    console.error('Error checking API key status:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
