// Client-side character data management (browser-safe)
import { FullCharacterData } from '@/types/firebase';

// Cache for character data
let charactersCache: Record<string, FullCharacterData> | null = null;
let lastFetchTime = 0;
const CACHE_DURATION = 30 * 1000; // 30 seconds (reduced for faster admin updates)

// Fetch characters from API (client-side safe)
export async function loadCharactersFromClient(): Promise<Record<string, FullCharacterData>> {
  const now = Date.now();
  
  // Return cached data if still valid
  if (charactersCache && (now - lastFetchTime) < CACHE_DURATION) {
    return charactersCache;
  }

  try {
    console.log('Fetching characters from API...');
    // Add cache-busting parameter to ensure fresh data
    const response = await fetch(`/api/characters?t=${Date.now()}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch characters: ${response.status}`);
    }

    const data = await response.json();
    
    if (!data.success || !data.characters) {
      throw new Error('Invalid characters data received');
    }

    // Convert array to object with character ID as key
    const charactersMap: Record<string, FullCharacterData> = {};
    data.characters.forEach((character: any) => {
      charactersMap[character.id] = character;
    });

    // Update cache
    charactersCache = charactersMap;
    lastFetchTime = now;

    console.log(`Loaded ${Object.keys(charactersMap).length} characters from API`);
    return charactersMap;

  } catch (error) {
    console.error('Failed to load characters from API:', error);
    
    // Return empty object if fetch fails
    return {};
  }
}

// Get specific character by ID (client-side safe)
export async function getCharacterFromClient(characterId: string): Promise<FullCharacterData | null> {
  try {
    const characters = await loadCharactersFromClient();
    return characters[characterId] || null;
  } catch (error) {
    console.error(`Failed to get character ${characterId}:`, error);
    return null;
  }
}

// Get all active characters with voice IDs (for warmup)
export async function getActiveCharactersWithVoice(): Promise<Array<{
  id: string;
  voiceId: string;
  name: string;
}>> {
  try {
    const characters = await loadCharactersFromClient();
    
    return Object.entries(characters)
      .filter(([_, character]) => 
        character.isActive && 
        character.elevenLabsVoiceId
      )
      .map(([id, character]) => ({
        id,
        voiceId: character.elevenLabsVoiceId!,
        name: typeof character.name === 'string' ? character.name : character.name.en
      }));
  } catch (error) {
    console.error('Failed to get active characters with voice:', error);
    return [];
  }
}

// Clear cache (useful for testing or manual refresh)
export function clearCharactersCache(): void {
  charactersCache = null;
  lastFetchTime = 0;
  console.log('Characters cache cleared');
}

// Check if cache is valid
export function isCacheValid(): boolean {
  const now = Date.now();
  return charactersCache !== null && (now - lastFetchTime) < CACHE_DURATION;
}

// Get cache info for debugging
export function getCacheInfo(): {
  cached: boolean;
  lastFetch: Date | null;
  charactersCount: number;
  cacheAge: number;
} {
  return {
    cached: charactersCache !== null,
    lastFetch: lastFetchTime > 0 ? new Date(lastFetchTime) : null,
    charactersCount: charactersCache ? Object.keys(charactersCache).length : 0,
    cacheAge: Date.now() - lastFetchTime
  };
}
