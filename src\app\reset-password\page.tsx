"use client";

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { confirmPasswordReset } from 'firebase/auth';
import { auth } from '@/lib/firebase';

function ResetPasswordForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isJapanese, setIsJapanese] = useState(false);

  const oobCode = searchParams.get('oobCode');
  const lang = searchParams.get('lang') || 'en';

  useEffect(() => {
    setIsJapanese(lang === 'ja');
    
    if (!oobCode) {
      setError(isJapanese ? '無効なリセットコードです。' : 'Invalid reset code.');
    }
  }, [oobCode, isJapanese]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!oobCode) {
      setError(isJapanese ? '無効なリセットコードです。' : 'Invalid reset code.');
      return;
    }

    if (password !== confirmPassword) {
      setError(isJapanese ? 'パスワードが一致しません。' : 'Passwords do not match.');
      return;
    }

    if (password.length < 6) {
      setError(isJapanese ? 'パスワードは6文字以上である必要があります。' : 'Password must be at least 6 characters.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      if (!auth) {
        throw new Error('Firebase not initialized');
      }

      await confirmPasswordReset(auth, oobCode, password);
      
      // Success - redirect to login
      alert(isJapanese 
        ? 'パスワードが正常にリセットされました。新しいパスワードでログインしてください。' 
        : 'Password reset successfully. Please login with your new password.'
      );
      
      router.push(isJapanese ? '/ja' : '/');
    } catch (error: any) {
      console.error('Password reset error:', error);
      
      switch (error.code) {
        case 'auth/expired-action-code':
          setError(isJapanese 
            ? 'リセットコードの有効期限が切れています。' 
            : 'Reset code has expired.'
          );
          break;
        case 'auth/invalid-action-code':
          setError(isJapanese 
            ? 'リセットコードが無効です。' 
            : 'Invalid reset code.'
          );
          break;
        case 'auth/weak-password':
          setError(isJapanese 
            ? 'パスワードが弱すぎます。' 
            : 'Password is too weak.'
          );
          break;
        default:
          setError(isJapanese 
            ? 'エラーが発生しました。もう一度お試しください。' 
            : 'An error occurred. Please try again.'
          );
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-md w-full p-8">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-gray-800 mb-2">
            {isJapanese ? 'パスワードリセット' : 'Reset Password'}
          </h1>
          <p className="text-gray-600">
            {isJapanese 
              ? '新しいパスワードを入力してください。' 
              : 'Enter your new password.'
            }
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-6">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {isJapanese ? '新しいパスワード' : 'New Password'}
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder={isJapanese ? '新しいパスワードを入力' : 'Enter new password'}
              required
              minLength={6}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {isJapanese ? 'パスワード確認' : 'Confirm Password'}
            </label>
            <input
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder={isJapanese ? 'パスワードを再入力' : 'Confirm new password'}
              required
              minLength={6}
            />
          </div>

          <button
            type="submit"
            disabled={loading || !oobCode}
            className="w-full bg-blue-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {isJapanese ? '更新中...' : 'Updating...'}
              </>
            ) : (
              isJapanese ? 'パスワードを更新' : 'Update Password'
            )}
          </button>
        </form>

        <div className="mt-6 text-center">
          <button
            onClick={() => router.push(isJapanese ? '/ja' : '/')}
            className="text-blue-500 hover:text-blue-600 text-sm transition-colors"
          >
            {isJapanese ? 'ホームに戻る' : 'Back to Home'}
          </button>
        </div>
      </div>
    </div>
  );
}

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
        <div className="bg-white rounded-xl shadow-2xl max-w-md w-full p-8 text-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="animate-spin w-8 h-8 text-blue-500" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Loading...</h1>
          <p className="text-gray-600">Please wait a moment.</p>
        </div>
      </div>
    }>
      <ResetPasswordForm />
    </Suspense>
  );
}
