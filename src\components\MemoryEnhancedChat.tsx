/**
 * 記憶システム対応チャットコンポーネント
 * 有料ユーザーには長期記憶機能を提供
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface MemoryStats {
  isPaidUser: boolean;
  shortTermCount: number;
  longTermCount: number;
  totalMemories: number;
  memorySystemActive: boolean;
  subscriptionTier: string;
  promptTruncated?: boolean;
}

interface MemoryEnhancedChatProps {
  characterId: string;
  onSendMessage: (message: string) => Promise<{
    success: boolean;
    response?: string;
    memoryStats?: MemoryStats;
    error?: string;
  }>;
}

export default function MemoryEnhancedChat({ 
  characterId, 
  onSendMessage 
}: MemoryEnhancedChatProps) {
  const { user, userData } = useAuth();
  const [memoryStats, setMemoryStats] = useState<MemoryStats | null>(null);
  const [showMemoryInfo, setShowMemoryInfo] = useState(false);

  // ユーザーの課金状態確認
  const isPaidUser = userData?.subscriptionTier !== 'free';

  /**
   * 記憶統計表示コンポーネント
   */
  const MemoryStatsDisplay = ({ stats }: { stats: MemoryStats }) => (
    <div className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-semibold text-purple-800 flex items-center">
          🧠 記憶システム状態
          <button
            onClick={() => setShowMemoryInfo(!showMemoryInfo)}
            className="ml-2 text-purple-600 hover:text-purple-800"
          >
            {showMemoryInfo ? '▼' : '▶'}
          </button>
        </h3>
        <div className="flex items-center space-x-2">
          {stats.isPaidUser ? (
            <span className="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-2 py-1 rounded-full text-xs font-medium">
              ✨ 有料プラン
            </span>
          ) : (
            <span className="bg-gray-400 text-white px-2 py-1 rounded-full text-xs font-medium">
              🆓 無料プラン
            </span>
          )}
        </div>
      </div>

      {showMemoryInfo && (
        <div className="space-y-2 text-sm">
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-white rounded-lg p-3 border border-purple-100">
              <div className="text-purple-600 font-medium">短期記憶</div>
              <div className="text-2xl font-bold text-purple-800">
                {stats.shortTermCount}
              </div>
              <div className="text-xs text-gray-600">最近の会話</div>
            </div>
            
            {stats.isPaidUser && (
              <div className="bg-white rounded-lg p-3 border border-blue-100">
                <div className="text-blue-600 font-medium">長期記憶</div>
                <div className="text-2xl font-bold text-blue-800">
                  {stats.longTermCount}
                </div>
                <div className="text-xs text-gray-600">重要な思い出</div>
              </div>
            )}
          </div>

          {!stats.isPaidUser && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-3">
              <div className="flex items-center text-yellow-800">
                <span className="text-lg mr-2">💡</span>
                <div>
                  <div className="font-medium">長期記憶機能をアンロック</div>
                  <div className="text-sm text-yellow-700">
                    有料プランで、AIがあなたとの重要な思い出を覚えてくれます
                  </div>
                </div>
              </div>
              <button className="mt-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-purple-600 hover:to-blue-600 transition-all">
                プランをアップグレード
              </button>
            </div>
          )}

          {stats.promptTruncated && (
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-2 text-xs text-orange-800">
              ⚠️ 記憶が多すぎるため、一部の古い記憶を省略しました
            </div>
          )}
        </div>
      )}
    </div>
  );

  /**
   * 記憶システムの説明コンポーネント
   */
  const MemorySystemExplanation = () => (
    <div className="bg-gradient-to-br from-indigo-50 to-purple-50 border border-indigo-200 rounded-xl p-6 mb-6">
      <h2 className="text-lg font-bold text-indigo-800 mb-4 flex items-center">
        🧠 ハイブリッド記憶システム
      </h2>
      
      <div className="space-y-4">
        <div className="flex items-start space-x-3">
          <div className="bg-green-100 rounded-full p-2">
            <span className="text-green-600">💬</span>
          </div>
          <div>
            <h3 className="font-semibold text-gray-800">短期記憶（全ユーザー）</h3>
            <p className="text-sm text-gray-600">
              最近の会話の流れを記憶し、自然な対話を維持します
            </p>
          </div>
        </div>

        <div className="flex items-start space-x-3">
          <div className={`rounded-full p-2 ${isPaidUser ? 'bg-purple-100' : 'bg-gray-100'}`}>
            <span className={isPaidUser ? 'text-purple-600' : 'text-gray-400'}>🌟</span>
          </div>
          <div>
            <h3 className={`font-semibold ${isPaidUser ? 'text-gray-800' : 'text-gray-500'}`}>
              長期記憶（有料ユーザー限定）
              {!isPaidUser && <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full ml-2">ロック中</span>}
            </h3>
            <p className={`text-sm ${isPaidUser ? 'text-gray-600' : 'text-gray-400'}`}>
              重要な出来事や好みを長期間記憶し、より深い関係性を構築
            </p>
          </div>
        </div>
      </div>

      {!isPaidUser && (
        <div className="mt-4 p-4 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg text-white">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-semibold">長期記憶をアンロック</h4>
              <p className="text-sm opacity-90">
                AIがあなたとの特別な思い出を永続的に記憶
              </p>
            </div>
            <button className="bg-white text-purple-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
              アップグレード
            </button>
          </div>
        </div>
      )}
    </div>
  );

  /**
   * メッセージ送信処理（記憶統計更新付き）
   */
  const handleSendMessage = async (message: string) => {
    try {
      const result = await onSendMessage(message);
      
      if (result.success && result.memoryStats) {
        setMemoryStats(result.memoryStats);
      }
      
      return result;
    } catch (error) {
      console.error('Error sending message with memory:', error);
      return {
        success: false,
        error: 'メッセージの送信に失敗しました'
      };
    }
  };

  return (
    <div className="space-y-4">
      {/* 記憶システム説明 */}
      <MemorySystemExplanation />
      
      {/* 記憶統計表示 */}
      {memoryStats && <MemoryStatsDisplay stats={memoryStats} />}
      
      {/* 既存のチャットインターフェース */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="text-sm text-gray-600 mb-2">
          {isPaidUser ? (
            <span className="text-purple-600">
              ✨ 長期記憶機能が有効です - AIがあなたとの思い出を覚えています
            </span>
          ) : (
            <span className="text-gray-500">
              💬 短期記憶モード - 最近の会話のみを記憶
            </span>
          )}
        </div>
        
        {/* ここに既存のチャットコンポーネントを統合 */}
        <div className="text-center text-gray-400 py-8">
          既存のChatInterfaceコンポーネントをここに統合
        </div>
      </div>
    </div>
  );
}
