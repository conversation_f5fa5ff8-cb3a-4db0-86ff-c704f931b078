import { doc, updateDoc, getDoc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { User } from '@/types/firebase';

/**
 * Points Service
 * Manages user points including daily refresh and consumption
 */

export class PointsService {
  private static readonly FREE_TIER_MAX_POINTS = 50;
  private static readonly DAILY_POINTS_REFRESH = 50;
  private static readonly INITIAL_POINTS = 100;

  /**
   * Check if user has enough points for an action
   */
  static hasEnoughPoints(user: User, requiredPoints: number = 1): boolean {
    console.log('PointsService: Checking points', {
      currentPoints: user.points,
      requiredPoints,
      hasEnough: user.points >= requiredPoints
    });
    return user.points >= requiredPoints;
  }

  /**
   * Consume points for an action
   */
  static async consumePoints(userId: string, pointsToConsume: number = 1): Promise<boolean> {
    if (!db) {
      console.warn('Firebase not initialized');
      return false;
    }

    try {
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data() as User;

      if (userData.points < pointsToConsume) {
        return false; // Not enough points
      }

      const newPoints = userData.points - pointsToConsume;

      await updateDoc(userRef, {
        points: newPoints,
        updatedAt: Timestamp.now()
      });

      return true;
    } catch (error) {
      console.error('Error consuming points:', error);
      return false;
    }
  }

  /**
   * Add points to user account
   */
  static async addPoints(userId: string, pointsToAdd: number): Promise<boolean> {
    if (!db) {
      console.warn('Firebase not initialized');
      return false;
    }

    try {
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data() as User;
      const newPoints = userData.points + pointsToAdd;

      await updateDoc(userRef, {
        points: newPoints,
        updatedAt: Timestamp.now()
      });

      return true;
    } catch (error) {
      console.error('Error adding points:', error);
      return false;
    }
  }

  /**
   * Check if daily points refresh is needed and apply it
   */
  static async checkAndApplyDailyRefresh(userId: string): Promise<{ refreshed: boolean; pointsAdded: number }> {
    if (!db) {
      console.warn('Firebase not initialized');
      return { refreshed: false, pointsAdded: 0 };
    }

    try {
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data() as User;

      // Check if user is free tier
      if (userData.subscriptionTier !== 'free') {
        return { refreshed: false, pointsAdded: 0 };
      }

      const now = new Date();
      const lastUpdate = userData.lastPointsUpdate?.toDate();

      // Check if it's a new day
      if (lastUpdate && this.isSameDay(now, lastUpdate)) {
        return { refreshed: false, pointsAdded: 0 };
      }

      // Calculate points to add (max 50, fill up to 50)
      const currentPoints = userData.points;
      const pointsToAdd = Math.max(0, this.FREE_TIER_MAX_POINTS - currentPoints);

      if (pointsToAdd > 0) {
        await updateDoc(userRef, {
          points: currentPoints + pointsToAdd,
          lastPointsUpdate: Timestamp.now(),
          updatedAt: Timestamp.now()
        });
      } else {
        // Update lastPointsUpdate even if no points added
        await updateDoc(userRef, {
          lastPointsUpdate: Timestamp.now(),
          updatedAt: Timestamp.now()
        });
      }

      return { refreshed: true, pointsAdded: pointsToAdd };
    } catch (error) {
      console.error('Error checking daily refresh:', error);
      return { refreshed: false, pointsAdded: 0 };
    }
  }

  /**
   * Check if two dates are the same day
   */
  private static isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  /**
   * Get points needed message for insufficient points
   */
  static getInsufficientPointsMessage(language: 'en' | 'ja' = 'en'): string {
    if (language === 'ja') {
      return 'ポイントが足りません。翌日に50ポイント追加されます。';
    }
    return 'Insufficient points. 50 points will be added tomorrow.';
  }

  /**
   * Initialize user with starting points
   */
  static async initializeUserPoints(userId: string): Promise<boolean> {
    if (!db) {
      console.warn('Firebase not initialized');
      return false;
    }

    try {
      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        points: this.INITIAL_POINTS,
        lastPointsUpdate: Timestamp.now(),
        updatedAt: Timestamp.now()
      });

      return true;
    } catch (error) {
      console.error('Error initializing user points:', error);
      return false;
    }
  }
}

export default PointsService;
