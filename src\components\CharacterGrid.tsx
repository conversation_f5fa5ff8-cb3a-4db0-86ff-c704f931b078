"use client";

import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { getCharacters } from "@/lib/firebaseService";
import { Character } from "@/types/firebase";
import { useAuth } from "@/contexts/AuthContext";
import { useI18n } from "@/contexts/I18nProvider";
import AuthModal from "./AuthModal";

interface CharacterCardProps {
  character: Character;
  isInView: boolean;
}

function CharacterCard({ character, isInView }: CharacterCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [videoError, setVideoError] = useState(false);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const { user } = useAuth();
  const { t, language } = useI18n();

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Video playback logic
  useEffect(() => {
    const video = videoRef.current;
    if (!video || videoError || !videoLoaded) return;

    const shouldPlay = isMobile ? isInView : (isHovered && isInView);

    if (shouldPlay) {
      video.play().catch((error) => {
        console.warn(`Video play failed for ${character.id}:`, error);
        // Don't set video error for autoplay failures, just log it
      });
    } else {
      video.pause();
      video.currentTime = 0;
    }
  }, [isHovered, isInView, videoLoaded, isMobile, videoError, character.id]);

  const characterName = typeof character.name === 'object'
    ? (character.name[language] || character.name.en || 'Unknown')
    : String(character.name);

  const characterDescription = typeof character.description === 'object'
    ? (character.description[language] || character.description.en || 'No description available')
    : String(character.description);

  const characterTags = typeof character.tags === 'object' && character.tags
    ? Array.isArray(character.tags[language])
      ? character.tags[language]
      : Array.isArray(character.tags.en)
      ? character.tags.en
      : []
    : [];

  const handleChatClick = (e: React.MouseEvent) => {
    // Prevent video from starting on click
    const video = videoRef.current;
    if (video) {
      video.pause();
      video.currentTime = 0;
    }

    // Allow navigation to chat page even without login
    // Authentication will be handled in the chat interface
  };

  // Generate appropriate chat URL based on language
  const chatUrl = language === 'ja' ? `/ja/chat/${character.id}` : `/chat/${character.id}`;

  return (
    <Link href={chatUrl} onClick={handleChatClick}>
      <div
        className="bg-white rounded-lg shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:scale-105 cursor-pointer"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="relative aspect-[3/4] overflow-hidden">
          {/* Character Image - Use Profile Image from admin settings */}
          <Image
            src={
              !imageError && character.images?.avatar
                ? character.images.avatar
                : !imageError && character.images?.preview
                ? character.images.preview
                : '/characters/images/placeholder.svg'
            }
            alt={characterName}
            fill
            className="object-cover transition-opacity duration-300"
            style={{ opacity: isHovered ? 0 : 1 }}
            onError={() => {
              console.warn(`Image error for character ${character.id}:`, character.images);
              setImageError(true);
            }}
            unoptimized
          />

          {/* Preview Video - Video Set 1 Normal Video */}
          {!videoError && (() => {
            // Get video source from character data
            const getVideoSource = () => {
              // Try to get from videoSets first (new structure)
              if (character.videoSets && character.videoSets.length > 0) {
                const firstVideoSet = character.videoSets[0];
                if (firstVideoSet.normalVideoPath) {
                  return firstVideoSet.normalVideoPath;
                }
              }

              // Fallback to legacy structure
              if (character.videos?.idle && character.videos.idle.length > 0) {
                return character.videos.idle[0];
              }

              // Final fallback to constructed path
              return `/characters/video-sets/${character.id}/${character.id}-set1-normal.mp4`;
            };

            const videoSource = getVideoSource();

            return (
              <video
                ref={videoRef}
                className="absolute inset-0 w-full h-full object-cover transition-opacity duration-300"
                style={{
                  opacity: isMobile ? (isInView && videoLoaded ? 1 : 0) : (isHovered && videoLoaded ? 1 : 0),
                  aspectRatio: '9/16'
                }}
                muted
                loop
                playsInline
                preload="auto"
                onLoadedData={() => {
                  console.log(`Video loaded for character: ${character.id}`);
                  setVideoLoaded(true);
                }}
                onCanPlay={() => {
                  console.log(`Video can play for character: ${character.id}`);
                  setVideoLoaded(true);
                }}
                onError={(e) => {
                  console.warn(`Video error for character ${character.id}:`, e);
                  console.warn(`Video source: ${videoSource}`);
                  setVideoError(true);
                }}
              >
                <source
                  src={videoSource}
                  type="video/mp4"
                />
              </video>
            );
          })()}

          {/* Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300">
            <div className="absolute bottom-4 left-4 right-4">
              <div className="flex flex-wrap gap-1">
                {characterTags.map((tag, index) => (
                  <span
                    key={`${character.id}-tag-${index}`}
                    className="bg-white/20 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full"
                  >
                    {typeof tag === 'string' ? tag : String(tag)}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="p-4">
          <h3 className="text-xl font-semibold text-gray-800 mb-2">{characterName}</h3>
          <p className="text-gray-600 text-sm line-clamp-3">{characterDescription}</p>
          <button className="mt-3 w-full bg-primary text-white py-2 rounded-md hover:bg-blue-600 transition-colors">
            {t('startChat')}
          </button>
        </div>
      </div>

      {/* Auth Modal */}
      {showAuthModal && (
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
      )}
    </Link>
  );
}

export default function CharacterGrid() {
  const [characters, setCharacters] = useState<Character[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [visibleCharacters, setVisibleCharacters] = useState<Set<string>>(new Set());
  const observerRef = useRef<IntersectionObserver | null>(null);
  const [mounted, setMounted] = useState(false);
  const { t, language } = useI18n();

  // Ensure component is mounted before doing anything
  useEffect(() => {
    setMounted(true);
  }, []);

  // Load characters from Firebase
  useEffect(() => {
    if (!mounted) return;

    const loadCharacters = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log('CharacterGrid: Starting to load characters...');
        const charactersData = await getCharacters();
        console.log('CharacterGrid: Loaded characters:', charactersData.length);
        setCharacters(charactersData);
      } catch (err) {
        console.error('CharacterGrid: Error loading characters:', err);
        setError('Failed to load characters. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    loadCharacters();
  }, [mounted]);

  useEffect(() => {
    if (!mounted || typeof window === 'undefined') return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const characterId = entry.target.getAttribute('data-character-id');
          if (characterId) {
            setVisibleCharacters(prev => {
              const newSet = new Set(prev);
              if (entry.isIntersecting) {
                newSet.add(characterId);
              } else {
                newSet.delete(characterId);
              }
              return newSet;
            });
          }
        });
      },
      { threshold: 0.5 }
    );

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [mounted]);

  useEffect(() => {
    if (!mounted || !observerRef.current || characters.length === 0) return;

    // Use setTimeout to ensure DOM is ready
    const timeoutId = setTimeout(() => {
      const cards = document.querySelectorAll('[data-character-id]');
      cards.forEach(card => {
        if (observerRef.current) {
          observerRef.current.observe(card);
        }
      });
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [characters, mounted]);

  // Early return if not mounted (prevents SSR issues)
  if (!mounted) {
    return (
      <section>
        <h2 className="text-3xl font-bold text-center text-gray-800 mb-8">
          Choose Your AI Companion
        </h2>
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </section>
    );
  }

  if (loading) {
    return (
      <section>
        <h2 className="text-3xl font-bold text-center text-gray-800 mb-8">
          {t('chooseCompanion')}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden animate-pulse">
              <div className="aspect-[3/4] bg-gray-300"></div>
              <div className="p-4">
                <div className="h-6 bg-gray-300 rounded mb-2"></div>
                <div className="h-4 bg-gray-300 rounded mb-2"></div>
                <div className="h-4 bg-gray-300 rounded mb-4"></div>
                <div className="h-10 bg-gray-300 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section>
        <h2 className="text-3xl font-bold text-center text-gray-800 mb-8">
          {t('chooseCompanion')}
        </h2>
        <div className="text-center py-12">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-primary text-white px-6 py-2 rounded-md hover:bg-blue-600 transition-colors"
          >
            {language === 'ja' ? '再試行' : 'Try Again'}
          </button>
        </div>
      </section>
    );
  }

  return (
    <section>
      <h2 className="text-3xl font-bold text-center text-gray-800 mb-8">
        {t('chooseCompanion')}
      </h2>

      {characters.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-600 mb-4">
            {language === 'ja' ? 'キャラクターが見つかりません。' : 'No characters available at the moment.'}
          </p>
          <p className="text-gray-500 text-sm">
            {language === 'ja' ? '後でもう一度お試しください。' : 'Please check back later.'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {characters.map((character) => (
            <div key={character.id} data-character-id={character.id}>
              <CharacterCard
                character={character}
                isInView={visibleCharacters.has(character.id)}
              />
            </div>
          ))}
        </div>
      )}
    </section>
  );
}
