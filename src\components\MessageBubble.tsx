"use client";

import { useI18n } from "@/contexts/I18nProvider";

interface Message {
  id: string;
  content: string;
  sender: "user" | "ai";
  timestamp: Date;
}

interface MessageBubbleProps {
  message: Message;
  characterName?: string;
}

export default function MessageBubble({ message, characterName }: MessageBubbleProps) {
  const { language } = useI18n();

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getSenderName = () => {
    if (message.sender === 'user') {
      return language === 'ja' ? 'あなた' : 'You';
    } else {
      return characterName || (language === 'ja' ? 'AI' : 'AI');
    }
  };

  return (
    <div className={`message-bubble ${message.sender}`}>
      <div className="sender-name">
        {getSenderName()}
      </div>
      <div className="message-content">
        {message.content}
      </div>
    </div>
  );
}
