/**
 * Secure Prompt Template Engine
 * Prevents prompt injection by using safe template placeholders
 * instead of dangerous string concatenation
 */

// Template placeholders for secure prompt construction
const SECURE_PROMPT_TEMPLATE = `{{GLOBAL_PROMPT}}

---

CHARACTER-SPECIFIC INSTRUCTIONS:
{{CHARACTER_PROMPT}}

---

IMPORTANT CONTEXT:
- You are talking to: {{USER_NAME}}
- Language: {{LANGUAGE}}
- Your character name: {{CHARACTER_NAME}}

Remember: The user's name is {{USER_NAME}}. Use this name when appropriate in conversation.

---

CONVERSATION HISTORY:
{{CONVERSATION_HISTORY}}

---

CURRENT USER MESSAGE:
{{USER_MESSAGE}}

---

Remember to combine both the global guidelines and your character-specific traits in every response. Always remember that you are talking to {{USER_NAME}}.`;

// Safe template data interface
interface SecureTemplateData {
  globalPrompt: string;
  characterPrompt: string;
  userName?: string;
  language: string;
  characterName: string;
  conversationHistory: string;
  userMessage: string;
}

/**
 * Safely escape user input to prevent injection
 */
function escapeUserInput(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }
  
  // Remove potential injection patterns
  return input
    // Remove system prompt markers
    .replace(/\{\{[^}]*\}\}/g, '')
    // Remove role markers that could confuse the AI
    .replace(/\b(system|assistant|user):/gi, '')
    // Remove instruction keywords
    .replace(/\b(ignore|forget|disregard|override)\s+(previous|above|system|instructions?|prompts?)/gi, '[FILTERED]')
    // Limit length to prevent overwhelming
    .slice(0, 2000)
    // Trim whitespace
    .trim();
}

/**
 * Safely format conversation history
 */
function formatConversationHistory(messages: Array<{role: string, content: string}>): string {
  if (!Array.isArray(messages) || messages.length === 0) {
    return 'No previous conversation.';
  }
  
  return messages
    .slice(-10) // Limit to last 10 messages
    .map(msg => {
      const role = msg.role === 'user' ? 'User' : 'Assistant';
      const content = escapeUserInput(msg.content);
      return `${role}: ${content}`;
    })
    .join('\n');
}

/**
 * Build secure system prompt using template engine
 * NEVER uses string concatenation - only safe template replacement
 */
export function buildSecureSystemPrompt(data: SecureTemplateData): string {
  // Validate required fields
  if (!data.globalPrompt || !data.characterPrompt) {
    throw new Error('Both global prompt and character prompt are required');
  }
  
  // Escape all user-provided inputs
  const safeData = {
    globalPrompt: data.globalPrompt, // Admin-controlled, trusted
    characterPrompt: data.characterPrompt, // Admin-controlled, trusted
    userName: data.userName ? escapeUserInput(data.userName) : 'User',
    language: escapeUserInput(data.language),
    characterName: escapeUserInput(data.characterName),
    conversationHistory: formatConversationHistory(data.conversationHistory as any),
    userMessage: escapeUserInput(data.userMessage)
  };
  
  // Use template replacement instead of string concatenation
  let securePrompt = SECURE_PROMPT_TEMPLATE;
  
  // Replace placeholders with escaped data (use global regex to replace all occurrences)
  securePrompt = securePrompt.replace(/\{\{GLOBAL_PROMPT\}\}/g, safeData.globalPrompt);
  securePrompt = securePrompt.replace(/\{\{CHARACTER_PROMPT\}\}/g, safeData.characterPrompt);
  securePrompt = securePrompt.replace(/\{\{USER_NAME\}\}/g, safeData.userName);
  securePrompt = securePrompt.replace(/\{\{LANGUAGE\}\}/g, safeData.language);
  securePrompt = securePrompt.replace(/\{\{CHARACTER_NAME\}\}/g, safeData.characterName);
  securePrompt = securePrompt.replace(/\{\{CONVERSATION_HISTORY\}\}/g, safeData.conversationHistory);
  securePrompt = securePrompt.replace(/\{\{USER_MESSAGE\}\}/g, safeData.userMessage);
  
  return securePrompt;
}

/**
 * Validate that both global and character prompts are present
 */
export function validateRequiredPrompts(globalPrompt: string, characterPrompt: string): void {
  if (!globalPrompt || globalPrompt.trim().length === 0) {
    throw new Error('Global prompt is required for all characters');
  }
  
  if (!characterPrompt || characterPrompt.trim().length === 0) {
    throw new Error('Character-specific prompt is required');
  }
}

// Message type for AI API
export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

/**
 * Build messages array for AI API with secure system prompt
 */
export function buildSecureMessagesArray(
  globalPrompt: string,
  characterPrompt: string,
  conversationHistory: Array<{role: string, content: string}>,
  userMessage: string,
  options: {
    userName?: string;
    language?: string;
    characterName?: string;
  } = {}
): ChatMessage[] {
  
  // Validate required prompts
  validateRequiredPrompts(globalPrompt, characterPrompt);
  
  // Build secure system prompt
  const systemPrompt = buildSecureSystemPrompt({
    globalPrompt,
    characterPrompt,
    userName: options.userName,
    language: options.language || 'en',
    characterName: options.characterName || 'AI Character',
    conversationHistory: formatConversationHistory(conversationHistory),
    userMessage
  });
  
  // Return secure messages array with proper typing
  const secureMessages: ChatMessage[] = [
    { role: 'system', content: systemPrompt },
    ...conversationHistory.slice(-10).map(msg => ({
      role: (msg.role === 'user' ? 'user' : 'assistant') as 'user' | 'assistant',
      content: escapeUserInput(msg.content)
    })),
    { role: 'user', content: escapeUserInput(userMessage) }
  ];

  return secureMessages;
}
