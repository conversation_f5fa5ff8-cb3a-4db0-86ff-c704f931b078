import { Timestamp } from 'firebase/firestore';

// User types
export interface User {
  uid: string;
  email: string;
  displayName: string;
  photoURL?: string;
  points: number;
  subscriptionTier: 'free' | 'standard' | 'middle' | 'big';
  subscriptionId?: string | null;
  subscriptionStatus?: string | null;
  gender?: 'male' | 'female' | 'non-binary' | 'prefer-not-to-say';
  birthDate?: Timestamp;
  lastPointsUpdate?: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  preferences: {
    language: 'en' | 'ja';
    notifications: boolean;
  };
}

// Character types
export interface Character {
  id: string;
  name: {
    en: string;
    ja: string;
  };
  description: {
    en: string;
    ja: string;
  };
  personality: {
    en: string;
    ja: string;
  };
  // SECURITY: systemPrompt removed from client-side types to prevent exposure
  welcomeMessage?: {
    en: string;
    ja: string;
  };
  tags: {
    en: string[];
    ja: string[];
  };
  images: {
    avatar: string;
    preview: string;
  };
  videos: {
    idle: string[];
    speaking: string[];
  };
  videoSets?: Array<{
    id: string;
    normalVideoPath?: string;
    lipSyncVideoPath?: string;
    uploaded: boolean;
  }>;
  aiConfig: {
    model: 'gpt-4' | 'gpt-3.5-turbo' | 'gemini-pro';
    temperature: number;
    maxTokens: number;
  };
  voiceConfig: {
    provider: 'elevenlabs' | 'nijivoice' | 'minimax';
    voiceId: string;
    model?: string;
    settings: Record<string, any>;
  };
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Extended character data for admin/management purposes
export interface FullCharacterData {
  id: string;
  name: {
    en: string;
    ja: string;
  };
  description: {
    en: string;
    ja: string;
  };
  personality: {
    en: string[];
    ja: string[];
  };
  interests?: {
    en: string[];
    ja: string[];
  };
  age?: number | {
    en: string;
    ja: string;
  };
  occupation?: {
    en: string;
    ja: string;
  };
  background?: {
    en: string;
    ja: string;
  };
  welcomeMessage?: {
    en: string;
    ja: string;
  };
  systemPrompt?: {
    en: string;
    ja: string;
  };
  tags?: {
    en: string[];
    ja: string[];
  };
  images?: {
    avatar: string;
    preview: string;
  };
  videos?: {
    idle: string[];
    speaking: string[];
  };
  media?: {
    profileImage?: string;
    thumbnailImage?: string;
    idleVideo?: string;
    speakingVideo?: string;
  };
  aiConfig?: {
    model: string;
    temperature: number;
    maxTokens: number;
    provider?: string;
  };
  voiceConfig?: {
    provider: 'elevenlabs' | 'nijivoice' | 'minimax';
    voiceId: string;
    model?: string;
    settings?: Record<string, any>;
  };
  aiProvider?: string;
  aiModel?: string;
  elevenLabsVoiceId?: string;
  voiceProvider?: 'elevenlabs' | 'fal-minimax' | 'minimax';
  // MiniMax Direct settings
  minimaxVoiceId?: string;
  minimaxModel?: string;
  customVoiceId?: string;
  // fal.ai/minimax settings
  falMinimaxVoiceId?: string;
  falMinimaxModel?: string;
  videoSets?: Array<{
    id: string;
    normalVideoPath?: string;
    lipSyncVideoPath?: string;
    uploaded: boolean;
  }>;
  isActive: boolean;
  sortOrder?: number;
  createdAt: string | Timestamp;
  updatedAt: string | Timestamp;
}

// Message types
export interface Message {
  id: string;
  chatId: string;
  userId: string;
  characterId: string;
  content: string;
  sender: 'user' | 'ai';
  type: 'text' | 'voice';
  metadata?: {
    voiceUrl?: string;
    duration?: number;
    model?: string;
    tokens?: number;
  };
  createdAt: Timestamp;
}

// Chat session types
export interface ChatSession {
  id: string;
  userId: string;
  characterId: string;
  messages: Message[];
  lastMessageAt: Timestamp;
  isActive: boolean;
  createdAt: Timestamp;
}

// Point transaction types
export interface PointTransaction {
  id: string;
  userId: string;
  type: 'earned' | 'spent' | 'purchased';
  amount: number;
  reason: string;
  metadata?: {
    messageId?: string;
    characterId?: string;
    purchaseId?: string;
  };
  createdAt: Timestamp;
}

// User memory for paid users (RAG)
export interface UserMemory {
  id: string;
  userId: string;
  characterId: string;
  content: string;
  embedding: number[];
  importance: number;
  tags: string[];
  createdAt: Timestamp;
}

// Video trigger mappings
export interface VideoTrigger {
  id: string;
  characterId: string;
  triggerText: {
    en: string[];
    ja: string[];
  };
  videoUrl: string;
  buttonText: {
    en: string;
    ja: string;
  };
  isActive: boolean;
  createdAt: Timestamp;
}

// Analytics types
export interface UserAnalytics {
  userId: string;
  totalMessages: number;
  totalPointsSpent: number;
  favoriteCharacters: string[];
  averageSessionDuration: number;
  lastActiveAt: Timestamp;
  createdAt: Timestamp;
}

// GDPR compliance types
export interface DataExportRequest {
  id: string;
  userId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  downloadUrl?: string;
  expiresAt?: Timestamp;
  createdAt: Timestamp;
}

export interface DataDeletionRequest {
  id: string;
  userId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  reason?: string;
  scheduledAt: Timestamp;
  createdAt: Timestamp;
}
