// Script to fix existing user documents with undefined fields
const admin = require('firebase-admin');

// Initialize Firebase Admin (for emulator)
admin.initializeApp({
  projectId: 'demo-project',
});

// Connect to Firestore emulator
const db = admin.firestore();
db.settings({
  host: 'localhost:8080',
  ssl: false
});

async function fixUserDocuments() {
  try {
    console.log('🔧 Fixing user documents with undefined fields...');
    
    const usersRef = db.collection('users');
    const snapshot = await usersRef.get();
    
    if (snapshot.empty) {
      console.log('No user documents found.');
      return;
    }
    
    const batch = db.batch();
    let fixCount = 0;
    
    snapshot.forEach((doc) => {
      const data = doc.data();
      const updates = {};
      
      // Remove undefined fields
      if (data.gender === undefined) {
        updates.gender = admin.firestore.FieldValue.delete();
        fixCount++;
      }
      
      if (data.birthDate === undefined) {
        updates.birthDate = admin.firestore.FieldValue.delete();
        fixCount++;
      }
      
      if (Object.keys(updates).length > 0) {
        batch.update(doc.ref, updates);
        console.log(`Fixing user ${doc.id}:`, updates);
      }
    });
    
    if (fixCount > 0) {
      await batch.commit();
      console.log(`✅ Fixed ${fixCount} undefined fields in user documents.`);
    } else {
      console.log('✅ No undefined fields found. All user documents are clean.');
    }
    
  } catch (error) {
    console.error('❌ Error fixing user documents:', error);
  }
}

// Run the fix
fixUserDocuments().then(() => {
  console.log('🎉 User document fix completed!');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Fix failed:', error);
  process.exit(1);
});
