/**
 * 統一キャラクター管理サービス
 * 複数の管理ファイルを統合し、シンプルで一貫したAPIを提供
 */

import * as fs from 'fs';
import * as path from 'path';
import { FullCharacterData } from '@/types/firebase';
import { buildSecureMessagesArray, validateRequiredPrompts, ChatMessage } from './securePromptTemplate';

// ファイルパス定義
const DATA_DIR = path.join(process.cwd(), 'data');
const CHARACTERS_FILE = path.join(DATA_DIR, 'characters.json');
const GLOBAL_PROMPTS_FILE = path.join(DATA_DIR, 'global-prompts.json');

// キャッシュ管理
let charactersCache: Record<string, FullCharacterData> = {};
let globalPromptsCache: { ja: string; en: string } = { ja: '', en: '' };
let lastFileModTime = 0;
let cacheInitialized = false;

/**
 * ディレクトリの存在確認・作成
 */
function ensureDirectoriesExist(): void {
  if (!fs.existsSync(DATA_DIR)) {
    fs.mkdirSync(DATA_DIR, { recursive: true });
  }
}

/**
 * キャラクターファイルの変更チェック
 */
function checkFileChanges(): boolean {
  if (typeof window !== 'undefined') return false;
  
  try {
    const stats = fs.statSync(CHARACTERS_FILE);
    const currentModTime = stats.mtime.getTime();
    
    if (currentModTime > lastFileModTime) {
      lastFileModTime = currentModTime;
      return true;
    }
    return false;
  } catch (error) {
    return true; // ファイルが存在しない場合は再読み込み
  }
}

/**
 * キャラクターデータの読み込み
 */
function loadCharactersFromFile(): Record<string, FullCharacterData> {
  if (typeof window !== 'undefined') return {};
  
  try {
    ensureDirectoriesExist();
    
    if (!fs.existsSync(CHARACTERS_FILE)) {
      return {};
    }
    
    const data = fs.readFileSync(CHARACTERS_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error loading characters:', error);
    return {};
  }
}

/**
 * グローバルプロンプトの読み込み
 */
function loadGlobalPrompts(): { ja: string; en: string } {
  if (typeof window !== 'undefined') return { ja: '', en: '' };
  
  try {
    ensureDirectoriesExist();
    
    if (!fs.existsSync(GLOBAL_PROMPTS_FILE)) {
      // デフォルトプロンプトを作成
      const defaultPrompts = {
        ja: 'あなたは親しみやすいAIキャラクターです。ユーザーとの会話を楽しんでください。',
        en: 'You are a friendly AI character. Enjoy conversations with users.'
      };
      fs.writeFileSync(GLOBAL_PROMPTS_FILE, JSON.stringify(defaultPrompts, null, 2));
      return defaultPrompts;
    }
    
    const data = fs.readFileSync(GLOBAL_PROMPTS_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error loading global prompts:', error);
    return { ja: '', en: '' };
  }
}

/**
 * キャッシュの初期化・更新
 */
function initializeCache(): void {
  if (typeof window !== 'undefined') return;
  
  if (!cacheInitialized || checkFileChanges()) {
    charactersCache = loadCharactersFromFile();
    globalPromptsCache = loadGlobalPrompts();
    cacheInitialized = true;
    console.log(`CharacterService: Loaded ${Object.keys(charactersCache).length} characters`);
  }
}

/**
 * 統一キャラクター管理サービス
 */
export class CharacterService {
  /**
   * キャラクター取得（ID指定）
   */
  static getCharacter(characterId: string): FullCharacterData | null {
    initializeCache();
    return charactersCache[characterId] || null;
  }
  
  /**
   * 全アクティブキャラクター取得
   */
  static getAllActiveCharacters(): FullCharacterData[] {
    initializeCache();
    return Object.values(charactersCache)
      .filter(char => char.isActive)
      .sort((a, b) => (a.sortOrder || 999) - (b.sortOrder || 999));
  }
  
  /**
   * キャラクター保存
   */
  static saveCharacter(character: FullCharacterData): boolean {
    if (typeof window !== 'undefined') return false;
    
    try {
      ensureDirectoriesExist();
      
      // キャッシュ更新
      charactersCache[character.id] = {
        ...character,
        updatedAt: new Date().toISOString()
      };
      
      // ファイル保存
      fs.writeFileSync(CHARACTERS_FILE, JSON.stringify(charactersCache, null, 2));
      return true;
    } catch (error) {
      console.error('Error saving character:', error);
      return false;
    }
  }
  
  /**
   * キャラクター削除（非アクティブ化）
   */
  static deleteCharacter(characterId: string): boolean {
    if (typeof window !== 'undefined') return false;
    
    try {
      const character = charactersCache[characterId];
      if (!character) return false;
      
      // 非アクティブ化
      character.isActive = false;
      character.updatedAt = new Date().toISOString();
      
      return this.saveCharacter(character);
    } catch (error) {
      console.error('Error deleting character:', error);
      return false;
    }
  }
  
  /**
   * セキュアなメッセージ配列生成
   */
  static buildSecureMessages(
    characterId: string,
    conversationHistory: Array<{role: string, content: string}>,
    userMessage: string,
    options: {
      language?: 'ja' | 'en';
      userName?: string;
    } = {}
  ): ChatMessage[] {
    const language = options.language || 'en';
    
    // キャラクター取得
    const character = this.getCharacter(characterId);
    if (!character) {
      throw new Error(`Character ${characterId} not found`);
    }
    
    // プロンプト取得
    initializeCache();
    const globalPrompt = globalPromptsCache[language];
    const characterPrompt = character.systemPrompt?.[language] || character.systemPrompt?.en || '';
    
    // プロンプト検証
    if (!globalPrompt || !characterPrompt) {
      throw new Error(`Missing prompts for character ${characterId} (${language})`);
    }
    
    validateRequiredPrompts(globalPrompt, characterPrompt);
    
    // セキュアメッセージ生成
    return buildSecureMessagesArray(
      globalPrompt,
      characterPrompt,
      conversationHistory,
      userMessage,
      {
        userName: options.userName,
        language,
        characterName: character.name[language] || character.name.en
      }
    );
  }
  
  /**
   * キャッシュクリア
   */
  static clearCache(): void {
    charactersCache = {};
    globalPromptsCache = { ja: '', en: '' };
    cacheInitialized = false;
    lastFileModTime = 0;
  }
  
  /**
   * 強制リロード
   */
  static forceReload(): boolean {
    try {
      this.clearCache();
      initializeCache();
      return true;
    } catch (error) {
      console.error('Error force reloading:', error);
      return false;
    }
  }
}

/**
 * プロンプト統合ユーティリティ
 */
export function integratePromptsToCharacters(): boolean {
  if (typeof window !== 'undefined') return false;

  try {
    const promptsDir = path.join(process.cwd(), 'data', 'prompts');

    // 現在のキャラクターデータを読み込み
    initializeCache();
    const characters = { ...charactersCache };

    // 各キャラクターにプロンプトを統合
    for (const characterId in characters) {
      const character = characters[characterId];

      // systemPromptフィールドを初期化
      if (!character.systemPrompt) {
        character.systemPrompt = { en: '', ja: '' };
      }

      // 英語プロンプトを読み込み
      const enPromptFile = path.join(promptsDir, `${characterId}-en.txt`);
      if (fs.existsSync(enPromptFile)) {
        character.systemPrompt.en = fs.readFileSync(enPromptFile, 'utf-8').trim();
        console.log(`✅ Added EN prompt for ${characterId} (${character.systemPrompt.en.length} chars)`);
      }

      // 日本語プロンプトを読み込み
      const jaPromptFile = path.join(promptsDir, `${characterId}-ja.txt`);
      if (fs.existsSync(jaPromptFile)) {
        character.systemPrompt.ja = fs.readFileSync(jaPromptFile, 'utf-8').trim();
        console.log(`✅ Added JA prompt for ${characterId} (${character.systemPrompt.ja.length} chars)`);
      }

      // レガシーファイルも確認
      const legacyPromptFile = path.join(promptsDir, `${characterId}.txt`);
      if (fs.existsSync(legacyPromptFile) && !character.systemPrompt.en && !character.systemPrompt.ja) {
        const legacyPrompt = fs.readFileSync(legacyPromptFile, 'utf-8').trim();
        character.systemPrompt.en = legacyPrompt;
        character.systemPrompt.ja = legacyPrompt;
        console.log(`✅ Added legacy prompt for ${characterId} (${legacyPrompt.length} chars)`);
      }
    }

    // 統合されたデータを保存
    fs.writeFileSync(CHARACTERS_FILE, JSON.stringify(characters, null, 2));

    // キャッシュを更新
    charactersCache = characters;

    console.log('🎯 プロンプト統合完了');
    return true;
  } catch (error) {
    console.error('❌ プロンプト統合エラー:', error);
    return false;
  }
}

/**
 * レガシー互換性のためのエクスポート
 */
export const getCharacterData = (characterId: string) => CharacterService.getCharacter(characterId);
export const getAllActiveCharacters = () => CharacterService.getAllActiveCharacters();
export const forceReloadCharacters = () => CharacterService.forceReload();
export const clearCharactersCache = () => CharacterService.clearCache();
